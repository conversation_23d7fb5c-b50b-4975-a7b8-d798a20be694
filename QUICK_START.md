# Business Owner Scraper - Quick Start Guide

## 🚀 Installation

### Option 1: Automated Installation (Recommended)
```bash
python install.py
```

### Option 2: Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Create directories
mkdir -p logs results backups examples

# Copy environment file
cp .env.example .env
```

## 🎯 Quick Usage

### 1. Basic Scraping
```bash
# Scrape lawn care businesses in Dallas
python main.py --business-type "lawn care" --location "dallas tx"

# Multiple business types and locations
python main.py -b "lawn care" -b "restaurant" -l "dallas tx" -l "houston tx"
```

### 2. Interactive Mode (Easiest)
```bash
python main.py --interactive
```
Follow the prompts to configure your scraping job.

### 3. Specific Sources
```bash
# Use only BBB and Manta
python main.py -b "construction" -l "austin tx" -s bbb -s manta

# Export to Excel
python main.py -b "plumbing" -l "san antonio tx" --format xlsx
```

### 4. Finder-Compatible Export
```bash
# Export in format compatible with finder application
python main.py -b "electrical" -l "dallas tx" --format finder
```

## 📊 Output Files

Results are saved to the `./results/` directory:

- **CSV Format**: `business_owners_YYYYMMDD_HHMMSS.csv`
- **Excel Format**: `business_owners_YYYYMMDD_HHMMSS.xlsx` (with multiple sheets)
- **Finder Format**: `finder_import_YYYYMMDD_HHMMSS.xlsx`

## 🔧 Configuration

Edit `config.yaml` to customize:

```yaml
# Enable/disable sources
sources:
  bbb:
    enabled: true
    max_pages: 5
  manta:
    enabled: true
    max_pages: 5
  linkedin:
    enabled: true
    max_pages: 3

# Anti-bot settings
anti_bot:
  use_proxies: true
  rotate_user_agents: true
  use_cloudscraper: true

# Proxy configuration (optional)
proxies:
  http_proxies:
    - "http://username:<EMAIL>:8080"
```

## 🧪 Testing

```bash
# Run all tests
python run_tests.py

# Run examples
python examples/example_usage.py
```

## 📋 Data Sources

| Source | Protection Level | Data Quality | Notes |
|--------|-----------------|--------------|-------|
| BBB.org | Low | High | Business profiles with owner info |
| Manta.com | Medium | High | Comprehensive business directory |
| LinkedIn.com | High | Medium | Uses search engine results |
| TruePeopleSearch.com | High | Medium | Cloudflare protected |
| CyberBackgroundChecks.com | High | Medium | Cloudflare protected |

## ⚡ Performance Tips

1. **Use Proxies**: Configure proxies for better success rates
2. **Adjust Delays**: Increase `request_delay` if getting blocked
3. **Limit Pages**: Reduce `max_pages` for faster results
4. **Enable Deduplication**: Removes duplicate entries automatically

## 🛠️ Troubleshooting

### Common Issues

**No results found:**
- Check business type spelling
- Try different locations
- Verify sources are enabled

**Getting blocked:**
- Enable proxy rotation
- Increase request delays
- Use undetected Chrome driver

**Export errors:**
- Check output directory permissions
- Ensure file isn't open in another program

### Logs

Check `logs/scraper.log` for detailed error information.

## 📞 Support

1. Check the troubleshooting section above
2. Review logs for error details
3. See full documentation in `README.md`

## ⚖️ Legal Notice

This tool is for educational and research purposes. Users must:
- Respect website terms of service
- Use appropriate rate limiting
- Comply with applicable laws
- Use data responsibly

---

**Ready to start scraping? Run:**
```bash
python main.py --interactive
```
