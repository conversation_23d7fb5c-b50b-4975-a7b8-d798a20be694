#!/usr/bin/env python3
"""
Test Playwright API - Demonstrates real browser API calls that bypass Cloudflare.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_playwright_apis():
    """Test both TruthFinder and Manta using Playwright browser automation."""
    print("🎭 Playwright API Test - Real Browser Automation")
    print("=" * 60)
    print("🎯 Using real browser context to bypass Cloudflare and anti-bot protection")
    print("=" * 60)
    
    try:
        from src.utils.playwright_api import PlaywrightAPIClient
        
        # Test scenarios
        test_scenarios = [
            {
                'type': 'truthfinder',
                'params': {
                    'first_name': '<PERSON>',
                    'last_name': 'Smith',
                    'city': 'Houston',
                    'state': 'TX'
                },
                'description': 'TruthFinder person search'
            },
            {
                'type': 'manta',
                'params': {
                    'business_type': 'restaurant',
                    'location': 'houston tx'
                },
                'description': 'Manta business search'
            }
        ]
        
        all_results = []
        
        # Test each scenario
        for scenario_idx, scenario in enumerate(test_scenarios, 1):
            scenario_type = scenario['type']
            params = scenario['params']
            description = scenario['description']
            
            print(f"\n🧪 BROWSER TEST {scenario_idx}/2: {description}")
            print(f"   Type: {scenario_type}")
            print(f"   Params: {params}")
            print("-" * 50)
            
            try:
                # Create Playwright client
                async with PlaywrightAPIClient(headless=False) as client:  # Set headless=False to see browser
                    print(f"🚀 Starting browser for {scenario_type}...")
                    
                    if scenario_type == 'truthfinder':
                        results = await client.truthfinder_search(**params)
                    else:  # manta
                        results = await client.manta_search(**params)
                    
                    print(f"✅ {scenario_type.title()} browser search completed!")
                    print(f"📊 Results found: {len(results)}")
                    
                    if results:
                        # Display sample results
                        print(f"\n📋 Sample Results from {scenario_type.title()}:")
                        for i, result in enumerate(results[:3], 1):  # Show first 3 results
                            print(f"   Result {i}:")
                            for key, value in result.items():
                                if key not in ['timestamp', 'source'] and value:
                                    print(f"      {key}: {value}")
                            print()
                        
                        all_results.extend(results)
                    else:
                        print(f"⚠️  No results found for {scenario_type}")
                
            except Exception as e:
                print(f"❌ {scenario_type.title()} browser test failed: {e}")
                continue
        
        print(f"\n🎯 PLAYWRIGHT API SUMMARY")
        print("=" * 40)
        print(f"📊 Total Browser Results: {len(all_results)}")
        
        if all_results:
            # Analyze results by source
            source_counts = {}
            for result in all_results:
                source = result.get('source', 'unknown')
                source_counts[source] = source_counts.get(source, 0) + 1
            
            print(f"\n📡 Results by Source:")
            for source, count in source_counts.items():
                print(f"   {source}: {count} results")
            
            # Save results to file
            import json
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"playwright_api_results_{timestamp}.json"
            
            with open(results_file, 'w') as f:
                json.dump(all_results, f, indent=2)
            
            print(f"\n📁 Results saved to: {results_file}")
            
            # Show data quality
            truthfinder_results = [r for r in all_results if 'truthfinder' in r.get('source', '')]
            manta_results = [r for r in all_results if 'manta' in r.get('source', '')]
            
            print(f"\n📈 Data Quality Analysis:")
            if truthfinder_results:
                print(f"   TruthFinder: {len(truthfinder_results)} results")
                names_found = len([r for r in truthfinder_results if r.get('name')])
                locations_found = len([r for r in truthfinder_results if r.get('location')])
                ages_found = len([r for r in truthfinder_results if r.get('age')])
                print(f"      Names: {names_found}, Locations: {locations_found}, Ages: {ages_found}")
            
            if manta_results:
                print(f"   Manta: {len(manta_results)} results")
                businesses_found = len([r for r in manta_results if r.get('business_name')])
                phones_found = len([r for r in manta_results if r.get('phone')])
                emails_found = len([r for r in manta_results if r.get('email')])
                websites_found = len([r for r in manta_results if r.get('website')])
                print(f"      Businesses: {businesses_found}, Phones: {phones_found}, Emails: {emails_found}, Websites: {websites_found}")
            
            print(f"\n🎉 Playwright API test completed successfully!")
            print(f"🎭 Real browser automation bypassed anti-bot protection!")
            print(f"💡 This approach provides the most reliable data extraction.")
            
        else:
            print("⚠️  No results found from browser automation")
            print("💡 This could be due to:")
            print("   - Website structure changes")
            print("   - Network connectivity issues")
            print("   - Search terms not returning results")
            print("   - Temporary website issues")
        
    except ImportError:
        print("❌ Playwright not installed. Run:")
        print("   pip install playwright")
        print("   playwright install")
    except Exception as e:
        print(f"❌ Playwright API test failed: {e}")
        import traceback
        traceback.print_exc()

def test_sync_wrappers():
    """Test the sync wrapper functions."""
    print("\n🔄 Testing Sync Wrapper Functions")
    print("=" * 40)
    
    try:
        from src.utils.playwright_api import search_truthfinder, search_manta
        
        print("🔍 Testing TruthFinder sync wrapper...")
        tf_results = search_truthfinder("John", "Smith", "Houston", "TX")
        print(f"✅ TruthFinder sync: {len(tf_results)} results")
        
        print("🏢 Testing Manta sync wrapper...")
        manta_results = search_manta("restaurant", "houston tx")
        print(f"✅ Manta sync: {len(manta_results)} results")
        
        print(f"🎯 Sync wrappers work! Total: {len(tf_results) + len(manta_results)} results")
        
    except Exception as e:
        print(f"❌ Sync wrapper test failed: {e}")

async def main():
    """Main test function."""
    await test_playwright_apis()
    
    # Test sync wrappers
    test_sync_wrappers()

if __name__ == '__main__':
    # Set up logging
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    asyncio.run(main())
