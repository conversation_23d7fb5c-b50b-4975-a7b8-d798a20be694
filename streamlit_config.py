#!/usr/bin/env python3
"""
Business Finder System - Streamlit Configuration Helper
Manages configuration settings and system integration for the web interface.
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional

class StreamlitConfig:
    """Configuration manager for Streamlit application"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        if not self.config_path.exists():
            return self._create_default_config()
        
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration"""
        return {
            'scrapers': {
                'manta_browser': {
                    'enabled': True,
                    'priority': 1,
                    'max_pages': 3,
                    'scraper_api': {
                        'enabled': True,
                        'api_key': '',
                        'use_premium': False,
                        'country_code': 'US'
                    }
                },
                'truthfinder_browser': {
                    'enabled': True,
                    'priority': 2,
                    'cookie_refresh': True
                },
                'bbb': {
                    'enabled': False,
                    'priority': 3
                },
                'manta': {
                    'enabled': False,
                    'priority': 4
                },
                'cyberbackgroundchecks': {
                    'enabled': False,
                    'priority': 5
                }
            },
            'export': {
                'default_format': 'excel',
                'include_metadata': True,
                'max_results': 1000
            },
            'system': {
                'verbose_logging': True,
                'timeout_seconds': 300,
                'max_concurrent_searches': 3
            }
        }
    
    def get_scraper_config(self, scraper_name: str) -> Dict[str, Any]:
        """Get configuration for a specific scraper"""
        return self.config.get('scrapers', {}).get(scraper_name, {})
    
    def is_scraper_enabled(self, scraper_name: str) -> bool:
        """Check if a scraper is enabled"""
        return self.get_scraper_config(scraper_name).get('enabled', False)
    
    def get_enabled_scrapers(self) -> list:
        """Get list of enabled scrapers"""
        enabled = []
        for name, config in self.config.get('scrapers', {}).items():
            if config.get('enabled', False):
                enabled.append(name)
        return enabled
    
    def get_scraper_priority(self, scraper_name: str) -> int:
        """Get priority for a scraper"""
        return self.get_scraper_config(scraper_name).get('priority', 999)
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for a service"""
        if service == 'scraperapi':
            return (self.config.get('scrapers', {})
                   .get('manta_browser', {})
                   .get('scraper_api', {})
                   .get('api_key'))
        return None
    
    def mask_api_key(self, api_key: str) -> str:
        """Mask API key for display"""
        if not api_key or len(api_key) < 8:
            return "Not configured"
        return f"{api_key[:4]}...{api_key[-4:]}"
    
    def get_export_config(self) -> Dict[str, Any]:
        """Get export configuration"""
        return self.config.get('export', {})
    
    def get_system_config(self) -> Dict[str, Any]:
        """Get system configuration"""
        return self.config.get('system', {})
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        try:
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def update_scraper_status(self, scraper_name: str, enabled: bool):
        """Update scraper enabled status"""
        if 'scrapers' not in self.config:
            self.config['scrapers'] = {}
        if scraper_name not in self.config['scrapers']:
            self.config['scrapers'][scraper_name] = {}
        
        self.config['scrapers'][scraper_name]['enabled'] = enabled
    
    def validate_config(self) -> Dict[str, list]:
        """Validate configuration and return issues"""
        issues = {
            'errors': [],
            'warnings': [],
            'info': []
        }
        
        # Check if any scrapers are enabled
        enabled_scrapers = self.get_enabled_scrapers()
        if not enabled_scrapers:
            issues['warnings'].append("No scrapers are enabled")
        else:
            issues['info'].append(f"Enabled scrapers: {', '.join(enabled_scrapers)}")
        
        # Check ScraperAPI configuration
        if self.is_scraper_enabled('manta_browser'):
            api_key = self.get_api_key('scraperapi')
            if not api_key:
                issues['errors'].append("Manta Browser is enabled but ScraperAPI key is missing")
            else:
                issues['info'].append("ScraperAPI key is configured")
        
        # Check for conflicting configurations
        if (self.is_scraper_enabled('manta') and 
            self.is_scraper_enabled('manta_browser')):
            issues['warnings'].append("Both Manta and Manta Browser are enabled (redundant)")
        
        return issues

class StreamlitState:
    """Manages Streamlit session state"""
    
    @staticmethod
    def initialize_state():
        """Initialize session state variables"""
        import streamlit as st
        
        if 'config' not in st.session_state:
            st.session_state.config = StreamlitConfig()
        
        if 'search_results' not in st.session_state:
            st.session_state.search_results = None
        
        if 'search_history' not in st.session_state:
            st.session_state.search_history = []
        
        if 'system_stats' not in st.session_state:
            st.session_state.system_stats = {
                'total_searches': 0,
                'total_results': 0,
                'total_cost': 0.0,
                'avg_processing_time': 0.0,
                'last_search_time': None
            }
        
        if 'current_search' not in st.session_state:
            st.session_state.current_search = {
                'running': False,
                'progress': 0,
                'status': '',
                'start_time': None
            }
    
    @staticmethod
    def update_search_stats(results_count: int, cost: float, processing_time: float):
        """Update system statistics"""
        import streamlit as st
        
        stats = st.session_state.system_stats
        stats['total_searches'] += 1
        stats['total_results'] += results_count
        stats['total_cost'] += cost
        
        # Update average processing time
        if stats['avg_processing_time'] == 0:
            stats['avg_processing_time'] = processing_time
        else:
            stats['avg_processing_time'] = (
                (stats['avg_processing_time'] * (stats['total_searches'] - 1) + processing_time) /
                stats['total_searches']
            )
        
        stats['last_search_time'] = datetime.now()
    
    @staticmethod
    def add_search_to_history(business_type: str, location: str, sources: list, results_count: int):
        """Add search to history"""
        import streamlit as st
        from datetime import datetime
        
        st.session_state.search_history.append({
            'timestamp': datetime.now(),
            'business_type': business_type,
            'location': location,
            'sources': sources,
            'results_count': results_count
        })
        
        # Keep only last 100 searches
        if len(st.session_state.search_history) > 100:
            st.session_state.search_history = st.session_state.search_history[-100:]
    
    @staticmethod
    def clear_search_results():
        """Clear current search results"""
        import streamlit as st
        st.session_state.search_results = None
    
    @staticmethod
    def get_search_summary() -> Dict[str, Any]:
        """Get summary of current search results"""
        import streamlit as st
        
        if not st.session_state.search_results:
            return {}
        
        data = st.session_state.search_results['data']
        metadata = st.session_state.search_results['metadata']
        
        return {
            'total_results': len(data),
            'business_type': metadata.get('business_type', 'Unknown'),
            'location': metadata.get('location', 'Unknown'),
            'sources': metadata.get('sources', []),
            'timestamp': metadata.get('timestamp'),
            'data_quality': {
                'business_names': (data['business_name'].notna().sum() / len(data)) * 100 if 'business_name' in data.columns else 0,
                'addresses': (data['address'].notna().sum() / len(data)) * 100 if 'address' in data.columns else 0,
                'phones': (data['phone'].notna().sum() / len(data)) * 100 if 'phone' in data.columns else 0,
            }
        }

# Global configuration instance
config = StreamlitConfig()

# Export commonly used functions
def get_config():
    """Get global configuration instance"""
    return config

def initialize_streamlit_state():
    """Initialize Streamlit session state"""
    StreamlitState.initialize_state()
