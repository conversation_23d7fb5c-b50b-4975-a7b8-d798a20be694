#!/usr/bin/env python3
"""
Business Finder System - Streamlit Web Application
A comprehensive frontend for the Business Finder data scraping and enrichment system.
"""

import streamlit as st

# Page configuration - MUST be first Streamlit command
st.set_page_config(
    page_title="Business Finder System",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import os
import sys
import subprocess
import threading
import time
import queue
import io
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(str(Path(__file__).parent / 'src'))

# Import the main scraping system with graceful fallbacks
DEMO_MODE = False
BusinessOwnerScraper = None
DataProcessor = None
ScrapingResult = None

# Initialize status messages container
status_container = st.container()

try:
    # Try to import the main system components
    import sys
    from pathlib import Path

    # Add src to path if not already there
    src_path = Path(__file__).parent / 'src'
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))

    # Import with fallback handling
    try:
        from main import BusinessOwnerScraper
    except ImportError as e:
        BusinessOwnerScraper = None
        DEMO_MODE = True
        with status_container:
            st.warning("⚠️ Main scraping system not available - running in demo mode")
            st.info(f"Import error: {e}")

    try:
        from src.utils.data_processor import DataProcessor
        from src.core import ScrapingResult
    except ImportError as e:
        DataProcessor = None
        ScrapingResult = None
        DEMO_MODE = True
        with status_container:
            st.info("ℹ️ Some system components not available - using demo data")
            st.info(f"Import error: {e}")

except Exception as e:
    DEMO_MODE = True
    with status_container:
        st.error(f"System initialization error: {e}")
        st.info("🎬 Running in demo mode with sample data")

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    .status-working { background-color: #28a745; }
    .status-limited { background-color: #ffc107; }
    .status-blocked { background-color: #dc3545; }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .progress-container {
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Load demo data if available
def load_demo_data():
    """Load demo data if available"""
    try:
        import json
        demo_config_file = Path('demo_config.json')
        if demo_config_file.exists():
            with open(demo_config_file, 'r') as f:
                return json.load(f)
    except:
        pass
    return None

# Initialize session state
if 'search_results' not in st.session_state:
    st.session_state.search_results = None
if 'search_history' not in st.session_state:
    # Load demo search history if available
    demo_data = load_demo_data()
    if demo_data and 'sample_data' in demo_data:
        st.session_state.search_history = demo_data['sample_data'].get('search_history', [])
    else:
        st.session_state.search_history = []
if 'system_stats' not in st.session_state:
    # Load demo system stats if available
    demo_data = load_demo_data()
    if demo_data and 'system_stats' in demo_data:
        st.session_state.system_stats = demo_data['system_stats']
    else:
        st.session_state.system_stats = {
            'total_searches': 0,
            'total_results': 0,
            'total_cost': 0.0,
            'avg_processing_time': 0.0
        }

# Fix any existing search history with string timestamps
if st.session_state.search_history:
    for item in st.session_state.search_history:
        if 'timestamp' in item and isinstance(item['timestamp'], str):
            try:
                item['timestamp'] = pd.to_datetime(item['timestamp'])
            except:
                item['timestamp'] = datetime.now()

# Data source configuration with status indicators
DATA_SOURCES = {
    'manta_browser': {
        'name': 'Manta Browser (ScraperAPI)',
        'status': 'working',
        'description': '230 business listings per search, 100% success rate',
        'cost_per_search': 0.003,
        'avg_response_time': '6-8 seconds'
    },
    'truthfinder_browser': {
        'name': 'TruthFinder Browser',
        'status': 'working',
        'description': '131 personal records with demographics',
        'cost_per_search': 0.0,
        'avg_response_time': '44 seconds'
    },
    'bbb': {
        'name': 'Better Business Bureau',
        'status': 'limited',
        'description': 'Limited functionality, requires Google API',
        'cost_per_search': 0.0,
        'avg_response_time': 'N/A'
    },
    'manta': {
        'name': 'Traditional Manta',
        'status': 'limited',
        'description': 'Superseded by Manta Browser',
        'cost_per_search': 0.0,
        'avg_response_time': 'N/A'
    },
    'cyberbackgroundchecks': {
        'name': 'CyberBackgroundChecks',
        'status': 'blocked',
        'description': 'Blocked by Cloudflare protection',
        'cost_per_search': 0.0,
        'avg_response_time': 'Timeout'
    }
}

def get_status_indicator(status):
    """Return HTML for status indicator"""
    status_class = f"status-{status}"
    return f'<span class="{status_class} status-indicator"></span>'

def format_currency(amount):
    """Format currency values"""
    return f"${amount:.4f}" if amount < 0.01 else f"${amount:.2f}"

def main():
    """Main application function"""
    
    # Header
    st.markdown('<h1 class="main-header">🔍 Business Finder System</h1>', unsafe_allow_html=True)
    st.markdown("**Comprehensive Business Intelligence & Data Enrichment Platform**")
    
    # Sidebar for navigation
    st.sidebar.title("Navigation")
    tab_selection = st.sidebar.radio(
        "Select Section:",
        ["🔍 Search Configuration", "📊 Results & Analytics", "📈 Usage Dashboard"]
    )

    # Main content based on tab selection
    if tab_selection == "🔍 Search Configuration":
        search_configuration_tab()
    elif tab_selection == "📊 Results & Analytics":
        results_analytics_tab()
    elif tab_selection == "📈 Usage Dashboard":
        usage_dashboard_tab()

def search_configuration_tab():
    """Search configuration interface"""
    st.header("🔍 Search Configuration")

    # Search Parameters in a clean container
    with st.container():
        st.subheader("📋 Search Parameters")

        col1, col2 = st.columns(2)

        with col1:
            # Business type input
            business_type = st.text_input(
                "Business Type",
                value="restaurant",
                help="Enter the type of business to search for (e.g., restaurant, construction, law firm)"
            )

        with col2:
            # Location input
            location = st.text_input(
                "Location",
                value="houston tx",
                help="Enter the location to search in (e.g., houston tx, miami fl, dallas tx)"
            )

    # Data sources selection - now collapsible
    with st.expander("🔍 Data Sources Configuration", expanded=True):
        st.markdown("Select which data sources to use for your search:")

        selected_sources = []
        for source_key, source_info in DATA_SOURCES.items():
            col_check, col_info = st.columns([1, 4])

            with col_check:
                if st.checkbox(
                    source_info['name'],
                    value=source_info['status'] == 'working',
                    disabled=source_info['status'] == 'blocked',
                    key=f"source_{source_key}"
                ):
                    selected_sources.append(source_key)

            with col_info:
                status_html = get_status_indicator(source_info['status'])
                st.markdown(
                    f"{status_html} {source_info['description']} "
                    f"(Cost: {format_currency(source_info['cost_per_search'])}, "
                    f"Response: {source_info['avg_response_time']})",
                    unsafe_allow_html=True
                )

    # Configuration options in a clean layout
    st.markdown("---")

    col1, col2 = st.columns(2)

    with col1:
        # Export format selection
        export_format = st.selectbox(
            "📁 Export Format",
            ["excel", "csv"],
            help="Choose the format for exporting results"
        )

    with col2:
        # Quick settings
        verbose_logging = st.checkbox(
            "🔍 Enable Verbose Logging",
            value=False,
            help="Show detailed logging information during scraping"
        )

    # Advanced options
    with st.expander("⚙️ Advanced Options"):
        max_pages = st.slider(
            "Maximum Pages per Source",
            min_value=1,
            max_value=5,
            value=3,
            help="Limit the number of pages to scrape per source"
        )

        st.info("💡 **Tip:** Start with 1-2 pages for faster results, increase for more comprehensive data.")

    # Search execution section
    st.markdown("---")
    st.subheader("🚀 Execute Search")

    # Show selected sources summary
    if selected_sources:
        st.success(f"✅ {len(selected_sources)} data source(s) selected: {', '.join([DATA_SOURCES[s]['name'] for s in selected_sources])}")
    else:
        st.warning("⚠️ No data sources selected. Please select at least one source above.")

    col_search, col_clear = st.columns([3, 1])

    with col_search:
        search_disabled = not business_type or not location or not selected_sources
        if st.button("🚀 Start Search", type="primary", use_container_width=True, disabled=search_disabled):
            if not business_type or not location:
                st.error("Please enter both business type and location.")
            elif not selected_sources:
                st.error("Please select at least one data source.")
            else:
                if DEMO_MODE or BusinessOwnerScraper is None:
                    execute_demo_search(business_type, location, selected_sources, export_format)
                else:
                    execute_real_search(business_type, location, selected_sources, export_format, verbose_logging)

    with col_clear:
        if st.button("🗑️ Clear Results", use_container_width=True):
            st.session_state.search_results = None
            st.rerun()

def execute_demo_search(business_type, location, sources, export_format):
    """Execute a demo search with sample data"""

    # Show demo message
    st.info("🎬 Demo Mode: Simulating search with sample data...")

    # Create progress indicators
    progress_bar = st.progress(0)
    status_text = st.empty()

    # Simulate search progress
    import time

    status_text.text("Initializing demo search...")
    progress_bar.progress(20)
    time.sleep(1)

    status_text.text("Loading sample business data...")
    progress_bar.progress(50)
    time.sleep(1)

    status_text.text("Processing and enriching demo data...")
    progress_bar.progress(80)
    time.sleep(1)

    # Load demo data
    demo_data = load_demo_data()
    if demo_data and 'sample_data' in demo_data:
        try:
            # Load the demo Excel file
            excel_file = demo_data['sample_data']['excel_file']
            if Path(excel_file).exists():
                df = pd.read_excel(excel_file)

                # Store results in session state
                st.session_state.search_results = {
                    'data': df,
                    'metadata': {
                        'business_type': business_type,
                        'location': location,
                        'sources': sources,
                        'total_results': len(df),
                        'timestamp': datetime.now(),
                        'output_file': excel_file,
                        'demo_mode': True
                    }
                }

                progress_bar.progress(100)
                status_text.text("✅ Demo search completed successfully!")

                st.success(f"Demo search completed! Found {len(df)} sample business records.")
                st.info("💡 This is sample data for demonstration. In production mode, this would be real scraped data.")

                return

        except Exception as e:
            st.error(f"Error loading demo data: {e}")

    # Fallback: create simple demo data
    demo_df = pd.DataFrame([
        {
            'business_name': f'Sample {business_type.title()} {i+1}',
            'owner_name': f'Owner {i+1}',
            'business_type': business_type,
            'location': location,
            'address': f'{100+i*10} Sample St, {location.title()}',
            'phone': f'(555) 555-{1000+i:04d}',
            'source': sources[0] if sources else 'demo',
            'confidence_score': 0.8,
            'data_quality': 'demo'
        }
        for i in range(10)
    ])

    st.session_state.search_results = {
        'data': demo_df,
        'metadata': {
            'business_type': business_type,
            'location': location,
            'sources': sources,
            'total_results': len(demo_df),
            'timestamp': datetime.now(),
            'demo_mode': True
        }
    }

    progress_bar.progress(100)
    status_text.text("✅ Demo search completed!")
    st.success(f"Demo search completed! Generated {len(demo_df)} sample records.")
    st.info("💡 This is generated sample data for demonstration purposes.")

def execute_real_search(business_type, location, sources, export_format, verbose):
    """Execute the search using the real BusinessOwnerScraper"""

    # Create progress indicators
    progress_bar = st.progress(0)
    status_text = st.empty()
    log_container = st.empty()

    # Initialize search
    status_text.text("Initializing real-time search...")
    progress_bar.progress(10)

    try:
        # Initialize the scraper
        scraper = BusinessOwnerScraper()

        status_text.text("Scraper initialized, starting search...")
        progress_bar.progress(20)

        # Convert sources to the format expected by the scraper
        source_list = sources if sources else None

        # Execute search
        status_text.text("Scraping data sources...")
        progress_bar.progress(40)

        # Run the scraping
        results = scraper.scrape(
            business_types=[business_type],
            locations=[location],
            sources=source_list
        )

        progress_bar.progress(70)
        status_text.text("Processing and enriching data...")

        if results:
            # Process and export results
            output_file = scraper.process_and_export(results, export_format)

            progress_bar.progress(90)
            status_text.text("Exporting results...")

            if output_file:
                # Load the results file to display in the interface
                try:
                    if output_file.endswith('.xlsx'):
                        df = pd.read_excel(output_file)
                    else:
                        df = pd.read_csv(output_file)

                    # Store results in session state
                    st.session_state.search_results = {
                        'data': df,
                        'metadata': {
                            'business_type': business_type,
                            'location': location,
                            'sources': sources,
                            'total_results': len(df),
                            'timestamp': datetime.now(),
                            'output_file': output_file,
                            'demo_mode': False
                        }
                    }

                    # Update search history
                    st.session_state.search_history.append({
                        'timestamp': datetime.now(),
                        'business_type': business_type,
                        'location': location,
                        'sources': sources,
                        'results_count': len(df)
                    })

                    # Update system stats
                    st.session_state.system_stats['total_searches'] += 1
                    st.session_state.system_stats['total_results'] += len(df)

                    progress_bar.progress(100)
                    status_text.text("✅ Real-time search completed successfully!")

                    st.success(f"Search completed! Found {len(df)} business records.")
                    st.info(f"Results saved to: {output_file}")

                except Exception as e:
                    st.error(f"Error loading results file: {e}")
            else:
                st.error("Error exporting results")
        else:
            progress_bar.progress(100)
            status_text.text("⚠️ No results found")
            st.warning("No results found for the specified search criteria.")

    except Exception as e:
        st.error(f"Error executing real-time search: {str(e)}")
        progress_bar.progress(0)
        status_text.text("❌ Search failed")

        # Show detailed error for debugging
        if verbose:
            st.text_area("Error Details", str(e), height=200)

    finally:
        # Clean up scraper resources
        try:
            if 'scraper' in locals():
                scraper.cleanup()
        except:
            pass

def execute_search(business_type, location, sources, export_format, verbose):
    """Legacy function - redirects to real search"""
    execute_real_search(business_type, location, sources, export_format, verbose)

def parse_search_results(output_lines, business_type, location, sources):
    """Parse search results from command output"""
    
    # Extract key metrics from output
    total_results = 0
    processing_time = 0
    
    for line in output_lines:
        if "Total results:" in line:
            try:
                total_results = int(line.split("Total results:")[1].strip())
            except:
                pass
        elif "Output file:" in line:
            output_file = line.split("Output file:")[1].strip()
            
            # Try to load the results file
            try:
                if output_file.endswith('.xlsx'):
                    df = pd.read_excel(output_file)
                else:
                    df = pd.read_csv(output_file)
                
                # Store results in session state
                st.session_state.search_results = {
                    'data': df,
                    'metadata': {
                        'business_type': business_type,
                        'location': location,
                        'sources': sources,
                        'total_results': len(df),
                        'timestamp': datetime.now(),
                        'output_file': output_file
                    }
                }
                
                # Update search history
                st.session_state.search_history.append({
                    'timestamp': datetime.now(),
                    'business_type': business_type,
                    'location': location,
                    'sources': sources,
                    'results_count': len(df)
                })
                
                # Update system stats
                st.session_state.system_stats['total_searches'] += 1
                st.session_state.system_stats['total_results'] += len(df)
                
            except Exception as e:
                st.error(f"Failed to load results file: {e}")

def results_analytics_tab():
    """Results and analytics display"""
    st.header("📊 Results & Analytics")
    
    if st.session_state.search_results is None:
        st.info("No search results available. Please run a search first.")
        return
    
    results_data = st.session_state.search_results
    df = results_data['data']
    metadata = results_data['metadata']
    
    # Results summary
    st.subheader("Search Summary")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Results", f"{len(df):,}")
    with col2:
        st.metric("Business Type", metadata['business_type'].title())
    with col3:
        st.metric("Location", metadata['location'].title())
    with col4:
        st.metric("Sources Used", len(metadata['sources']))
    
    # Data quality metrics
    st.subheader("Data Quality Metrics")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Calculate completion rates for key fields
        business_name_rate = (df['business_name'].notna().sum() / len(df)) * 100
        st.metric("Business Names", f"{business_name_rate:.1f}%")
    
    with col2:
        address_rate = (df['address'].notna().sum() / len(df)) * 100
        st.metric("Addresses", f"{address_rate:.1f}%")
    
    with col3:
        phone_rate = (df['phone'].notna().sum() / len(df)) * 100
        st.metric("Phone Numbers", f"{phone_rate:.1f}%")
    
    # Source distribution
    if 'source' in df.columns:
        st.subheader("Source Distribution")
        source_counts = df['source'].value_counts()
        
        fig = px.pie(
            values=source_counts.values,
            names=source_counts.index,
            title="Results by Data Source"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # Interactive data table
    st.subheader("Results Data")
    
    # Filtering options
    col1, col2 = st.columns(2)
    
    with col1:
        if 'business_name' in df.columns:
            search_term = st.text_input("Search Business Names", "")
            if search_term:
                df = df[df['business_name'].str.contains(search_term, case=False, na=False)]
    
    with col2:
        if 'city' in df.columns:
            cities = df['city'].dropna().unique()
            selected_city = st.selectbox("Filter by City", ["All"] + list(cities))
            if selected_city != "All":
                df = df[df['city'] == selected_city]
    
    # Display data table
    st.dataframe(
        df,
        use_container_width=True,
        height=400
    )
    
    # Export options
    st.subheader("Export Options")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📥 Download CSV"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download CSV File",
                data=csv,
                file_name=f"business_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    
    with col2:
        if st.button("📥 Download Excel"):
            buffer = io.BytesIO()
            with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
            
            st.download_button(
                label="Download Excel File",
                data=buffer.getvalue(),
                file_name=f"business_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
    
    with col3:
        if st.button("📥 Download JSON"):
            json_data = df.to_json(orient='records', indent=2)
            st.download_button(
                label="Download JSON File",
                data=json_data,
                file_name=f"business_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )


def usage_dashboard_tab():
    """Usage analytics and dashboard"""
    st.header("📈 Usage Dashboard")

    # Overall statistics
    st.subheader("Overall Statistics")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Total Searches",
            st.session_state.system_stats['total_searches'],
            delta=None
        )

    with col2:
        st.metric(
            "Total Results",
            f"{st.session_state.system_stats['total_results']:,}",
            delta=None
        )

    with col3:
        st.metric(
            "Total Cost",
            format_currency(st.session_state.system_stats['total_cost']),
            delta=None
        )

    with col4:
        avg_results = (st.session_state.system_stats['total_results'] /
                      max(st.session_state.system_stats['total_searches'], 1))
        st.metric(
            "Avg Results/Search",
            f"{avg_results:.0f}",
            delta=None
        )

    # Search history
    if st.session_state.search_history:
        st.subheader("Search History")

        # Convert search history to DataFrame
        history_df = pd.DataFrame(st.session_state.search_history)
        history_df['timestamp'] = pd.to_datetime(history_df['timestamp'])

        # Display recent searches
        st.dataframe(
            history_df.sort_values('timestamp', ascending=False).head(10),
            use_container_width=True
        )

        # Usage trends
        st.subheader("Usage Trends")

        # Searches over time
        if len(history_df) > 1:
            daily_searches = history_df.groupby(history_df['timestamp'].dt.date).size()

            fig = px.line(
                x=daily_searches.index,
                y=daily_searches.values,
                title="Daily Search Volume",
                labels={'x': 'Date', 'y': 'Number of Searches'}
            )
            st.plotly_chart(fig, use_container_width=True)

        # Popular business types
        business_type_counts = history_df['business_type'].value_counts()
        if len(business_type_counts) > 0:
            fig = px.bar(
                x=business_type_counts.index,
                y=business_type_counts.values,
                title="Most Searched Business Types",
                labels={'x': 'Business Type', 'y': 'Search Count'}
            )
            st.plotly_chart(fig, use_container_width=True)

        # Popular locations
        location_counts = history_df['location'].value_counts()
        if len(location_counts) > 0:
            fig = px.bar(
                x=location_counts.index,
                y=location_counts.values,
                title="Most Searched Locations",
                labels={'x': 'Location', 'y': 'Search Count'}
            )
            st.plotly_chart(fig, use_container_width=True)

    else:
        st.info("No search history available yet. Run some searches to see analytics.")

    # Cost analysis
    st.subheader("Cost Analysis")

    # Estimated monthly costs based on usage
    if st.session_state.search_history:
        # Convert string timestamps to datetime objects for comparison
        cutoff_date = datetime.now() - timedelta(days=30)
        recent_searches = []

        for h in st.session_state.search_history:
            timestamp = h['timestamp']
            # Convert string timestamp to datetime if needed
            if isinstance(timestamp, str):
                try:
                    timestamp = pd.to_datetime(timestamp)
                except:
                    continue
            elif not isinstance(timestamp, datetime):
                continue

            if timestamp > cutoff_date:
                recent_searches.append(h)

        recent_searches_count = len(recent_searches)

        estimated_monthly_cost = recent_searches_count * 0.003  # Average cost per search

        col1, col2 = st.columns(2)

        with col1:
            st.metric("Searches This Month", recent_searches_count)
            st.metric("Estimated Monthly Cost", format_currency(estimated_monthly_cost))

        with col2:
            # Cost breakdown by source
            cost_breakdown = {
                'Manta Browser (ScraperAPI)': estimated_monthly_cost,
                'TruthFinder Browser': 0.0,
                'Other Sources': 0.0
            }

            fig = px.pie(
                values=list(cost_breakdown.values()),
                names=list(cost_breakdown.keys()),
                title="Cost Breakdown by Source"
            )
            st.plotly_chart(fig, use_container_width=True)

    # Export usage data
    st.subheader("Export Usage Data")

    if st.session_state.search_history:
        if st.button("📥 Download Usage Report"):
            usage_df = pd.DataFrame(st.session_state.search_history)
            csv = usage_df.to_csv(index=False)
            st.download_button(
                label="Download Usage CSV",
                data=csv,
                file_name=f"usage_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

if __name__ == "__main__":
    main()
