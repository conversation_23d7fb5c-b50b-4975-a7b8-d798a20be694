# Session Extraction Requirements
# Install with: pip install -r requirements_session.txt

# Core Playwright for browser automation
playwright>=1.40.0

# Async support
asyncio-mqtt>=0.11.1

# JSON handling
orjson>=3.9.0

# Logging
colorlog>=6.7.0

# HTTP requests (fallback)
httpx>=0.25.0

# Data processing
pandas>=2.0.0

# Existing requirements (if not already installed)
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
selenium>=4.15.0
fake-useragent>=1.4.0
cloudscraper>=1.2.71
undetected-chromedriver>=3.5.0
pyyaml>=6.0.1
openpyxl>=3.1.0
xlsxwriter>=3.1.0
