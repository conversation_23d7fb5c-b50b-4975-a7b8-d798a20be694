#!/usr/bin/env python3
"""
Demo script to showcase the Business Owner Scraper functionality.
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Suppress warnings for demo
import warnings
warnings.filterwarnings("ignore")

def demo_scraping():
    """Demonstrate the scraping functionality."""
    print("🤖 Business Owner Scraper - Demo")
    print("=" * 50)
    
    try:
        # Import modules
        from src.core import ScrapingEngine, ScrapingResult
        from src.scrapers.bbb_scraper import BBBScraper
        from src.scrapers.manta_scraper import MantaScraper
        from src.utils.data_processor import DataProcessor
        from src.exporters.csv_exporter import CSVExporter
        from src.exporters.excel_exporter import ExcelExporter
        
        print("✅ All modules imported successfully")
        
        # Create sample results to demonstrate data processing
        print("\n📊 Creating sample business owner data...")
        
        sample_results = [
            ScrapingResult(
                owner_name="<PERSON>",
                business_name="Smith Lawn Care Services",
                business_type="lawn care",
                location="dallas tx",
                source="bbb",
                phone="(*************",
                email="<EMAIL>",
                address="123 Main St, Dallas, TX 75201",
                scraped_at=datetime.now()
            ),
            ScrapingResult(
                owner_name="john smith",  # Duplicate with different case
                business_name="Smith Lawn Care Inc",
                business_type="lawn care", 
                location="dallas tx",
                source="manta",
                phone="(*************",
                address="123 Main Street, Dallas, TX 75201",
                scraped_at=datetime.now()
            ),
            ScrapingResult(
                owner_name="Sarah Johnson",
                business_name="Johnson's Green Lawn Service",
                business_type="lawn care",
                location="dallas tx", 
                source="bbb",
                phone="(*************",
                email="<EMAIL>",
                scraped_at=datetime.now()
            ),
            ScrapingResult(
                owner_name="Mike Wilson",
                business_name="Wilson Landscaping",
                business_type="lawn care",
                location="dallas tx",
                source="manta",
                phone="(*************",
                scraped_at=datetime.now()
            ),
            ScrapingResult(
                owner_name="Lisa Brown",
                business_name="Brown's Yard Care",
                business_type="lawn care",
                location="dallas tx",
                source="linkedin",
                email="<EMAIL>",
                scraped_at=datetime.now()
            )
        ]
        
        print(f"   Created {len(sample_results)} sample business owner records")
        
        # Process the data
        print("\n🔄 Processing data through deduplication pipeline...")
        
        config = {
            'general': {
                'output_directory': './results'
            },
            'output': {
                'deduplication': {
                    'enabled': True,
                    'similarity_threshold': 0.8,
                    'key_fields': ['owner_name', 'business_name']
                },
                'csv_columns': [
                    'owner_name', 'business_name', 'business_type', 'location',
                    'source', 'phone', 'email', 'address', 'scraped_at'
                ]
            }
        }
        
        processor = DataProcessor(config)
        processed_results = processor.process_results(sample_results)
        
        print(f"   ✅ Processed {len(processed_results)} unique results (removed duplicates)")
        
        # Export to CSV
        print("\n📁 Exporting to CSV format...")
        csv_exporter = CSVExporter(config)
        csv_file = csv_exporter.export(processed_results, "demo_lawn_care_owners.csv")
        print(f"   ✅ CSV exported to: {csv_file}")
        
        # Export to Excel
        print("\n📁 Exporting to Excel format...")
        excel_exporter = ExcelExporter(config)
        excel_file = excel_exporter.export(processed_results, "demo_lawn_care_owners.xlsx")
        print(f"   ✅ Excel exported to: {excel_file}")
        
        # Export finder-compatible format
        print("\n📁 Exporting finder-compatible format...")
        finder_file = excel_exporter.export_finder_compatible(processed_results, "demo_finder_import.xlsx")
        print(f"   ✅ Finder format exported to: {finder_file}")
        
        # Display summary
        print("\n📊 SCRAPING SUMMARY")
        print("=" * 30)
        print(f"Total business owners found: {len(processed_results)}")
        
        # Source breakdown
        source_counts = {}
        for result in processed_results:
            source = result.source or 'Unknown'
            source_counts[source] = source_counts.get(source, 0) + 1
        
        print("\nResults by source:")
        for source, count in sorted(source_counts.items()):
            print(f"   {source}: {count} results")
        
        # Data quality metrics
        results_with_phone = len([r for r in processed_results if r.phone])
        results_with_email = len([r for r in processed_results if r.email])
        results_with_address = len([r for r in processed_results if r.address])
        
        print("\nData quality metrics:")
        print(f"   Results with phone: {results_with_phone}/{len(processed_results)} ({results_with_phone/len(processed_results)*100:.1f}%)")
        print(f"   Results with email: {results_with_email}/{len(processed_results)} ({results_with_email/len(processed_results)*100:.1f}%)")
        print(f"   Results with address: {results_with_address}/{len(processed_results)} ({results_with_address/len(processed_results)*100:.1f}%)")
        
        # Display individual results
        print("\n📋 DETAILED RESULTS")
        print("=" * 30)
        for i, result in enumerate(processed_results, 1):
            print(f"\n{i}. {result.owner_name or 'N/A'}")
            print(f"   Business: {result.business_name or 'N/A'}")
            print(f"   Type: {result.business_type or 'N/A'}")
            print(f"   Location: {result.location or 'N/A'}")
            print(f"   Phone: {result.phone or 'N/A'}")
            print(f"   Email: {result.email or 'N/A'}")
            print(f"   Address: {result.address or 'N/A'}")
            print(f"   Source: {result.source or 'N/A'}")
            if result.raw_data:
                confidence = result.raw_data.get('confidence_score', 0)
                quality = result.raw_data.get('data_quality', 'unknown')
                print(f"   Confidence: {confidence:.2f}")
                print(f"   Quality: {quality}")
        
        print("\n🎉 Demo completed successfully!")
        print("\nOutput files generated:")
        print(f"   📄 CSV: {csv_file}")
        print(f"   📊 Excel: {excel_file}")
        print(f"   🔗 Finder: {finder_file}")
        
        print("\n💡 Next steps:")
        print("   1. Review the generated files in ./results directory")
        print("   2. Configure proxies in config.yaml for live scraping")
        print("   3. Run: python3 main.py --interactive")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    
    # Set up basic logging
    logging.basicConfig(level=logging.WARNING)
    
    success = demo_scraping()
    sys.exit(0 if success else 1)
