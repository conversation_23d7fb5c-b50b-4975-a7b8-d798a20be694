#!/usr/bin/env python3
"""
Direct launcher for Streamlit app
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Set environment variables
os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
os.environ['STREAMLIT_SERVER_ENABLE_CORS'] = 'false'

# Import and run streamlit
try:
    import streamlit.web.cli as stcli
    
    # Set up sys.argv for streamlit
    sys.argv = [
        'streamlit',
        'run',
        'streamlit_app.py',
        '--server.port', '8501',
        '--server.headless', 'true',
        '--browser.gatherUsageStats', 'false'
    ]
    
    print("🚀 Starting Business Finder System Web Interface...")
    print("   URL: http://localhost:8501")
    print("   Press Ctrl+C to stop")
    
    # Launch streamlit
    stcli.main()
    
except KeyboardInterrupt:
    print("\n👋 Shutting down...")
except Exception as e:
    print(f"❌ Error launching Streamlit: {e}")
    sys.exit(1)
