# Business Owner Scraper Configuration

# General Settings
general:
  max_workers: 5
  request_delay: 2 # seconds between requests
  timeout: 30
  max_retries: 3
  output_format: "csv" # csv, xlsx, json
  output_directory: "./results"

# Anti-Bot Protection Settings
anti_bot:
  use_proxies: true
  rotate_user_agents: true
  use_cloudscraper: true
  use_undetected_chrome: true
  headless: true

# Google Custom Search API configuration (recommended for production)
google_custom_search:
  enabled: false # Set to true when API key is configured
  api_key: "" # Your Google Custom Search API key
  search_engine_id: "" # Your Custom Search Engine ID
  rate_limit: 100 # requests per day (free tier limit)
  timeout: 30

# Google search fallback configuration
google_search_fallback:
  enabled: true
  fallback_enabled: false # Disable direct scraping fallback

# Monitoring and Health Checking
monitoring:
  enabled: true
  metrics_retention_hours: 24
  health_check_interval: 300 # 5 minutes
  alert_thresholds:
    error_rate: 0.5 # 50% error rate triggers alert
    success_rate: 0.3 # Below 30% success rate triggers alert
    response_time: 30.0 # Above 30s response time triggers alert

# Fallback System Configuration
fallback_system:
  enabled: true
  rules:
    - primary_scraper: "bbb"
      fallback_scrapers: ["manta", "truthfinder_api"]
      trigger_conditions:
        error_rate: 0.7
        consecutive_failures: 3
        response_time: 30.0
      cooldown_minutes: 15
      max_attempts: 2

    - primary_scraper: "manta"
      fallback_scrapers: ["bbb", "truthfinder_api"]
      trigger_conditions:
        error_rate: 0.8
        consecutive_failures: 5
        response_time: 45.0
      cooldown_minutes: 20
      max_attempts: 2

# Health Checker Configuration
health_checker:
  enabled: true
  check_interval: 300 # 5 minutes

# Alert System Configuration
alerts:
  enabled: true
  channels: ["log", "file"]
  alert_file: "logs/alerts.log"

# Proxy Settings (optional - can be left empty)
proxies:
  http_proxies: []
  # Example:
  # - "http://username:<EMAIL>:8080"
  # - "http://username:<EMAIL>:8080"

  socks_proxies: []
  # Example:
  # - "socks5://username:<EMAIL>:1080"

# Data Sources Configuration
# All sources now use Google search with site: operator as specified by client
sources:
  bbb:
    enabled: true
    base_url: "https://www.bbb.org"
    search_pattern: 'site:bbb.org "Owner" "{business_type}" "{location}"'
    google_search: true
    max_pages: 5
    cloudflare_bypass: true # Enhanced Cloudflare bypass enabled
    cloudflare_protected: true # BBB uses Cloudflare protection
    retry_attempts: 3
    request_delay: 3 # Longer delay for BBB

  manta:
    enabled: true
    base_url: "https://www.manta.com"
    search_pattern: 'site:manta.com "Owner" "{business_type}" "{location}"'
    google_search: true
    max_pages: 5
    cloudflare_bypass: true # Enhanced Cloudflare protection bypass
    cloudflare_protected: true # Manta uses Cloudflare protection
    retry_attempts: 3
    request_delay: 4 # Longer delay for Manta (more aggressive protection)

  linkedin:
    enabled: false # PERMANENTLY DISABLED - Not viable for production
    # Reasons for disabling:
    # - 90%+ blocking rate due to aggressive anti-scraping measures
    # - Requires login for most profile data
    # - Violates LinkedIn Terms of Service
    # - Legal risks for automated access
    # - Low data quality and success rate (<10%)
    base_url: "https://www.linkedin.com"
    search_pattern: 'site:linkedin.com "Owner" "{business_type}" "{location}"'
    google_search: true
    max_pages: 3
    deprecated: true
    replacement: "truthfinder_api"

  truepeoplesearch:
    enabled: false # Replaced by TruthFinder API
    base_url: "https://www.truepeoplesearch.com"
    search_pattern: 'site:truepeoplesearch.com "Owner" {business_type} {location}'
    max_pages: 3
    cloudflare_protected: true

  truthfinder_api:
    enabled: false # Disabled - use truthfinder_browser instead
    api_key: "B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98" # From provided documentation
    app_id: "tf-web"
    base_url: "https://api.truthfinder.com"
    rate_limit: 100 # requests per minute
    timeout: 30
    priority: 1 # High priority source
    max_results: 10 # Limit results per search

  # TruthFinder Browser API (RECOMMENDED - bypasses Cloudflare)
  truthfinder_browser:
    enabled: true
    priority: 1 # High priority source
    max_searches: 3 # Limit browser instances to avoid resource usage
    headless: true # Set to false for debugging

  # Manta Internal API Configuration
  manta_api:
    enabled: false # Disabled - use manta_browser instead
    base_url: "https://www.manta.com"
    rate_limit_delay: 2 # seconds between requests
    max_retries: 3
    max_pages: 5 # maximum pages to scrape per search
    timeout: 30
    priority: 2 # High priority source

  # Manta Browser API (RECOMMENDED - bypasses Cloudflare)
  manta_browser:
    enabled: true
    priority: 1 # High priority source for business data enrichment
    max_pages: 3 # Limit pages to avoid resource usage
    headless: true # Set to false for debugging

    # Data enrichment configuration
    enrichment:
      enabled: true # Enable data enrichment features
      merge_with_other_sources: true # Merge with BBB, TruthFinder, etc.
      provide_business_details: true # Add missing business details
      cross_reference_validation: true # Validate info across sources

    # ScraperAPI configuration for bypassing IP blocks
    scraper_api:
      enabled: true
      api_key: "89a467f0dd31dee55c2aaf9fa1fc0645"
      use_premium: false # Disabled premium due to 500 errors in tests
      country_code: "US" # Use US IP addresses
      render_js: false # Basic rendering works best

  cyberbackgroundchecks:
    enabled: true
    base_url: "https://www.cyberbackgroundchecks.com"
    search_pattern: 'site:cyberbackgroundchecks.com "Owner" {business_type} {location}'
    max_pages: 3
    cloudflare_protected: true

# Search Configuration
search:
  default_business_types:
    - "lawn care"
    - "restaurant"
    - "construction"
    - "plumbing"
    - "electrical"

  default_locations:
    - "dallas"
    - "houston"
    - "austin"
    - "san antonio"

# Data Extraction Patterns
extraction:
  owner_patterns:
    - "Owner: ([A-Za-z\\s]+)"
    - "CEO: ([A-Za-z\\s]+)"
    - "President: ([A-Za-z\\s]+)"
    - "Founder: ([A-Za-z\\s]+)"
    - "Managing Director: ([A-Za-z\\s]+)"

  business_patterns:
    - "Company: ([A-Za-z0-9\\s&,.-]+)"
    - "Business: ([A-Za-z0-9\\s&,.-]+)"
    - "Organization: ([A-Za-z0-9\\s&,.-]+)"

# Output Configuration
output:
  csv_columns:
    # Basic Information
    - "owner_name"
    - "business_name"
    - "business_type"
    - "location"
    - "source"
    - "url"
    - "scraped_at"

    # Contact Information
    - "phone"
    - "phone_secondary"
    - "email"
    - "email_secondary"
    - "website"
    - "fax"

    # Address Information
    - "address"
    - "street_address"
    - "city"
    - "state"
    - "zip_code"
    - "county"
    - "country"

    # Business Details
    - "business_description"
    - "business_category"
    - "business_subcategory"
    - "years_in_business"
    - "employee_count"
    - "annual_revenue"
    - "business_license"

    # Owner Information
    - "owner_title"
    - "owner_age"
    - "owner_education"

    # Business Ratings
    - "bbb_rating"
    - "bbb_accredited"
    - "google_rating"
    - "yelp_rating"
    - "total_reviews"

    # Social Media
    - "linkedin_url"
    - "facebook_url"
    - "twitter_url"
    - "instagram_url"
    - "youtube_url"

    # Business Structure
    - "business_structure"
    - "tax_id"
    - "duns_number"
    - "incorporation_date"
    - "incorporation_state"

    # Related Information
    - "parent_company"
    - "subsidiaries"
    - "related_businesses"
    - "previous_addresses"
    - "previous_names"

    # Quality Metrics
    - "confidence_score"
    - "data_quality"
    - "verification_status"
    - "google_snippet"
    - "google_title"
    - "search_pattern"

  deduplication:
    enabled: true
    similarity_threshold: 0.8
    key_fields: ["owner_name", "business_name"]
