<!DOCTYPE html>
<html lang="en">
  <head><script>(function(w,i,g){w[g]=w[g]||[];if(typeof w[g].push=='function')w[g].push(i)})
(window,'GTM-R7B4','google_tags_first_party');</script><script>(function(w,d,s,l){w[l]=w[l]||[];(function(){w[l].push(arguments);})('set', 'developer_id.dY2E1Nz', true);
		var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s);j.async=true;j.src='/pxhh/';
		f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer');</script>
      <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Restaurants and Bars | United States - Manta.com</title>
<link rel="icon" href="https://cc3.manta-r3.com/assets-gz/2e2a97f56/img/favicon.png" type="image/png">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com">
<link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/app.css">
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css"></noscript>
<style>
  iframe[name=d_ifrm] {
  display: none;
  }
</style>
        <script rel="gtm-render">
      var screenWidth = screen.width;
      var sSz = 'lg';
      var wsSz = 'lg';
      var wSz = 'lg';
      if (screenWidth < 768) {
        sSz = wsSz = wSz = 'xs';
      } else if (screenWidth < 992) {
        sSz = wsSz = wSz = 'sm';
      } else if (screenWidth < 1200) {
        sSz = wsSz = wSz = 'md';
      }

      var gtmData = {
        ua_property: "UA-10299948-11",
        googleExperimentId: "",
        googleExperimentVariation: "-1",
        pageTitle: "Restaurants and Bars | United States - Manta.com",
        page_type: "company-content", // TODO: make this work when we do search/browse
        is_pagespeed: Boolean('false'),

        
        visitor_id: "604da00a-9a25-46f1-aeb1-a88cf5a15567",
        customer_segment: "consumer",
        page_depth: "1",
        scr_win_width: sSz + '-' + wSz,

        
        treatment: "no-test",

                  altTreatment1: "LM $49 Price Test CONTROL",
                  altTreatment2: "",
                  altTreatment3: "",
        
        ip: "**************",

                  // Older cookies might not have stateAbbrv and countryAbbrv,
          // so fall back to state and country
          user_state: "MA",
          user_country: "US",
        
        url_hash: window.location.hash,
        timestamp: new Date().toString(),
        sbi: "false",
        statusCode: "200",  // TODO: actual status
      };

              var dataLayer = [gtmData];
      
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-R7B4');
    </script>
    <script>
      var gaTrack = function (category, action, label, value, interactive) {
        if (arguments.length === 1) {
          // Non-event tracking stuff
          dataLayer.push(category);
          return;
        } else if (arguments.length === 3) {
          value = 1;
          interactive = true;
        }
        if (arguments.length === 4) {
          if (typeof value === 'boolean') {
            interactive = value;
            value = 1;
          } else {
            interactive = true;
          }
        }

        // if label is an object, serialize it into name=value pairs
        if (typeof label === 'object' && !Array.isArray(label)) {
          var pairs = [];
          for (var key in label) {
            pairs.push(key + '=' + label[key]);
          }
          label = pairs.join(',');
        }

        dataLayer.push({
          eventData: {
            category: category,
            action: action,
            label: label,
            value: value
          },
          event: (interactive ? 'interactive' : 'non-interactive') + ' event'
        });
      };
    </script>
  <script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/cash.min.js"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/js.cookie.min.js" onload="Cookies.defaults = { path: '/', domain: '.manta.com' }"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/axios.min.js" onload="window.axios = window.redaxios"></script>
<script>var loadScript=(function(d,s,c,e,f){return function(u){if(c[u]){return c[u];}e=d.createElement(s);e.async=!0;e.src=u;f=d.getElementsByTagName(s)[0];f.parentNode.insertBefore(e,f);return c[u]=e;};})(document,'script',{});</script>
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css"></noscript>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/simpleLightbox.min.js"></script>
<script>
  window.__PRELOADED_STATE__ = {
    toggledFeatures: {"experimentId":"","experimentVariation":"-1","forceGzip":false,"adsenseChannel":"1","scrollTracking":false,"exitTracking":true,"fakeMixpanel":true,"fakeGa":false,"useSubscriptionTestMode":false,"yextAllowDuplicatesMode":false,"allowSurveys":true,"isOffHoursOverride":false,"isBusinessHoursOverride":false,"skipDittoVendorRequests":false,"marketplaceJobs":false,"claimCtaText":"Own This Business?","claimCtaColor":"btn-primary","useLongBundleDesc":false,"isSiteMaintenanceMode":false,"homeyouLeads":false,"updoptProdOverride":false,"enableUpdoptDossier":true,"adsenseSlotMobile":"1160948055","adsenseSlotDesktop":"5730748454","useFapiForSubscriptions":false,"popOverlayOnCheckout":true,"useBuilderToAdd":false,"checkoutTemplate":"checkout","buyerlink_treatmentA":true,"adsenseHeroPlacement":true,"refreshAds":false,"show300x250":true,"showTaboola":true,"showDesktopAdhesionBanner":true,"homeyouWidgetPlacement":"about","newStack":true,"similarBusinessesUp":true,"reducedAdDensity":true,"navAffiliateBusinessCreditAd":true,"trackNumbers":true,"homeyouNumberTest":true,"homeyouTrackingNumber":"+***********","unclaimedStatic":true,"includeGMBScan":true,"postponeMerchantScans":true,"embedYextDashboard":true,"showPaywallPage":false,"checkoutPaywallTreatment":"paywall-control","enableWebsiteAddOn":true,"useBriteVerify":true,"useGooglePlacesInClaimBuilder":true,"useGoogleMaps":true,"useGoogleAutocomplete":true,"enableFacebookSignIn":true,"enableGoogleSignIn":true,"logCookieErrors":true,"rightRailDoubleAds":true,"triggerDemoScheduler":true,"showCovid":true,"covidShow":true,"es_search":true,"es_related":true,"useNewMemberDashboard":true,"showDetailedDescription":true,"useInternationalSearch":true,"useNewEditPage":true,"useElasticMb":true,"showMarketStats":false,"useNewAnalyticsService":true,"useElasticWorld":true,"requireTermsOfService":true,"useTaboolaAds":false,"usePlaywire":true,"adSenseSearchPages":true,"adSenseProfilePages":true,"oldSurveyModal":true,"useRepSalesDashboard":true,"blockSICM":true,"useNewCheckout":true,"showBanner":false,"copyTextBanner":"Promo Banner","redeemByTextBanner":"Redeem by 19th march","expiryDateBanner":"03/18/2023","catchPhraseBanner":"Special Offer!","validForBanner":"hasDitto, hasLmReviews, hasWebsite, hasPpcAds, hasDisplayAds, hasFeaturelessPlan, freeUser","toggleUrlCheckout":true,"changeVersion3":true,"showReviewTile":false,"showAdapexAds":true},
    referral_source: '',
    abTreatment: 'no-test',
    gamNetworkCode: '6009',
    visitor: {"ip":"**************","id":"604da00a-9a25-46f1-aeb1-a88cf5a15567","pageDepth":1,"customerSegment":{"threshold":1,"id":"c","label":"consumer"},"smallBusinessInterest":false,"xri":"9971611f18f0c5234354407ecf84979a"},
    pageComponents: {},
    clientIp: '**************',
    isCalifornia: false,
    isDev: false,
    env: 'production',
    member: undefined  };
</script>
<script>
  var cache = {};
  window.logError = function(e, info) {
    var lines = (e.stack || '').split('\n');
    var callsite = lines.length > 1 ? lines[1].match(/(app\.js:\d+:\d+)/) : null;
    var key = e.message + (callsite && callsite[1] ? ' at ' + callsite[1] : '');

    if (!cache[key]) {
      try {
        window.axios && axios.post('/fapi/errors', {
          message: e.message || 'Unknown error',
          stack: e.stack || 'No stack trace available',
          info: info,
                    userAgent: (window.navigator && window.navigator.userAgent) || 'unknown'
        }).catch(function() {});
      } catch (e) {
        // Obviously, this isn't async/await, so it won't catch the
        // axios call, but I just want to _assure_ we don't throw
        // from the onerror handler.
      }
    }
  };
  window.onerror = function(message, source, lineno, colno, error) {
    // Don't log errors that come from ads and crap like that
    if (source.indexOf('manta.com') > -1) {
      logError(error, { source: source, lineno: lineno, colno: colno });
    }
  };
</script>
<!-- The script below is going to be commented until we figure out what could be a better implementation talking about performance -->
<!-- <script defer src="//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js" importance="low"></script> --><script defer src="https://btloader.com/tag?o=5150306120761344&upapi=true"></script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>

      <script data-cfasync="false">
        window.ramp = window.ramp || {};
        window.ramp.que = window.ramp.que || [];
      </script>
    
    <script type="text/javascript">
      window.ramp = window.ramp || {};
      window.ramp.que = window.ramp.que || [];
    </script>
          <script>
  if($) {
    $.fn.swapWithNext = function() {
      this.hide();
      this.next().removeClass('hidden');
    }
  }
</script>
  <script>
    var maTrack = (function() { 
      return function(xri, type, listings, event, stopPropagation) {
        if (event && stopPropagation) {
          event.stopPropagation();
        }
        var params = {
          listings: typeof listings === 'string' ? JSON.parse(listings) : listings,
          t: type + (screen.width < 992 ? '_mobile' : ''),
          ts: Date.now(),
          total: listings.length
        };

        var fp = JSON.stringify(params);

        typeof axios === 'function' && axios({
          url: '/track',
          method: 'GET',
          params: { fp: fp },
          withCredentials: true,
          headers: { 'x-request-id': xri }
        }).catch(function(e) {
          logError(e, { trackData: params });
        });
      };
    })();
  </script>
  <script>
    var mantaTrack = (function() {
      var mat = {};

      mat.xri = '9971611f18f0c5234354407ecf84979a';
      mat.device = screen.width < 992 ? 'mobile' : 'desktop';
                        
      mat.trackView = (function() {
        return function(context) {
          var events = [{
            emid: mat.emid,
            type: 'view',
            data: {
              context: context,
              device: mat.device,
              sicm: mat.sicm,
              city_code: mat.city_code,
              claim_source: mat.claim_source
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackClick = (function() {
        return function(category, context, section) {
          var events = [{
            emid: mat.emid,
            type: 'click',
            data: {
              context: context,
              device: mat.device,
              city_code: mat.city_code,
              sicm: mat.sicm,
              claim_source: mat.claim_source,
              category: category,
              section: section
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackSearch = (function() {
        return function(context, city, sicm, emid) {
          let events = [];
          var values = [{
            emid: emid,
            type: "view",
            data: {
              context: context,
              device: mat.device,
              city: city,
              sicm: sicm
            }
          }];
          values.forEach(val => {
            events.push(val)
          });
          return mantaTrack(events);
        }
      })();
      
      mat.trackAndGo = (function() {
        return function(location, category, context, section) {
          mat.trackClick(category, context, section);
          window.location.href = location;
        }
      })();

      var mantaTrack = (function() {
          return function(events) {
                  return true;
                };
      })();

      return mat;

    })();
  </script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        .store-logo {
          position: relative;
          overflow: hidden;
          width: 100px;
        }
        .store-logo:before {
          content: "";
          display: block;
          padding-top: 100%;
        }
        .store-logo > div {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          text-align: center;
        }
      </style>
  </head>
  <body class="bg-primary-light-v1 text-gray-800">
    <div class="relative">
  <a href="#start-of-content" class="text-xs text-darks-v1 focus:text-white absolute right-100">Skip to Content</a>
</div>
<style>
      .desktop-search-wrapper {
      width: 380px;
    }
    @media(min-width: 1110px) {
      .desktop-search-wrapper {
        width: 480px;
      }
    }
  
</style>

<header>
  <div class="mobile-menu hidden fixed w-screen h-screen bg-white p-4 z-50 overflow-auto">
    <div class="float-right" onclick="$('.mobile-menu').addClass('hidden'); $('body').removeClass('overflow-hidden')"><i class="text-text-darks-v1 text-3xl fa fa-times"></i></div>
    <ul class="text-gray-600 my-16 text-lg">
  <li class="mb-2 hover:font-bold">
    <a href="/services">For Businesses</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/free-business-listing">Free Company Listing</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/listings-management">Premium Business Listings</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/small-business-marketing/websites">Websites</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/organic-seo-company">SEO</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/affordable-local-seo">Local SEO</a>
  </li>
  <li>
    <a href="/services/national-seo-company">National SEO</a>
  </li>
</ul>
      <div class="flex flex-col lg:flex-row">
    <a class="btn bg-primary-light-v1 text-gray-800 font-bold flex-1 mb-4 py-3" href="/member/login">Log In</a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="mb-4 btn bg-primary-v1 text-white inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
      </div>
  </div>

  <div class="mobile-search hidden fixed w-screen h-screen bg-white z-50 overflow-y-scroll">
  <div class="justify-center py-3 mx-auto max-w-header flex items-center bg-darks-v1 px-4 relative">
    <div onclick="$('.mobile-search').addClass('hidden');$('.pre-mobile-search').removeClass('hidden')" class="lg:hidden text-primary-light-v1 cursor-pointer absolute left-0 top-0 mt-6 ml-5">Cancel</div>
    <div class="flex sm:pr-3">
      <a href="/" data-test="btn-logo-navbar">
        <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo" width="109" height="25">
      </a>
    </div>
  </div>
  <div class="bg-darks-v1 text-gray-dark search-component-mobile"></div>
</div>

  <div class="px-6 bg-darks-v1 h-auto text-white">
    <div class="justify-between py-3 mx-auto max-w-header flex items-center">

      <div onclick="$('.pre-mobile-search').addClass('hidden');loadSearchBar('.mobile-search')" class="flex md:hidden"><i class="text-2xl fa fa-search"></i></div>
      <div>
  <a href="/" data-test="btn-logo-navbar">
    <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo"  width="109" height="25" >
    <span class="sr-only">Manta Home</span>
  </a>
</div>
      <div class="hidden md:inline-block desktop-search-wrapper">
        <div class="rounded text-gray-dark hidden md:inline-block search-component w-full">
          <form name="searchForm">
  <div class="flex flex-col sm:flex-row px-3 sm:px-0" style="border-radius: 4px 4px 4px 0px;">
    <div class="flex sm:w-1/2 relative px-3 py-2 bg-white my-1 sm:my-0 rounded sm:rounded-l-lg sm:rounded-r-none">
      <div class="flex justify-center items-center mr-4 w-5">
        <span class="text-primary-v1 fa fa-search text-xl"></span>
      </div>
      <div class="flex w-full">
        <label for="header-search" class="sr-only">Search</label>
        <input
          id="header-search"
          name="search"
          placeholder="I'm looking for..."
          class="w-full outline-none"
          onfocus="loadSearchBar('.search-services-menu')"
          autocomplete="off"
          value=""
        />
      </div>
      <div class="absolute search-services-menu hidden" style="z-index: 10000">
        <ul class="p-0 m-0 text-gray-600">
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-utensils mr-3"></span
            ></span>
            <span class="text-small">Restaurants</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-stethoscope mr-3"></span
            ></span>
            <span class="text-small">Doctors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-gavel mr-3"></span
            ></span>
            <span class="text-small">Lawyers</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-hammer mr-3"></span
            ></span>
            <span class="text-small">Contractors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"><span class="fa fa-car mr-3"></span></span>
            <span class="text-small">Automotive</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-tooth mr-3"></span
            ></span>
            <span class="text-small">Dentists</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex py-2 sm:py-0 sm:w-1/2">
      <div class="flex w-full relative px-3 sm:pl-0 sm:pr-3 py-2 bg-white rounded-l-lg sm:rounded-none">
        <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
        <div class="self-center w-full text-gray-800">
          <label for="header-location" class="sr-only">Location</label>
          <input
            id="header-location"
            name="location"
            placeholder="City, State, Country, Zip"
            class="w-full outline-none"
            onfocus="loadSearchBar('.search-location-menu')"
            autocomplete="off"
            value=""
          />
        </div>
        <div class="search-location-menu hidden" style="z-index: 10000">
          <ul class="m-0 p-0 locations">
            <li
              class="px-4 py-3 text-primary-v1 hover:bg-gray-200 cursor-pointer"
            >
              <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
              <span class="small loc-name">Current Location</span>
            </li>
          </ul>
        </div>
      </div>
      <button
        type="submit"
        class="sm:flex items-center justify-center bg-primary-v1 text-white px-3 py-2 overflow-hidden rounded-r-lg">
        <span class="sr-only">Search</span>
        <span class="not-sr-only text-white fa fa-search text-xl"></span>
      </button>
    </div>
  </div>
</form>
<script>
  (function () {
    var loc = localStorage.getItem("locHistory");
    if (loc) {
      var li = $(
        '<li class="px-4 py-3 text-gray-600 hover:bg-gray-200 cursor-pointer border-t border-gray-300 sm:border-none"><span class="fa fa-clock mr-4"></span><span class="small loc-name">Current Location</span></li>'
      );
      JSON.parse(loc).forEach(function (l) {
        if (l.stateAbbrv) {
          li.find(".loc-name").text(l.formatted);
          $(".locations").append(li.clone());
        }
      });
    }
  })();
  var loadSearchBar = (function () {
    return function (c) {
      $(c).removeClass("hidden");
      loadScript("//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js");
    };
  })();
  $("form[name=searchForm]").on("submit", function (e) {
    e.preventDefault();
    var search = $("input[name=search]").val();
    var locationInput = $("input[name=location]").val().trim();
    if (!locationInput) {
      const lastGeo = Cookies.get('lastGeo');
      if (lastGeo) {
        try {
          const geo = JSON.parse(lastGeo);
          if (geo && geo.city && geo.stateAbbrv) locationInput = `${geo.city}, ${geo.stateAbbrv}`;
        } catch (e) {
          return;
        }
      }
    };

    if (!locationInput) return;

    var device = "desktop";
    if (window.screen.availWidth <= 500) {
      device = "mobile";
    } else if (window.screen.availWidth <= 1024) {
      device = "tablet";
    }
    var parts = locationInput.split(/[, ]+/);
    var state = parts.pop();
    var city = parts.join(" ");
    window.location =
      "/search?search_source=nav&search=" +
      encodeURIComponent(search) +
      "&city=" +
      encodeURIComponent(city) +
      "&state=" +
      encodeURIComponent(state) +
      "&device=" +
      device +
      "&screenResolution=" +
      window.screen.availWidth +
      "x" +
      window.screen.availHeight;
  });
</script>
        </div>
      </div>

      <div class="hidden lg:block text-sm">
        <div data-test="btn-products-navbar-desktop" class="dropdown inline-block py-4 text-primary-light-v1">
          <a data-test="btn-findBusiness-navbar-desktop" class="hover:underline font-bold px-3" href="/services">For Businesses <i class="fa fa-angle-down"></i></a>
          <div class="dropdown-tri"></div>
          <ul class="dropdown-menu py-2 text-nordic-v1">
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/free-business-listing">Free Company Listing</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/listings-management">Premium Business Listings</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/small-business-marketing/websites">Websites</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/organic-seo-company">SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/affordable-local-seo">Local SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/national-seo-company">National SEO</a></li>
          </ul>
        </div>
            <a data-test="btn-login-navbar-desktop" class="hover:underline text-primary-light-v1 font-bold"
    href="/member/login"><span class="lg:px-1 px-3 xl:px-3">Log In</span></a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="px-5 lg:px-2 xl:px-5 py-2 w-auto rounded cursor-pointer text-center  bg-primary-v1 text-white mx-3 lg:ml-3 lg:mr-2 xl:mx-3 inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
        </div>

      <div onclick="$('.mobile-menu').removeClass('hidden');" class="flex lg:hidden"><i class="text-2xl fa fa-bars"></i></div>
    </div>
      </div>

  <div class="pl-0 lg:px-6 bg-primary-dark text-white overflow-x-hidden faded faded-x-primary-dark hidden">
  <div class="py-1 mx-auto max-w-header flex items-center overflow-x-auto">
    <div class="inline-block py-2 text-sm whitespace-no-wrap ml-5">
      <a class="cursor-pointer" href="/mb_33_A6_000/professional_services">Business Services<span class="align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_C4_000/restaurants_and_bars">Food & Beverage<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_B3_000/consumer_services">Consumer Products & Services<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_D0_000/healthcare">Health<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_G4_000/information_technology">Tech & Communications<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer mr-5" href="/mb_33_E6_000/industrial_machinery">Industrial</a>
    </div>
  </div>
</div>
  

</header>
    <main>
      <div class="bg-white w-screen lg:mx-auto pb-8 pt-12 flex flex-row justify-center">
        <div class="flex flex-col w-page mx-4 xl:mx-0">
          <h1 class="text-3xl font-serif text-gray-900">Restaurants and Bars</h1>
          <p>
            <span>Manta has 597,603 businesses under Restaurants and Bars in</span>
                                          <span>the</span>
                            <span>United States</span>
                      </p>
        </div>
      </div>
      <div class="w-screen lg:w-page lg:mx-auto">
                  <div class="mb-16">
            <h1 class="text-2xl mx-4 xl:mx-0 font-serif text-gray-900">Featured Company Listings</h1>
                          <div data-test="mb-result-card-m1x1tf1" class="md:rounded bg-white border-b  border-t  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1x1tf1" href="/c/m1x1tf1/fondue-chinoise" class="cursor-pointer font-serif text-gray-900 mr-2">Fondue Chinoise</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:100%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(1)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">430 Broadway</div>
                                            <div>San Francisco, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Ffonduesf.com&s=3b361a5344b31ff4d4b1414f974e2eae&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4152178888" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Fondue%20Chinoise,+430%20Broadway+San%20Francisco" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Ffonduesf.com&s=3b361a5344b31ff4d4b1414f974e2eae&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Classic Chicken Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Spicy Sichuan Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Herbal Mushroom Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Tomato Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Bone Marrow Broth</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Guest cook thinly sliced meats, seafood, tofu, and vegetables directly in the broth at the table, customizing each bite.

A wide variety of dipping sauces—like sesame, soy, and chili oil—enhance the flavors and offer endless combinations

The experience is interactive, engaging, and ideal for gatherings with family or friends. Fondue Chinoise is not only fun, but also a healthy way to eat, as ingredients are cooked quickly with minimal oil, preserving their natural taste and nutrients. 

Different broths cater to all preferences, from mild herbal to spicy Sichuan. This style of dining promotes connection and conversation, turning every meal into a memorable event.</div>
    </div>
  </div>                          <div data-test="mb-result-card-m1x126n" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20250415tsLYFArhCW)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1x126n" href="/c/m1x126n/spicy-deluxe-catering" class="cursor-pointer font-serif text-gray-900 mr-2">Spicy Deluxe Catering</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:100%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(2)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">5913 E. Johnson Ave.</div>
                                            <div>Jonesboro, AR</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.spicydeluxecatering.com&s=b187b4792c31d3340643cb55efee9442&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8705684511" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Spicy%20Deluxe%20Catering,+5913%20E.%20Johnson%20Ave.+Jonesboro" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.spicydeluxecatering.com&s=b187b4792c31d3340643cb55efee9442&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Caterer</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Catering</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">From extravagant weddings to small backyard get-together's, our catering consultants work closely with you to ensure your vision is brought to life. It is safe to say that food is our passion – eating it, creating it, and serving it to you! Everything on our menu is prepared using the freshest ingredients and we aim to support local markets whenever possible. Having catered a wide range of events in the past, we are more than happy to provide you with suggestions and we look forward to helping you narrow down your menu ideas.</div>
    </div>
  </div>                          <div data-test="mb-result-card-mmg3kh9" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20221206p6xVv7VVyR)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmg3kh9" href="/c/mmg3kh9/eppies-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">Eppies Restaurant</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:100%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(5)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4025 Lake Road</div>
                                            <div>West Sacramento, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=https%3A%2F%2Feppieswestsac.com&s=7208ab6193e10f3268e7dd874c093482&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9163717767" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Eppies%20Restaurant,+4025%20Lake%20Road+West%20Sacramento" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=https%3A%2F%2Feppieswestsac.com&s=7208ab6193e10f3268e7dd874c093482&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                          <p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving west sacramento and the Surrounding Area</p>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Restaurant</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Eppies is the best family restaurant in West Sacramento. 
Family owned business with very good service and broad offerings. Large free parking lot. ADA compliant. Beer & Wine with meals. 
TV's in dining areas. 
Weekly Specials: Meatloaf Wednesday, Ribs Thursday, Prime Rib Friday.  Ask Server and see board at front for others.
 We are open for Indoor dining and Take-Out daily from 7:00 am to 8:30pm.
Menu available at https://eppieswestsac.com/eppies-menus/
Please Call 916-371-7767 to order.</div>
    </div>
  </div>                          <div data-test="mb-result-card-mkcrfrm" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20200627PGNdYptaKT)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkcrfrm" href="/c/mkcrfrm/hot-food-to-go" class="cursor-pointer font-serif text-gray-900 mr-2">Hot food to go</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3380 Coach Ln</div>
                                            <div>Cameron Park, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:5103876653" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Hot%20food%20to%20go,+3380%20Coach%20Ln+Cameron%20Park" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Fast Food Restaurants and Stands</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Chinese Take out inside Safeway</div>
    </div>
  </div>                          <div data-test="mb-result-card-mkbzjc5" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20200516prevng3fp1)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkbzjc5" href="/c/mkbzjc5/delectables-catering-and-venue" class="cursor-pointer font-serif text-gray-900 mr-2">Delectables Catering and Venue</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">427 East Limberlost Drive</div>
                                            <div>Tucson, AZ</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fdelectables.com%2F&s=695167cfc0dfd1ed4482e0e4a6086ee6&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:5208849289" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Delectables%20Catering%20and%20Venue,+427%20East%20Limberlost%20Drive+Tucson" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fdelectables.com%2F&s=695167cfc0dfd1ed4482e0e4a6086ee6&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Bridalshower</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Thisistucson</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Memorialservicelocation</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Cateredlunch</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Breakfastclubs</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Welcome to Delectables Catering and Venue, Tucson Arizona’s newest premier classic wedding venue. Delectables is located on Limberlost Drive just west of First Avenue and features a beautiful garden area and private dining/meeting room. For any reason you may entertain, Delectables provides catering off site to your home or business and has an acre and a half garden venue with so many options to suit your needs. Delectables prides itself on providing the most affordable, professional and beautiful service possible to make your event one to remember.</div>
    </div>
  </div>                          <div data-test="mb-result-card-m1w9vyw" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1w9vyw" href="/c/m1w9vyw/in-common-nyc-cafe" class="cursor-pointer font-serif text-gray-900 mr-2">In Common NYC Cafe</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">441 9th Ave Suite 101</div>
                                            <div>New York, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fincommonnyc.com&s=490f2cc279a448106779ded0930dced2&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6463703099" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=In%20Common%20NYC%20Cafe,+441%209th%20Ave%20Suite%20101+New%20York" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fincommonnyc.com&s=490f2cc279a448106779ded0930dced2&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Restaurant</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">At In Common come from diverse backgrounds extending from fine dining restaurants, event venues and casual dining cafes. We have been working with specialty coffee for the past 20 years educating ourselves and collaborating with world class coffee farmers, roasters, and baristas both locally and internationally.  Our journey is just beginning as we launch our first NYC venture.</div>
    </div>
  </div>                          <div data-test="mb-result-card-mx2hm50" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20120813n31uZmv3MN)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx2hm50" href="/c/mx2hm50/crossroads-of-ivanhoe" class="cursor-pointer font-serif text-gray-900 mr-2">Crossroads of Ivanhoe</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">20915 W Park</div>
                                            <div>Mundelein, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fcrossroadsofivanhoe.com&s=484efef2452a82cece64a36d434419e9&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8479499009" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Crossroads%20of%20Ivanhoe,+20915%20W%20Park+Mundelein" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fcrossroadsofivanhoe.com&s=484efef2452a82cece64a36d434419e9&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Eating places</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">The original Emil's Sports Bar and Pizza was established in 1978 by the Bowes family, with the idea that people in the community have a cozy eatery where they could relax and enjoy great food with friends and family at affordable prices. Since then, Emil's Sports Bar and Pizza in Mundelein has become a staple in Lake County.

In 1986, the Bowes family purchased "The Ivanhoe Inn", a cozy tavern just west of Mundelein at the "crossroads" of 60/83 & 176. The now CROSSROADS OF IVANHOE offerings include a full breakfast, lunch and dinner menu. CROSSROADS has become well known for their Fish Fry, Garlic Steamed Mussels, Giant Mozzarella Stix, Breakfast Skillets and many Homestyle Daily Specials.

Come on in and enjoy great food with family, friends and your neighbors at the Crossroads.</div>
    </div>
  </div>                          <div data-test="mb-result-card-mkbw3qv" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/202004168zrQsKBeaL)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkbw3qv" href="/c/mkbw3qv/jimmyls-bbq-catering" class="cursor-pointer font-serif text-gray-900 mr-2">JimmyLs BBQ & Catering</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Sidney, NE</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.jimmyls.com&s=31e96fb96b39e8cebb7891fd7f4601e6&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3082491765" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                                  <a href="/urlverify?redirect=http%3A%2F%2Fwww.jimmyls.com&s=31e96fb96b39e8cebb7891fd7f4601e6&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Caterers</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Providing Nebraska, Colorado and Wyoming excellent food service. From catering to vending we look forward to serving our customers needs.

Bringing you the best BBQ and hospitality Western Nebraska has to offer.</div>
    </div>
  </div>                          <div data-test="mb-result-card-m196sx1" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m196sx1" href="/c/m196sx1/chrome-plated-catering" class="cursor-pointer font-serif text-gray-900 mr-2">Chrome Plated Catering</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">792 Cascade Ave SW</div>
                                            <div>Atlanta, GA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:18008539583" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Chrome%20Plated%20Catering,+792%20Cascade%20Ave%20SW+Atlanta" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering </span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Television and Film Catering</div>
    </div>
  </div>                          <div data-test="mb-result-card-mmjd05y" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmjd05y" href="/c/mmjd05y/nokaoi" class="cursor-pointer font-serif text-gray-900 mr-2">Nokaoi</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">16027 W 5th Ave</div>
                                            <div>Golden, CO</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.biscuitsandberries.com&s=2dbdfefd1e3e49e7c31f50827b0ffc12&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3032779677" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Nokaoi,+16027%20W%205th%20Ave+Golden" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.biscuitsandberries.com&s=2dbdfefd1e3e49e7c31f50827b0ffc12&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Party Catering Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Wedding Caterers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Corporate Event Catering Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Special Event Caterers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Wedding Catering Services</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">We provide impeccable catering services for a range of special events. Because we value our customers' visions and budgets, we are dedicated to catering events with each variable handled with the utmost care and professionalism. Our entire staff remains accessible for guests, and each item we prepare is crafted with you in mind. Our goal is to accommodate each event with personalized services, seeing to it that every event is carried out perfectly.</div>
    </div>
  </div>                      </div>
                <h1 class="text-2xl mx-4 xl:mx-0 font-serif text-gray-900">All Company Listings</h1>
                  <div data-test="mb-result-card-mkc60qm" class="md:rounded bg-white border-b  border-t  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20201106xCkEhWXVsn)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkc60qm" href="/c/mkc60qm/cbc-cajun-boiling-crab-l-a-" class="cursor-pointer font-serif text-gray-900 mr-2">CBC: Cajun Boiling Crab - L.A.</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4050 Crenshaw Blvd</div>
                                            <div>Los Angeles, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.instagram.com%2Fcbc_cajun_seafood_la&s=613e4506d8759d3f2aa5851e10a340cb&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3238151749" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=CBC%3A%20Cajun%20Boiling%20Crab%20-%20L.A.,+4050%20Crenshaw%20Blvd+Los%20Angeles" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.instagram.com%2Fcbc_cajun_seafood_la&s=613e4506d8759d3f2aa5851e10a340cb&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Combo Dinners</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Seafood</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Lobster Tails</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Gumbo</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Onion Rings</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Cooked to order, you may want to call in ahead to save time because the locals are seriously lined up for fried cheese sticks, onion rings, cajun fries, calamari, oysters, hush puppies, chicken wings, gumbo, shrimp, lobster tails, crab, corn on the cob, potatoes, steamed rice, beef sausage, boiled eggs, crawfish, clams, mussels, po-boy sandwiches, and amazing combo dinners! 

Dine outdoors, pick-up or delivery... and we also provide catering.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk7tx5c" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk7tx5c" href="/c/mk7tx5c/shanghai-impression" class="cursor-pointer font-serif text-gray-900 mr-2">Shanghai Impression</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">135-21 37th Avenue</div>
                                            <div>Flushing, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9292828638" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Shanghai%20Impression,+135-21%2037th%20Avenue+Flushing" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Chinese Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Our business has authentic and traditional shanghainese food like our famous shanghai impression soup dumplings and other shanghainese foods. Our restaurant has very old fashion furnitures making you guys feeling in a high end shanghainese restaurant back in the 1950s-1970s.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkcyq62" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkcyq62" href="/c/mkcyq62/nutritional-productions-inc" class="cursor-pointer font-serif text-gray-900 mr-2">Nutritional Productions Inc</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">25350 Magic Mountain Pkwy</div>
                                            <div>Santa Clarita, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fnutritionalproductions.com%2F&s=38059b3db70d9e89733675cdea5fac4c&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8183014741" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Nutritional%20Productions%20Inc,+25350%20Magic%20Mountain%20Pkwy+Santa%20Clarita" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fnutritionalproductions.com%2F&s=38059b3db70d9e89733675cdea5fac4c&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Food Bars</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mkc46cq" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20201117ozZE4cXSiy)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkc46cq" href="/c/mkc46cq/roadrunners" class="cursor-pointer font-serif text-gray-900 mr-2">RoadRunners</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Coplay, PA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.eatroadrunners.com&s=fb2e3c7fec5038acfcd8e5a4a3e94d15&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8006834768" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                                  <a href="/urlverify?redirect=http%3A%2F%2Fwww.eatroadrunners.com&s=fb2e3c7fec5038acfcd8e5a4a3e94d15&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Restaurant Delivery</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Delivery</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Groceries</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Workplace Delivery</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Breakfast</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Road Runners is a local delivery service. Servicing the Eastern, PA (Lehigh Valley) and Northern, NJ (Passaic County) area. We bring convenience to your every day busy life with a smile 😀. No more late food 🥘! No more cold fries 🍟! No more refunds without an explanation! And no more worrying about if its across town! Get what you want 🛍! When you want it 🛍!
Restaurant Delivery 🌮 🍔 🥤, Groceries 🥩 🍗 🌽 🍉,  Workplace Delivery 👷‍♂️ 👩‍🏫 👨‍🏭, Pet Supplies 🐶 🐱 🐠 🦎, Shoes 👟 👞 👠, Clothes 👔 👗 🩳 👙, Furniture 🛋 🪑 🛌, and much more to come! 
Put in your orders by visiting our website (eatroadrunners.com) or downloading our app (roadrunners delivery). You like doing things the old fashion way? Well then you can feel free to call us (800-683-4768) or send us an email (<a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="a9ccc8dddbc6c8cddbdcc7c7ccdbdae9cec4c8c0c587cac6c4">[email&#160;protected]</a>) and let us know what you would like delivered.
Don’t see your favorite restaurant or store up there? No problem! Just contact us and let us know what you would like and from where.
Is your favorite place across town? No worries! We'll go an hour out. 
Don't have a card 💳? No problem! We except cash 💵.
RoadRunners! We're convenience at your fingertips.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk23mk7" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20211126q8dF5Hogpk)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk23mk7" href="/c/mk23mk7/tino-s-kitchen" class="cursor-pointer font-serif text-gray-900 mr-2">Tino's Kitchen</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4960 North Marine Drive</div>
                                            <div>Chicago, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.tinoskitchens.com%2F&s=2ceeadd97e0454c7047b48de1abc9952&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8443874899" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Tino's%20Kitchen,+4960%20North%20Marine%20Drive+Chicago" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.tinoskitchens.com%2F&s=2ceeadd97e0454c7047b48de1abc9952&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Eating places</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Specializing in bringing yummy food to your tummy! Our food carts will make your indoor or outdoor occasion a hit.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk283hp" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/202108234FoRirrLZY)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk283hp" href="/c/mk283hp/waffles-and-whatnot" class="cursor-pointer font-serif text-gray-900 mr-2">Waffles and Whatnot</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">500 Muldoon Road, Unit 5</div>
                                            <div>Anchorage, AK</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.wafflesandwhatnot.com%2F&s=2121b7d7230954a726e97ff059065e01&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9074064503" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Waffles%20and%20Whatnot,+500%20Muldoon%20Road%2C%20Unit%205+Anchorage" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.wafflesandwhatnot.com%2F&s=2121b7d7230954a726e97ff059065e01&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Waffles</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Fried Chicken</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Waffles And Whatnot (WAWN) is on a mission to enrich our community by serving up healthier, craveable comfort food that satisfies nearly any dietary restriction. Thankfully, our founder wanted to know how fried chicken could be delicious, yet healthier than what was currently available.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk2snwq" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20220126lv3xjqNbHT)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2snwq" href="/c/mk2snwq/the-crazy-clamdigger-waterbury-llc" class="cursor-pointer font-serif text-gray-900 mr-2">The Crazy ClamDigger - Waterbury LLC</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">835 Wolcott Street, Suite 2</div>
                                            <div>Waterbury, CT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.clamdiggerwaterbury.com&s=a85a4bb150be4c3d0ca479395fd7782d&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2037562526" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=The%20Crazy%20ClamDigger%20-%20Waterbury%20LLC,+835%20Wolcott%20Street%2C%20Suite%202+Waterbury" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.clamdiggerwaterbury.com&s=a85a4bb150be4c3d0ca479395fd7782d&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Fish</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Shrimp</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Clam Strips</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Homemade Crab Cakes</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Calamari</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Connecticut's #1 Premier Seafood Restaurant is open daily serving Lunch and Dinner. We  proudly serve freshly breaded, made to order seafood. Our menu has something delicious for everyone’s taste. Burgers, Sandwiches, Fries, Cole Slaw, Onion Rings, Philly Cheese Steaks and more are available.  Salads, Pasta and seafood boils will be added in the near future.

Thank you for your valued business and being our friend at The Crazy ClamDigger - Waterbury LLC!</div>
    </div>
  </div>                  <div data-test="mb-result-card-m196sx1" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m196sx1" href="/c/m196sx1/chrome-plated-catering" class="cursor-pointer font-serif text-gray-900 mr-2">Chrome Plated Catering</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">792 Cascade Ave SW</div>
                                            <div>Atlanta, GA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:18008539583" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Chrome%20Plated%20Catering,+792%20Cascade%20Ave%20SW+Atlanta" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering </span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Television and Film Catering</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1wxp36" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1wxp36" href="/c/m1wxp36/tipsy-pony-party-bar" class="cursor-pointer font-serif text-gray-900 mr-2">Tipsy Pony Party Bar</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">278 Sonnet Circle</div>
                                            <div>Wentzville, MO</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Ftipsyponypartybar.com&s=12110577804b5bcef311d45dd366901d&cb=1309485" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3143221812" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Tipsy%20Pony%20Party%20Bar,+278%20Sonnet%20Circle+Wentzville" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Ftipsyponypartybar.com&s=12110577804b5bcef311d45dd366901d&cb=1309485" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Mobile Bar</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Event Bar</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Tipsy Pony Party Bar loves their brides, grooms, families & corporate friends. We love to be a part of your events and special celebrations. We will schedule a consultation to find your vision for your special day. You may have a specific vision in mind or a theme with specialty drinks? We’ll help create your perfect bar experience.

You will be wowed from our drink creations to our bartenders and fun personalities. Let’s get started! Give us a call or request a quote.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk2vnfy" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20250705ZTKF0QEByB)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2vnfy" href="/c/mk2vnfy/reyhoon-catering-inc" class="cursor-pointer font-serif text-gray-900 mr-2">Reyhoon Catering Inc</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">11335 NE 122nd way</div>
                                            <div>Kirkland, WA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2FReyhooncatering.com&s=787e2db077f4980bc0a091a05e71fde8&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4254204017" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Reyhoon%20Catering%20Inc,+11335%20NE%20122nd%20way+Kirkland" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2FReyhooncatering.com&s=787e2db077f4980bc0a091a05e71fde8&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                          <p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving kirkland and the Surrounding Area</p>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Caterer</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Personal Chef</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Wedding</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Birthday</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Anniversary</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Delicious catering for every occasion. Follow us on social for updates. Contact Us Drop us a line! Name Email* Sign up for our email list for updates.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkc5q09" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkc5q09" href="/c/mkc5q09/kbae-worldwide-inc-" class="cursor-pointer font-serif text-gray-900 mr-2">kbae worldwide inc.</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">8762 brookdale dr.</div>
                                            <div>garden grove, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fkbaeworldwideinc.com&s=a060d8311d16db1b0bf6fa160219bde8&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6572139055" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=kbae%20worldwide%20inc.,+8762%20brookdale%20dr.+garden%20grove" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fkbaeworldwideinc.com&s=a060d8311d16db1b0bf6fa160219bde8&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurant, Lunch Counters</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mk25pc4" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20211130ln3TrIstqz)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk25pc4" href="/c/mk25pc4/la-cocina-patina-llc" class="cursor-pointer font-serif text-gray-900 mr-2">La Cocina Patina LLC</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1240 Winnowing Way, Suite 102</div>
                                            <div>Mount Pleasant, SC</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2FWww.lacocinapatina.com&s=41cc57eccd0184b2eb3bcb48fe2db28d&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8432256386" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=La%20Cocina%20Patina%20LLC,+1240%20Winnowing%20Way%2C%20Suite%20102+Mount%20Pleasant" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2FWww.lacocinapatina.com&s=41cc57eccd0184b2eb3bcb48fe2db28d&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Mobile Concessions</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Inspired by old family recipes and new age style, La Cocina Patina was founded out of a deep love for flavor and our heritage and a strong desire to share both through food that is fresh, delicious, and authentic. Born in 2020 as a dream and way for our family to reach yours during the pandemic. Over the past 2 years, our dream has grown into a thriving food truck that continues to share our culture and flavor with the world each day.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk28m55" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20210909vFwyQLOgw2)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk28m55" href="/c/mk28m55/bumblebees-bbq-grill" class="cursor-pointer font-serif text-gray-900 mr-2">Bumblebees BBQ & Grill</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2682 High Commons Way</div>
                                            <div>West Valley City, UT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwestvalley.bumblebeeskbbq.com%2F&s=b0fcf7f8abcae2637f22a60e9d7337eb&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8018691600" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Bumblebees%20BBQ%20%26%20Grill,+2682%20High%20Commons%20Way+West%20Valley%20City" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwestvalley.bumblebeeskbbq.com%2F&s=b0fcf7f8abcae2637f22a60e9d7337eb&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Burgers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">K-Philly Sandwiches</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">K-Pop Fries</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Dup-Bop</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Tacos</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Fusing Korean BBQ with American style dishes we've created a fun range of unique food for your taste buds. We dish up anything ranging from Kimchi Hamburgers, Korean BBQ loaded fries, or Korean BBQ style Philly Cheesesteaks.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1r14f6" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20220616fxCPGU_DlB)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1r14f6" href="/c/m1r14f6/c-r-e-a-m-sugar-coffee-house" class="cursor-pointer font-serif text-gray-900 mr-2">C.R.E.A.M. & Sugar Coffee House</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1680 S State St</div>
                                            <div>Orem, UT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.cream-and-sugar.com&s=634f5ebfd6344bf64427876a28829a91&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8016075725" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=C.R.E.A.M.%20%26%20Sugar%20Coffee%20House,+1680%20S%20State%20St+Orem" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.cream-and-sugar.com&s=634f5ebfd6344bf64427876a28829a91&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Coffee</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Tea</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Breakfast</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Lunch</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sandwich</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">We love sharing our passion for coffee with our customers. We put care into each order and focus on customer service. Our shop was created to be a great environment to hang out and enjoy a drink or pastry. Come join us sometime!</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1r07rd" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20230127JSykWU8kzi)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1r07rd" href="/c/m1r07rd/cubaneo" class="cursor-pointer font-serif text-gray-900 mr-2">Cubaneo</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2873 n broadway st</div>
                                            <div>Chicago, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=https%3A%2F%2Fwww.cubaneochicago.com&s=d37d8672ee96266603a09b57f438b64b&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7737998808" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Cubaneo,+2873%20n%20broadway%20st+Chicago" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=https%3A%2F%2Fwww.cubaneochicago.com&s=d37d8672ee96266603a09b57f438b64b&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Restaraunt</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Cuban Food</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Brunch</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Breakfast</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Dinner</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Located in Chicago, Cuabaneo serves delicious Traditional Cuban food with a modern approach. Our Menu features products like delicious Cuban Sandwiches, BBQ Guava Pork Ribs, Traditional Spanish Paella. Enjoyed in a friendly, fun, vibrant place! We offer Catering, Delivery, and Pickup.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmjd05y" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmjd05y" href="/c/mmjd05y/nokaoi" class="cursor-pointer font-serif text-gray-900 mr-2">Nokaoi</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">16027 W 5th Ave</div>
                                            <div>Golden, CO</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.biscuitsandberries.com&s=2dbdfefd1e3e49e7c31f50827b0ffc12&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3032779677" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Nokaoi,+16027%20W%205th%20Ave+Golden" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.biscuitsandberries.com&s=2dbdfefd1e3e49e7c31f50827b0ffc12&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Party Catering Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Wedding Caterers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Corporate Event Catering Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Special Event Caterers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Wedding Catering Services</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">We provide impeccable catering services for a range of special events. Because we value our customers' visions and budgets, we are dedicated to catering events with each variable handled with the utmost care and professionalism. Our entire staff remains accessible for guests, and each item we prepare is crafted with you in mind. Our goal is to accommodate each event with personalized services, seeing to it that every event is carried out perfectly.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1xknj4" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1xknj4" href="/c/m1xknj4/pollo-s" class="cursor-pointer font-serif text-gray-900 mr-2">Pollo's</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">530 N State College Blvd</div>
                                            <div>Anaheim, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fpollosanaheimca.com&s=7b7ea6f3963c35d56c55bcb0428bde37&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7145357777" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Pollo's,+530%20N%20State%20College%20Blvd+Anaheim" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fpollosanaheimca.com&s=7b7ea6f3963c35d56c55bcb0428bde37&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                          <p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving anaheim and the Surrounding Area</p>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Chicken restaurant</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Casual eatery serving rotisserie chicken and tacos, plus small plates, and comfort food.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1rc694" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1rc694" href="/c/m1rc694/chesterfield-s-tavolo" class="cursor-pointer font-serif text-gray-900 mr-2">Chesterfield's Tavolo</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">131 N Genesee Street</div>
                                            <div>Utica, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2FTavolo315.com&s=72cdf034fc30f470c2b5ad3d920c38b7&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3157329356" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Chesterfield's%20Tavolo,+131%20N%20Genesee%20Street+Utica" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2FTavolo315.com&s=72cdf034fc30f470c2b5ad3d920c38b7&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Banquets</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">After 30 years in the old neighborhood, we have packed up and moved our classic dishes into a modern atmosphere. Take your taste buds on an adventure . Sit at our table and enjoy the many contemporary twists on old dishes, as well a few of our own creations!

Located in North Utica Right minutes from the NYS Thruway. Come experience a unique way of dining with us. Great for a night out with friends, family, business or a romantic date! Fresh food and great service is what we strive to bring to your table! !</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkcrfrm" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20200627PGNdYptaKT)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkcrfrm" href="/c/mkcrfrm/hot-food-to-go" class="cursor-pointer font-serif text-gray-900 mr-2">Hot food to go</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3380 Coach Ln</div>
                                            <div>Cameron Park, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:5103876653" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Hot%20food%20to%20go,+3380%20Coach%20Ln+Cameron%20Park" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Fast Food Restaurants and Stands</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Chinese Take out inside Safeway</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkw9cbx" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/201903050YH04rdad5)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkw9cbx" href="/c/mkw9cbx/davids-catfish-house" class="cursor-pointer font-serif text-gray-900 mr-2">Davids Catfish House</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">131 North New Warrington Road</div>
                                            <div>Pensacola, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.davidscatfishhousepensacola.com%2F&s=f7ddc1e6e7e3dacb4c3bfefa57dc7ad0&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8504551610" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Davids%20Catfish%20House,+131%20North%20New%20Warrington%20Road+Pensacola" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.davidscatfishhousepensacola.com%2F&s=f7ddc1e6e7e3dacb4c3bfefa57dc7ad0&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Seafood Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mhpyrz7" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/201805047PhWvnh6SH)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhpyrz7" href="/c/mhpyrz7/takeout-guys-llc" class="cursor-pointer font-serif text-gray-900 mr-2">Takeout Guys, LLC</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">240 Jennie Lane</div>
                                            <div>Eliot, ME</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Ftakeoutguys.biz&s=56b7443682f4c2bbd64fdbabafd4e1c9&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2074515858" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Takeout%20Guys%2C%20LLC,+240%20Jennie%20Lane+Eliot" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Ftakeoutguys.biz&s=56b7443682f4c2bbd64fdbabafd4e1c9&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Restaurant Delivery Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Delivery Services</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Takeout Guys, LLC works with over 80 local restaurants to bring all your favorites right to your door.

Place your order now or for up to 30 days from now! We ask that group orders be placed at least 24 hours in advance.

You can even order from separate restaurants! Just pay separate delivery fees.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkbzjc5" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20200516prevng3fp1)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkbzjc5" href="/c/mkbzjc5/delectables-catering-and-venue" class="cursor-pointer font-serif text-gray-900 mr-2">Delectables Catering and Venue</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">427 East Limberlost Drive</div>
                                            <div>Tucson, AZ</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fdelectables.com%2F&s=695167cfc0dfd1ed4482e0e4a6086ee6&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:5208849289" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Delectables%20Catering%20and%20Venue,+427%20East%20Limberlost%20Drive+Tucson" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fdelectables.com%2F&s=695167cfc0dfd1ed4482e0e4a6086ee6&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Bridalshower</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Thisistucson</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Memorialservicelocation</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Cateredlunch</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Breakfastclubs</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Welcome to Delectables Catering and Venue, Tucson Arizona’s newest premier classic wedding venue. Delectables is located on Limberlost Drive just west of First Avenue and features a beautiful garden area and private dining/meeting room. For any reason you may entertain, Delectables provides catering off site to your home or business and has an acre and a half garden venue with so many options to suit your needs. Delectables prides itself on providing the most affordable, professional and beautiful service possible to make your event one to remember.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk79kzs" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20201208SDXJJipHE0)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk79kzs" href="/c/mk79kzs/kekoa-poke-kitchen" class="cursor-pointer font-serif text-gray-900 mr-2">KEKOA Poke + Kitchen</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">8177 Glades Rd</div>
                                            <div>Boca Raton, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Featkekoa.com%2F&s=692aa0eaab8fbfc94383edea8e9b7dd1&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:5612184523" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=KEKOA%20Poke%20%2B%20Kitchen,+8177%20Glades%20Rd+Boca%20Raton" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Featkekoa.com%2F&s=692aa0eaab8fbfc94383edea8e9b7dd1&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">When you eat well, you feel well, and when you feel well, you live well. This is the driving force behind everything we do. The spirit of aloha can easily be felt the moment you walk through our doors, as everyone is welcomed in like family with a warm smile and our traditional Hawaiian fare.

Take a look behind our counter and discover a world of fresh and flavorful choices, all made from scratch. Build a poke bowl or enjoy one of our many Sandwiches or Burgers. We also feature well-loved Hawaiian classics like the loco moco, spam musubi, and the kahuku shrimp.  

Healthy and delicious, our menu has been carefully curated to ensure everyone has something to enjoy, even the keikis. 

Whether you are in Boca Raton, or any of it's neighboring cities we are just a drive away, right at the intersection of the Florida Turnpike and Glades Rd., close to wherever you are. Experience peace, love, and little bit of Hawaii here at Kekoa.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk2ls7x" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2ls7x" href="/c/mk2ls7x/luke-s-deli" class="cursor-pointer font-serif text-gray-900 mr-2">Luke's Deli</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1219 MOUNT ZION AVE</div>
                                            <div>Janesville, WI</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Flukesdelimenu.com%2F&s=c3da9ea0f2a672195153161dcdd351a8&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6087561123" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Luke's%20Deli,+1219%20MOUNT%20ZION%20AVE+Janesville" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Flukesdelimenu.com%2F&s=c3da9ea0f2a672195153161dcdd351a8&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurant, Lunch Counters</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">We offer tasty sandwiches, soups and salads.  We roast whole turkeys everyday and then shred them to use in unique offerings. Our soups are made in our kitchen and are very popular. You won't find the usual franchise food here. We prepare the food when you order it, not ahead of time. Hand scooped ice cream is offered in cones, shakes and malts.  Luke's Deli is a favorite spot for take out but in addition to inside dining, we have three outdoor patios with umbrellas over the tables.  We also cater individual box lunches for businesses and special occasions.  We can do 15 to 600 meals in a order.  Our online order system is popular with the full menu available.  You may also call in your order for pickup at our outside window or use our convenient curbside pickup.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk28k30" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk28k30" href="/c/mk28k30/e77-sports-bar-gaming-and-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">E77 Sports Bar, Gaming and Restaurant</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4702 147th Street</div>
                                            <div>Midlothian, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.E77SportsBar.com&s=a211e9686a0291ac81160a8e02cd6cdb&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7088970538" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=E77%20Sports%20Bar%2C%20Gaming%20and%20Restaurant,+4702%20147th%20Street+Midlothian" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.E77SportsBar.com&s=a211e9686a0291ac81160a8e02cd6cdb&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Food Bars</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mk2n44w" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2n44w" href="/c/mk2n44w/hall-106" class="cursor-pointer font-serif text-gray-900 mr-2">Hall 106</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">10600 South Torrence Avenue</div>
                                            <div>Chicago, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.hall106.com%2F&s=f07444316a0fb78ae284daedc693bd51&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7085431407" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Hall%20106,+10600%20South%20Torrence%20Avenue+Chicago" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.hall106.com%2F&s=f07444316a0fb78ae284daedc693bd51&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Banquet Hall</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Venue Space</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Birthday Party</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Dance Hall</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Photography Space</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">At Hall 106, we’ll make sure the vision for your special event becomes a reality and goes off without a glitch. Our Event Venue offers everything you need to create the stunning occasion you and your guests will always remember. We can’t wait to work with you.

Whether you're searching for an elegant venue to host an intimate retirement party, a party room for your office bash or a space for 300 guests to celebrate your daughter's quinceanera or wedding reception, Hall 106 offers affordability and sophistication.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk2dqmf" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20210909PtIXKPFz2Q)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2dqmf" href="/c/mk2dqmf/bumblebee-s-kbbq-grill" class="cursor-pointer font-serif text-gray-900 mr-2">Bumblebee's KBBQ & Grill</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1254 North State Street</div>
                                            <div>Provo, UT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fprovo.bumblebeeskbbq.com%2F%23%2F&s=3fc1313cb81b6a64db1d592f7dbb798f&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8016072543" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Bumblebee's%20KBBQ%20%26%20Grill,+1254%20North%20State%20Street+Provo" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fprovo.bumblebeeskbbq.com%2F%23%2F&s=3fc1313cb81b6a64db1d592f7dbb798f&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Burgers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">K-Philly Sandwiches </span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">K-Pop Fries</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Dup-Bop</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Tacos</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Fusing Korean BBQ with American style dishes we've created a fun range of unique food for your taste buds. We dish up anything ranging from Kimchi Hamburgers, Korean BBQ loaded fries, or Korean BBQ style Philly Cheesesteaks.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk24cf3" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20211027ypDDaWeeRj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk24cf3" href="/c/mk24cf3/anitas-kitchenaire" class="cursor-pointer font-serif text-gray-900 mr-2">Anitas Kitchenaire</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">23400 Stratford Ct, 1012</div>
                                            <div>Southfield, MI</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fanitas-kitchenaire.square.site&s=838f590b4faa8833e12bc1c6a02b1f30&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2162502624" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Anitas%20Kitchenaire,+23400%20Stratford%20Ct%2C%201012+Southfield" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fanitas-kitchenaire.square.site&s=838f590b4faa8833e12bc1c6a02b1f30&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Food Delivery Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Hello, This is Anita and Paul. We are happy to present to you Anita's Kitchenaire, where we make some of the best multi delicious dishes in town from our mouth-watering spaghetti, to our juicy tender barbecue and last but not least our tasty sides. We know you will enjoy every bite of what we have to offer. Every Saturday we will prepare a different dish for you,.

 The meal includes a water or kool-aid, a dinner  roll or cornbread and a slice of cake. For extra sides or drinks there will be an additional charge. Each week we will be sending you a order request with the signature dinner for that week. Due to high demand and small staff we are selling dinners Saturdays only from 1 pm to 6:45 pm

Pre-order are available Sunday-Thursday from 12 pm to 6 pm.

Curbside Pick up time (1pm-6:45pm).   If you have any questions please contact us at (216)-250-2624.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1rw1w2" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20220328GdUXH6yiRF)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1rw1w2" href="/c/m1rw1w2/swizzle-bartending" class="cursor-pointer font-serif text-gray-900 mr-2">Swizzle Bartending</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2892 E Park Ave</div>
                                            <div>Tallahassee, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=https%3A%2F%2Fyourswizzle.com%2F&s=5b50a7f2f6eaa7b832282e0889503ceb&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4484882842" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Swizzle%20Bartending,+2892%20E%20Park%20Ave+Tallahassee" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=https%3A%2F%2Fyourswizzle.com%2F&s=5b50a7f2f6eaa7b832282e0889503ceb&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Hosted Bar Service</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Cash Bar Service </span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Custom Cocktail Menu</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">StirUp Package $895
This includes:
Your Swizzle Spirits Bartender for up to 4 hours

Our 2 Million Dollar Liquor Liability Insurance

(Furniture & Decor Rental Available)

$600 Trailer Only Rental

Bar Services for Your Event
I'm your About section. Click to edit and tell your visitors what mixology services you offer.

Hosted bar service?
Cash bar service?
Custom cocktail menu?
All needed permits?
Event Extras and Add-ons

Generate excitement
 

Services
Unlimited email /phone consultations with YOUR personal Swizzle Coordinator
Customized bar menu or suggested shopping list
Signature drinks tailored to YOUR theme, color and budget
Bar menu printed and framed for view at bar
Supplies
Complete bar set-up
Custom decor, linens and decorative bins
Bar supplies including coolers, shakers, garbage cans and bar kit
Bartender(Your Bartender for 4 hours)

SWIZZLE Professional, reliable, creative, experienced, responsible and S!
We recommend 1 bartender per 100 guests
$2 Million Liquor Liability Insurance & License
To purchase this for the day through a private company costs $195</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1rh0fg" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20220505eIzOOK1m7S)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1rh0fg" href="/c/m1rh0fg/pies-n-thighs" class="cursor-pointer font-serif text-gray-900 mr-2">Pies 'N Thighs</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">383 Bridge street</div>
                                            <div>Brooklyn, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fpiesnthighs.com&s=07416277f05ec9d1c69447ed808f488e&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:5167032299" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Pies%20'N%20Thighs,+383%20Bridge%20street+Brooklyn" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fpiesnthighs.com&s=07416277f05ec9d1c69447ed808f488e&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Dine-in</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Delivery </span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">We took over a beer storage closet in the and transformed it into a tiny kitchen with six stools, serving huge donuts, two-napkin smoked pork sandwiches and salty, crispy fried chicken. The New York Times called the pies and the thighs “…a compelling combination, well executed and put forth with real heart—the sort of restaurant that’s hard to find, especially in the big city, but easy to love once found.”

Owners Sarah Sanneh and Carolyn Bane met as cooks at Diner, a nearby Brooklyn stalwart that has been the parent restaurant of many local favorites. In 2010, Sarah and Carolyn opened a larger location on a sunny corner (still by the bridge!), which has become a beloved neighborhood spot and a destination for travelers from well beyond its Brooklyn home.

Our 15th anniversary was celebrated across the country with former employees and dear colleagues who have gone on to open Konbi in Los Angeles, Tandem Bakery in Portland, ME, Han Oak in Portland, OR, Dough Mama in Columbus, OH, and as far as Mexico City at Cicatriz. We have been awarded Best Apple Pie and Best Fried Chicken in national surveys by Food & Wine and Bon Appétit, Best Donut in New York by New York Magazine, and Best Biscuits in New York by the New York Daily News.

Whether it's an occasional fix of banana cream pie, or inhaling a chicken box once a week, we believe that comfort food speaks to everyone. After 15 years and counting, we are deeply grateful for the support of our customers and community. It is still a thrill to be part of the fabric of New York, one biscuit at a time.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1r4dpn" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20230127vE4A_MiqV4)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1r4dpn" href="/c/m1r4dpn/gallery-grill-poke-house-llc" class="cursor-pointer font-serif text-gray-900 mr-2">Gallery Grill Poke House LLC</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">13 West Centre Street</div>
                                            <div>Baltimore, MD</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fgallerygrillpokehouse.com&s=454bbfecd14fa3c7e0f819e8ebe89138&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4105394026" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Gallery%20Grill%20Poke%20House%20LLC,+13%20West%20Centre%20Street+Baltimore" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fgallerygrillpokehouse.com&s=454bbfecd14fa3c7e0f819e8ebe89138&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Poke Bowls</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sushi</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sushi Boxes</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Poke</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sushi Rolls</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">All of our poke and sushi is made with high quality seafood to ensure the best and freshest experience.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1rpsny" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20231228SByt9rqVMo)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1rpsny" href="/c/m1rpsny/inside-scoop-creamery" class="cursor-pointer font-serif text-gray-900 mr-2">Inside Scoop Creamery</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1535 Platte Street</div>
                                            <div>Denver, CO</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=https%3A%2F%2Fwww.insidescoopdenver.com&s=b90fd2c5f81368ff49b74a8dddc59c28&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6123068672" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Inside%20Scoop%20Creamery,+1535%20Platte%20Street+Denver" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=https%3A%2F%2Fwww.insidescoopdenver.com&s=b90fd2c5f81368ff49b74a8dddc59c28&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Ice-cream</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Handcrafted Waffle Cones
Gluten Free & Dairy Free Options</div>
    </div>
  </div>                  <div data-test="mb-result-card-mhb4kq0" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/201811157svLwDKDJ4)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhb4kq0" href="/c/mhb4kq0/yah-suh-nyce" class="cursor-pointer font-serif text-gray-900 mr-2">Yah Suh NYCE</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">94-14 Sutphin Blvd</div>
                                            <div>Queens, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.yahsuhnyce.com&s=9e5ffe7b68569cb01ae289d4f34a3725&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3474750402" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Yah%20Suh%20NYCE,+94-14%20Sutphin%20Blvd+Queens" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.yahsuhnyce.com&s=9e5ffe7b68569cb01ae289d4f34a3725&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Oxtails</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Natural Juices</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Smoothies</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Curry Goat</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Serving quality food at a great value with our large selection of Caribbean-infused food, we are unlike the typical restaurant experience. A fun, pleasant atmosphere awaits. Come today!</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1w9vyw" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1w9vyw" href="/c/m1w9vyw/in-common-nyc-cafe" class="cursor-pointer font-serif text-gray-900 mr-2">In Common NYC Cafe</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">441 9th Ave Suite 101</div>
                                            <div>New York, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fincommonnyc.com&s=490f2cc279a448106779ded0930dced2&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6463703099" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=In%20Common%20NYC%20Cafe,+441%209th%20Ave%20Suite%20101+New%20York" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fincommonnyc.com&s=490f2cc279a448106779ded0930dced2&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Restaurant</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">At In Common come from diverse backgrounds extending from fine dining restaurants, event venues and casual dining cafes. We have been working with specialty coffee for the past 20 years educating ourselves and collaborating with world class coffee farmers, roasters, and baristas both locally and internationally.  Our journey is just beginning as we launch our first NYC venture.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1wyq9c" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1wyq9c" href="/c/m1wyq9c/comfort-cuisine" class="cursor-pointer font-serif text-gray-900 mr-2">Comfort Cuisine</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">632 Spring point Dr</div>
                                            <div>Bradbury, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.comfortcuisinecaterers.com&s=80ebc508b319463d79d8460966c3b5bd&cb=1309486" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6617695595" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Comfort%20Cuisine,+632%20Spring%20point%20Dr+Bradbury" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.comfortcuisinecaterers.com&s=80ebc508b319463d79d8460966c3b5bd&cb=1309486" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Chicken</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Rice</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Steak</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Salad</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Dessert</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Welcome to Comfort Cuisine. We are a premier catering company offering our services in California and the surrounding areas. With both casual and sophisticated food options we are able to cater an expansive array of affairs. From extravagant weddings to small backyard get-together's, our catering consultants work closely with you to ensure your vision is brought to life.

It is safe to say that food is our passion – eating it, creating it, and serving it to you! Everything on our menu is prepared using the freshest ingredients and we aim to support local markets whenever possible. If you find yourself overwhelmed by our comprehensive menu please do not hesitate to contact us. Having catered a wide range of events in the past, we are more than happy to provide you with suggestions and we look forward to helping you narrow down your menu ideas.
Our handpicked wait staff is extremely attentive. From the setup to the breakdown of your party.</div>
    </div>
  </div>                <div id="more-results">
        </div>
        <div data-test="mb-infinite-scroll-trigger" id="infinite-scroll-trigger"></div>
        <div data-test="mb-spinner-loader" id="loading-more-content" class="hidden my-8">
          <img class="mx-auto" width="100" height="100" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/spinner.svg" />
        </div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb">Companies</a>
                              <span class="mx-1 fa fa-angle-right"></span>
                          </span>
                      <span>
              <a class="hover:underline" href="/mb_33_C4_000/restaurants_and_bars">Restaurants and Bars</a>
                          </span>
                  </div>
        <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse Subcategories</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C432D_000/bars_taverns">Bars / Taverns</a> (62,324)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C4091_000/confectionery">Confectionery</a> (3,976)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C432C_000/restaurants">Restaurants</a> (531,303)
          </div>
              </div>
    </div>
  </div>
</div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb_33_C4_000/restaurants_and_bars">United States</a>
                          </span>
                  </div>
                  <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse undefined</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_01/restaurants_and_bars/alabama">Alabama</a> (8,092)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_02/restaurants_and_bars/alaska">Alaska</a> (1,526)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_03/restaurants_and_bars/arizona">Arizona</a> (10,729)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_04/restaurants_and_bars/arkansas">Arkansas</a> (4,032)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_05/restaurants_and_bars/california">California</a> (76,869)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_06/restaurants_and_bars/colorado">Colorado</a> (12,444)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_07/restaurants_and_bars/connecticut">Connecticut</a> (7,868)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_08/restaurants_and_bars/delaware">Delaware</a> (1,993)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_09/restaurants_and_bars/district_of_columbia">District of Columbia</a> (1,717)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_10/restaurants_and_bars/florida">Florida</a> (39,458)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_11/restaurants_and_bars/georgia">Georgia</a> (15,885)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_12/restaurants_and_bars/hawaii">Hawaii</a> (2,555)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_13/restaurants_and_bars/idaho">Idaho</a> (2,690)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_14/restaurants_and_bars/illinois">Illinois</a> (25,322)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_15/restaurants_and_bars/indiana">Indiana</a> (9,388)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_16/restaurants_and_bars/iowa">Iowa</a> (5,374)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_17/restaurants_and_bars/kansas">Kansas</a> (5,093)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_18/restaurants_and_bars/kentucky">Kentucky</a> (6,528)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_19/restaurants_and_bars/louisiana">Louisiana</a> (7,535)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_20/restaurants_and_bars/maine">Maine</a> (2,416)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_21/restaurants_and_bars/maryland">Maryland</a> (11,988)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_22/restaurants_and_bars/massachusetts">Massachusetts</a> (13,317)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_23/restaurants_and_bars/michigan">Michigan</a> (14,704)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_24/restaurants_and_bars/minnesota">Minnesota</a> (8,536)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_25/restaurants_and_bars/mississippi">Mississippi</a> (3,874)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_26/restaurants_and_bars/missouri">Missouri</a> (11,039)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_27/restaurants_and_bars/montana">Montana</a> (2,447)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_28/restaurants_and_bars/nebraska">Nebraska</a> (3,917)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_29/restaurants_and_bars/nevada">Nevada</a> (4,978)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_30/restaurants_and_bars/new_hampshire">New Hampshire</a> (2,375)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_31/restaurants_and_bars/new_jersey">New Jersey</a> (19,840)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_32/restaurants_and_bars/new_mexico">New Mexico</a> (3,115)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_33/restaurants_and_bars/new_york">New York</a> (39,986)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_34/restaurants_and_bars/north_carolina">North Carolina</a> (17,288)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_35/restaurants_and_bars/north_dakota">North Dakota</a> (1,621)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_36/restaurants_and_bars/ohio">Ohio</a> (20,123)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_37/restaurants_and_bars/oklahoma">Oklahoma</a> (5,551)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_38/restaurants_and_bars/oregon">Oregon</a> (6,741)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_39/restaurants_and_bars/pennsylvania">Pennsylvania</a> (27,523)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_53/restaurants_and_bars/puerto_rico">Puerto Rico</a> (332)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_40/restaurants_and_bars/rhode_island">Rhode Island</a> (2,641)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_41/restaurants_and_bars/south_carolina">South Carolina</a> (12,132)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_42/restaurants_and_bars/south_dakota">South Dakota</a> (1,632)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_43/restaurants_and_bars/tennessee">Tennessee</a> (11,419)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_44/restaurants_and_bars/texas">Texas</a> (56,253)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_45/restaurants_and_bars/utah">Utah</a> (3,877)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_46/restaurants_and_bars/vermont">Vermont</a> (1,290)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_47/restaurants_and_bars/virginia">Virginia</a> (14,596)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_48/restaurants_and_bars/washington">Washington</a> (10,911)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_49/restaurants_and_bars/west_virginia">West Virginia</a> (3,862)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_50/restaurants_and_bars/wisconsin">Wisconsin</a> (10,112)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_51/restaurants_and_bars/wyoming">Wyoming</a> (1,125)
          </div>
              </div>
    </div>
  </div>
</div>
                      </div>
    </main>
    <style>
  @media (max-width: 360px) {
    .xs-device {
      font-size: 0.875rem;
    }
    .xs-8 {
      height: 2rem;
      width: 2rem;
    }
  }
</style>
<footer>
  <div class="w-full border-t bg-darks-v1 text-primary-light-v1 py-8 lg:py-16 flex flex-col justify-center items-center px-4 sm:px-6">
    <div class="grid grid-cols-8 gap-4 sm:gap-8 max-w-header w-full">
      <div class="hidden lg:flex justify-between mt-6 flex-col col-span-2">
        <div>
          <div>
            <a href="/" data-test="btn-logo-navbar">
              <img loading="lazy" width="162" height="37" class="mb-6 mr-3" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo">
              <span class="sr-only">Manta Home</span>
            </a>
          </div>
          <div class="flex">
            <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
                <span class="sr-only">Manta on Twitter</span>
              </span>
            </a>
            <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
                <span class="sr-only">Manta on Facebook</span>
              </span>
            </a>
            <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
                <span class="sr-only">Manta on LinkedIn</span>
              </span>
            </a>
          </div>
        </div>
        <div class="text-primary-light-v1 xs-device">
          © 2025 Manta Media Inc.<br>All rights reserved.
                  </div>
      </div>
      <div class="grid grid-cols-2 sm:grid-cols-3 gap-8 col-span-8 sm:col-span-5 lg:col-span-4">
        <div class="flex flex-col">
          <span class="font-serif">Manta</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-contact-footer-desktop" href="/contact" class="hover:font-bold py-1">Contact Us</a>
            <a data-test="btn-about-footer-desktop" href="/about-us" class="hover:font-bold py-1">About Us</a>
            <a data-test="btn-reviews-footer-desktop" href="/manta-reviews" class="hover:font-bold py-1">Reviews</a>
            <a data-test="btn-careers-footer-desktop" href="/careers" class="hover:font-bold py-1">Careers</a>
            <a data-test="btn-termsConditions-footer-desktop" href="/terms-and-conditions" class="hover:font-bold py-1">Terms & Conditions</a>
            <a data-test="btn-privacyPolicy-footer-desktop" href="/privacy-policy" class="hover:font-bold py-1">Privacy Policy</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Services</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-digital-mkt-footer-desktop" href="/digital-marketing-services" class="hover:font-bold py-1">Digital Marketing Services</a>
            <a data-test="btn-seo-services-footer-desktop" href="/services/organic-seo-company" class="hover:font-bold py-1">SEO Services</a>
            <a data-test="btn-local-seo-footer-desktop" href="/services/affordable-local-seo" class="hover:font-bold py-1">Local SEO</a>
            <a data-test="btn-national-seo-footer-desktop" href="/services/national-seo-company" class="hover:font-bold py-1">National SEO</a>
            <a data-test="btn-free-seo-footer-desktop" href="/free-seo-website-test" class="hover:font-bold py-1">Free SEO Website Test</a>
            <a data-test="btn-listings-footer-desktop" href="/business-listings/listings-management" class="hover:font-bold py-1">Listings Management</a>
            <a data-test="btn-display-ads-footer-desktop" href="/services/display-advertising" class="hover:font-bold py-1">Display Ads</a>
            <a data-test="btn-ppc-footer-desktop" href="/services/ppc-consulting" class="hover:font-bold py-1">PPC Consulting</a>
            <a data-test="btn-websites-footer-desktop" href="/small-business-marketing/websites" class="hover:font-bold py-1">Website Creation</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Resources</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-seo-faq-footer-desktop" href="/seo-faqs" class="hover:font-bold py-1">SEO FAQ</a>
            <a data-test="btn-ecommerce-footer-desktop" href="/seo-industry-guide/ecommerce-seo-guide" class="hover:font-bold py-1">Ecommerce SEO Guide</a>
            <a data-test="btn-construction-footer-desktop" href="/seo-industry-guide/seo-for-construction-companies" class="hover:font-bold py-1">Construction SEO Guide</a>
            <a data-test="btn-hvac-footer-desktop" href="/seo-industry-guide/seo-for-hvac" class="hover:font-bold py-1">HVAC SEO Guide</a>
            <a data-test="btn-homeyou-footer-desktop" href="/costs" class="hover:font-bold py-1">Home Services Cost</a>
          </div>
        </div>
      </div>
      <div class="border-t border-primary-light-v1 sm:border-none pt-6 sm:pt-0 flex flex-col col-span-8 sm:col-span-3 lg:col-span-2">
        <span class="font-serif">Manta Members</span>
                  <div class="flex items-center mt-6">
            <a data-test="btn-login-footer-desktop" class="hover:font-bold mr-4" href="/member/login">Log In</a>
            <a data-test="btn-login-footer-desktop" class="btn bg-primary-v1 text-white inline-block hover:font-bold" href="/member/register">Sign Up</a>
          </div>
                  <div class="mt-6">
                      <p class="mb-2">Search Manta's Directory to find the Small Business you're looking for</p>
            <a data-test="btn-index-footer-desktop" class="bg-primary-v1 py-2 px-4 rounded text-white inline-block hover:font-bold" href="/">Find a Business Near You</a>
                  </div>
      </div>
    </div>
    <div class="lg:hidden border-b border-t border-primary-light-v1 py-4 my-6 w-full">
      <div class="flex text-primary-light-v1">
        <a data-test="btn-help-footer-desktop-mobile" href="/contact" class="mr-6">Help</a>
        <a data-test="btn-termsConditions-footer-mobile" href="/terms-and-conditions" class="mr-6">Terms</a>
        <a data-test="btn-privacyPolicy-footer-mobile" href="/privacy-policy" class="mr-6">Privacy</a>
      </div>
    </div>
    <div class="flex lg:hidden justify-between items-center w-full">
      <div class="text-primary-light-v1 xs-device">
        © 2025 Manta Media Inc.<br>All rights reserved.
              </div>
      <div class="flex">
        <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
            <span class="sr-only">Manta on Twitter</span>
          </span>
        </a>
        <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
            <span class="sr-only">Manta on Facebook</span>
          </span>
        </a>
        <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
            <span class="sr-only">Manta on LinkedIn</span>
          </span>
        </a>
      </div>
    </div>
  </div>
  
      <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script data-cfasync="false" async src="//cdn.intergient.com/1024347/72853/ramp.js"></script>
         </footer>    <script id="lazy-load-images">
  (function(hasObserver) {
    var createObservers = function(list, loadFn) {
      if (hasObserver) {
        var observer = new IntersectionObserver(function(entries, self) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              loadFn(entry.target);
              self.unobserve(entry.target);
            }
          });
        }, { rootMargin: '0px 0px 200px 0px' });

        list.forEach(function(el) {
          observer.observe(el);
        });
      } else {
        list.forEach(loadFn);
      }
    };

    var imgs = document.querySelectorAll('[lazy-load]');
    if (imgs.length) {
      createObservers(imgs, function(el) {
        el.setAttribute('src', el.getAttribute('lazy-load'));
      });
    }

    var bgs = document.querySelectorAll('.lazy');
    if (bgs.length) {
      createObservers(bgs, function(el) {
        el.classList.remove('lazy');
      });
    }
  })(typeof IntersectionObserver !== 'undefined');
</script>
    <script>
  const createCompany = company => {
    return (`
    <div class="flex items-start justify-between">
      <div>
        <div class="border border-gray-200 store-logo rounded">
          ${
            company.logo ? `<div class="bg-no-repeat bg-center bg-contain" style="background-image: url(${company.logo})"></div>`
            : `<div class="hidden md:block">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400" style="font-size: 4rem"></i>
            </div>
          </div>
          <div class="md:hidden">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400 text-6xl"></i>
            </div>
          </div>`
          }
        </div>
      </div>
      <div class="flex flex-grow justify-center">
        <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
          <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
            <a data-test="mb-result-card-title-${company.emid}" href="${company.url}" class="cursor-pointer font-serif text-gray-900 mr-2 break-words">${company.name}</a>
            ${company.claimStatus === 'PBL' 
            ? `<div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>`
            : company.claimStatus === 'CLAIMED'
            ? `<div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>`
            : ''
            }
          </div>
          ${company.recommendations.averageRating.ratingCount > 0
          ? `<div class="flex items-center">
              <div class="text-primary-v1 font-bold mr-4">${company.recommendations.averageRating.ratingValue}</div>
              {stars({ stars: ${company.recommendations.averageRating.ratingValue} })/}
              <div class="ml-4">(${company.recommendations.averageRating.ratingCount})</div>
            </div>` 
          : ''}
          <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
              ${company.location.address !== undefined ? `<div class="hidden md:block">${company.location.address}</div>` : ''}
              ${company.location.country && company.location.country !== 'United States' 
              ? `<div>${company.location.city}, ${company.location.country}</div>`
              : `<div>${company.location.city}, ${company.location.stateAbbrv}</div>`}
            </div>
          </div>
          ${company.contactInfo.phone ? `<div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>${company.contactInfo.phone}</div>
          </div>` : ''}
          ${company.contactInfo.website ? `<div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="${company.contactInfo.website}" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>` : ''}
          ${company.contactInfo.phone || company.contactInfo.website || company.location.address 
            ? `
            <div class="flex md:hidden mt-3">
              ${company.contactInfo.phone ? `<a href="tel:${company.contactInfo.phone}" rel="nofollow noopener"class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
              ${company.location.address ? `
                <a href="${`https://maps.google.com/maps?q=${encodeURIComponent(company.name)},` + '+' + `${encodeURIComponent(company.location.address)}` + '+' + `${encodeURIComponent(company.location.city)}`}" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                ` : ''}
                ${company.contactInfo.website ? `
                  <a href="${company.contactInfo.website}" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
            </div>` 
            : ''}
        </div>
        <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
          <div class="flex rounded w-full mb-4">
            <div class="flex justify-between w-full">
              <div class="flex justify-between items-center">
                ${company.claimStatus === 'PBL' 
                ? `
                <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
                  <i class="text-white fa fa-badge-check"></i>
                </div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                  MANTA VERIFIED
                </div>` 
                : company.claimStatus === 'CLAIMED'
                ? `<div class="mt-1 rounded-l bg-success px-2">
                    <i class="text-xs text-white fa fa-check"></i>
                  </div>
                  <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                    CLAIMED
                  </div>`
                : ''
                }
                ${company?.serviceAreasString && `<p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving ${company.serviceAreasString.split(',')[0]} and the Surrounding Area</p>`}
              </div>
            </div>
          </div>
          ${company.products.productTerms.list.length > 0 
            ? company.products.productTerms.list.forEach(term => {
              return `
                <div class="flex items-baseline mb-1">
                  <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
                  <span class="text-gray-800">${term.name}</span>
                </div>
              `
            })
            : `<div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under ${company.industry.sicm.description}</span>
          </div>`}
        </div>
      </div>
    </div>
    <div>
    ${company.detailedDescription 
      ? `<div class="hidden lg:block">
    <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">${company.detailedDescription}</div>
    </div>` 
      : ''}
    </div>
    `);
  }
</script>    <script>

  const trigger = document.querySelector('#infinite-scroll-trigger');
  const loader = document.querySelector('#loading-more-content');
  const results = document.querySelector('#more-results');

  const config = {
    root: null,
    threshold: 0.1,
    rootMargin: '0px'
  };

  let search = 2;

  const sicAndGeoLastIndex = window.location.pathname.lastIndexOf('/');
  const sicAndGeoFirstIndex = window.location.pathname.indexOf('_');
  const sicAndGeo = window.location.pathname.slice(sicAndGeoFirstIndex + 1, sicAndGeoLastIndex);

  const observer = new IntersectionObserver(async (entries, self) => {
    try {
      const [entry] = entries;
      if (!entry.isIntersecting) return;
      loader.classList.toggle('hidden');
      const response = await axios.get(`/more-results/${sicAndGeo}?pg=${search}`, {
        headers: { 'x-request-id': '9971611f18f0c5234354407ecf84979a' }
      });
      if (response.data.companies.list.length === 0) {
        self.unobserve(entry.target);
      }
      loader.classList.toggle('hidden');
      let companies = [];
      response.data.companies.list.forEach((company) => {
        const resultContainer = document.createElement('div');
        resultContainer.setAttribute('data-test', `mb-result-card-${company.emid}`);
        resultContainer.classList.add('md:rounded', 'bg-white', 'border-b', 'border-primary-light-v1', 'px-3', 'py-4', 'md:p-8', 'md:mt-4', 'mx-4', 'xl:mx-0');
        resultContainer.innerHTML = createCompany(company);
        companies.push(resultContainer);
      })
      results.append(...companies);
      search++;
    } catch(error) {
      loader.classList.toggle('hidden');
      self.unobserve(entry.target);
    }
  });

  observer.observe(trigger, config);

</script>  </body>
</html>
