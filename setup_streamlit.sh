#!/bin/bash

# Business Finder System - Streamlit Setup Script
# Automated installation and configuration for the web interface

set -e  # Exit on any error

echo "🔍 Business Finder System - Streamlit Web Interface Setup"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Python 3.9+ is available
check_python() {
    print_info "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        REQUIRED_VERSION="3.9"
        
        if python3 -c "import sys; exit(0 if sys.version_info >= (3,9) else 1)"; then
            print_status "Python $PYTHON_VERSION found (compatible)"
            PYTHON_CMD="python3"
        else
            print_error "Python $PYTHON_VERSION found (requires 3.9+)"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.9 or higher."
        exit 1
    fi
}

# Check if pip is available
check_pip() {
    print_info "Checking pip availability..."
    
    if command -v pip3 &> /dev/null; then
        print_status "pip3 found"
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        print_status "pip found"
        PIP_CMD="pip"
    else
        print_error "pip not found. Please install pip."
        exit 1
    fi
}

# Create virtual environment
setup_venv() {
    print_info "Setting up virtual environment..."
    
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        print_status "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    print_status "Virtual environment activated"
    
    # Upgrade pip
    pip install --upgrade pip
    print_status "pip upgraded"
}

# Install requirements
install_requirements() {
    print_info "Installing Python packages..."
    
    # Install main requirements first
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_status "Main requirements installed"
    else
        print_warning "requirements.txt not found, skipping main requirements"
    fi
    
    # Install Streamlit requirements
    if [ -f "requirements_streamlit.txt" ]; then
        pip install -r requirements_streamlit.txt
        print_status "Streamlit requirements installed"
    else
        print_error "requirements_streamlit.txt not found"
        exit 1
    fi
}

# Check system files
check_system_files() {
    print_info "Checking system files..."
    
    REQUIRED_FILES=(
        "main.py"
        "config.yaml"
        "streamlit_app.py"
        "run_streamlit.py"
        "src/utils/data_processor.py"
        "src/models/scraping_result.py"
    )
    
    MISSING_FILES=()
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            MISSING_FILES+=("$file")
        fi
    done
    
    if [ ${#MISSING_FILES[@]} -eq 0 ]; then
        print_status "All required system files found"
    else
        print_error "Missing required files:"
        for file in "${MISSING_FILES[@]}"; do
            echo "   - $file"
        done
        exit 1
    fi
}

# Create directories
create_directories() {
    print_info "Creating required directories..."
    
    DIRECTORIES=(
        "results"
        "logs"
        "cache"
    )
    
    for dir in "${DIRECTORIES[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        fi
    done
}

# Setup demo environment
setup_demo() {
    print_info "Setting up demo environment..."
    
    if [ -f "demo_streamlit.py" ]; then
        $PYTHON_CMD demo_streamlit.py
        print_status "Demo environment configured"
    else
        print_warning "demo_streamlit.py not found, skipping demo setup"
    fi
}

# Test installation
test_installation() {
    print_info "Testing installation..."
    
    # Test Python imports
    $PYTHON_CMD -c "
import streamlit
import pandas
import plotly
import yaml
print('✅ All imports successful')
"
    
    if [ $? -eq 0 ]; then
        print_status "Installation test passed"
    else
        print_error "Installation test failed"
        exit 1
    fi
}

# Main setup function
main() {
    echo
    print_info "Starting setup process..."
    echo
    
    # Run setup steps
    check_python
    check_pip
    setup_venv
    install_requirements
    check_system_files
    create_directories
    setup_demo
    test_installation
    
    echo
    print_status "Setup completed successfully!"
    echo
    print_info "🚀 To start the Business Finder Web Interface:"
    echo "   source venv/bin/activate"
    echo "   python run_streamlit.py"
    echo
    print_info "🌐 The interface will be available at:"
    echo "   http://localhost:8501"
    echo
    print_info "📚 For more information, see:"
    echo "   - STREAMLIT_README.md"
    echo "   - TECHNICAL_ANALYSIS.md"
    echo
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "Business Finder System - Streamlit Setup Script"
        echo
        echo "Usage: $0 [options]"
        echo
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --demo         Setup demo environment only"
        echo "  --test         Test installation only"
        echo
        exit 0
        ;;
    --demo)
        setup_demo
        exit 0
        ;;
    --test)
        test_installation
        exit 0
        ;;
    *)
        main
        ;;
esac
