"""
Test cases for business owner scrapers.
"""

import unittest
from unittest.mock import Mo<PERSON>, patch, MagicMock
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.core import <PERSON>raping<PERSON>ng<PERSON>, Scraping<PERSON><PERSON>ult
from src.scrapers.bbb_scraper import BBB<PERSON>craper
from src.scrapers.manta_scraper import MantaScraper
from src.scrapers.linkedin_scraper import LinkedInScraper
from src.utils.data_processor import DataProcessor


class TestScrapingEngine(unittest.TestCase):
    """Test cases for the core scraping engine."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'general': {
                'max_workers': 2,
                'request_delay': 1,
                'timeout': 10
            },
            'anti_bot': {
                'use_proxies': False,
                'rotate_user_agents': True
            },
            'proxies': {
                'http_proxies': [],
                'socks_proxies': []
            }
        }
        
        with patch('src.core.ScrapingEngine._load_config', return_value=self.config):
            self.engine = ScrapingEngine()
    
    def test_engine_initialization(self):
        """Test engine initializes correctly."""
        self.assertIsNotNone(self.engine.session)
        self.assertIsNotNone(self.engine.cloudscraper_session)
        self.assertIsNotNone(self.engine.user_agent)
    
    def test_get_user_agent(self):
        """Test user agent generation."""
        user_agent = self.engine._get_user_agent()
        self.assertIsInstance(user_agent, str)
        self.assertGreater(len(user_agent), 10)
    
    @patch('requests.Session.request')
    def test_make_request(self, mock_request):
        """Test HTTP request making."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<html>Test</html>"
        mock_request.return_value = mock_response
        
        response = self.engine.make_request("http://example.com")
        
        self.assertIsNotNone(response)
        self.assertEqual(response.status_code, 200)
        mock_request.assert_called_once()


class TestBBBScraper(unittest.TestCase):
    """Test cases for BBB scraper."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'sources': {
                'bbb': {
                    'enabled': True,
                    'base_url': 'https://www.bbb.org',
                    'max_pages': 2
                }
            }
        }
        
        with patch('src.core.ScrapingEngine._load_config', return_value=self.config):
            self.engine = ScrapingEngine()
            self.scraper = BBBScraper(self.engine)
    
    def test_scraper_initialization(self):
        """Test scraper initializes correctly."""
        self.assertEqual(self.scraper.source_name, 'bbb')
        self.assertTrue(self.scraper.is_enabled())
        self.assertEqual(self.scraper.base_url, 'https://www.bbb.org')
    
    def test_build_search_url(self):
        """Test search URL building."""
        url = self.scraper.build_search_url("lawn care", "dallas tx")
        
        self.assertIn("bbb.org", url)
        self.assertIn("lawn+care", url)
        self.assertIn("dallas", url)
    
    def test_clean_name(self):
        """Test name cleaning functionality."""
        test_cases = [
            ("John Smith", "John Smith"),
            ("  John   Smith  ", "John Smith"),
            ("Mr. John Smith", "John Smith"),
            ("John Smith Jr.", "John Smith"),
            ("john smith", "John Smith"),
            ("JOHN SMITH", "John Smith")
        ]
        
        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = self.scraper.clean_text(input_name)
                # Note: clean_text is a basic implementation, 
                # more sophisticated cleaning is in DataProcessor
                self.assertIsInstance(result, str)
    
    @patch('bs4.BeautifulSoup')
    def test_extract_business_name(self, mock_soup):
        """Test business name extraction."""
        mock_soup_instance = Mock()
        mock_soup_instance.select_one.return_value = Mock()
        mock_soup_instance.select_one.return_value.get_text.return_value = "Test Business Inc."
        mock_soup.return_value = mock_soup_instance
        
        business_name = self.scraper._extract_business_name(mock_soup_instance)
        self.assertEqual(business_name, "Test Business Inc.")


class TestMantaScraper(unittest.TestCase):
    """Test cases for Manta scraper."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'sources': {
                'manta': {
                    'enabled': True,
                    'base_url': 'https://www.manta.com',
                    'max_pages': 2
                }
            }
        }
        
        with patch('src.core.ScrapingEngine._load_config', return_value=self.config):
            self.engine = ScrapingEngine()
            self.scraper = MantaScraper(self.engine)
    
    def test_build_search_url(self):
        """Test Manta search URL building."""
        url = self.scraper.build_search_url("restaurant", "houston tx")
        
        self.assertIn("manta.com", url)
        self.assertIn("restaurant", url)
        self.assertIn("houston", url)


class TestLinkedInScraper(unittest.TestCase):
    """Test cases for LinkedIn scraper."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'sources': {
                'linkedin': {
                    'enabled': True,
                    'base_url': 'https://www.linkedin.com',
                    'max_pages': 2
                }
            }
        }
        
        with patch('src.core.ScrapingEngine._load_config', return_value=self.config):
            self.engine = ScrapingEngine()
            self.scraper = LinkedInScraper(self.engine)
    
    def test_build_search_queries(self):
        """Test LinkedIn search query building."""
        queries = self.scraper._build_search_queries("construction", "austin tx")
        
        self.assertIsInstance(queries, list)
        self.assertGreater(len(queries), 0)
        
        for query in queries:
            self.assertIn("linkedin.com", query)
            self.assertIn("construction", query)
            self.assertIn("austin", query)
    
    def test_extract_name_from_title(self):
        """Test name extraction from LinkedIn titles."""
        test_cases = [
            ("John Smith - CEO at Test Company | LinkedIn", "John Smith"),
            ("Jane Doe - Owner at Jane's Business | LinkedIn", "Jane Doe"),
            ("Bob Johnson at Johnson Construction | LinkedIn", "Bob Johnson"),
            ("Invalid Title", ""),
            ("", "")
        ]
        
        for title, expected in test_cases:
            with self.subTest(title=title):
                result = self.scraper._extract_name_from_title(title)
                self.assertEqual(result, expected)


class TestDataProcessor(unittest.TestCase):
    """Test cases for data processing and deduplication."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'output': {
                'deduplication': {
                    'enabled': True,
                    'similarity_threshold': 0.8,
                    'key_fields': ['owner_name', 'business_name']
                }
            }
        }
        self.processor = DataProcessor(self.config)
    
    def test_clean_name(self):
        """Test name cleaning."""
        test_cases = [
            ("john smith", "John Smith"),
            ("  JANE   DOE  ", "Jane Doe"),
            ("Mr. Bob Johnson Jr.", "Bob Johnson"),
            ("Dr. Sarah Wilson PhD", "Sarah Wilson"),
            ("McDonald", "McDonald"),  # Should preserve McDonald
            ("o'connor", "O'Connor")   # Should fix O'Connor
        ]
        
        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = self.processor._clean_name(input_name)
                self.assertEqual(result, expected)
    
    def test_clean_business_name(self):
        """Test business name cleaning."""
        test_cases = [
            ("Test Company Inc", "Test Company Inc."),
            ("ABC Corp", "ABC Corp."),
            ("XYZ LLC", "XYZ LLC"),
            ("The Best Company", "Best Company"),
            ("  Spaced   Company  ", "Spaced Company")
        ]
        
        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = self.processor._clean_business_name(input_name)
                self.assertEqual(result, expected)
    
    def test_clean_phone(self):
        """Test phone number cleaning."""
        test_cases = [
            ("1234567890", "(*************"),
            ("(*************", "(*************"),
            ("************", "(*************"),
            ("************", "(*************"),
            ("***********", "(*************"),  # With country code
            ("invalid", "invalid")  # Should return original if can't format
        ]
        
        for input_phone, expected in test_cases:
            with self.subTest(input_phone=input_phone):
                result = self.processor._clean_phone(input_phone)
                self.assertEqual(result, expected)
    
    def test_string_similarity(self):
        """Test string similarity calculation."""
        test_cases = [
            ("John Smith", "John Smith", 1.0),
            ("John Smith", "john smith", 1.0),  # Case insensitive
            ("John Smith", "Jon Smith", 0.9),   # High similarity
            ("John Smith", "Jane Doe", 0.0),    # Low similarity
            ("", "", 0.0),                      # Empty strings
            ("Test", "", 0.0)                   # One empty
        ]
        
        for str1, str2, expected_min in test_cases:
            with self.subTest(str1=str1, str2=str2):
                result = self.processor._string_similarity(str1, str2)
                if expected_min == 1.0:
                    self.assertEqual(result, expected_min)
                elif expected_min == 0.0 and (not str1 or not str2):
                    self.assertEqual(result, expected_min)
                else:
                    self.assertGreaterEqual(result, expected_min)
    
    def test_validate_results(self):
        """Test result validation."""
        valid_result = ScrapingResult(
            owner_name="John Smith",
            business_name="Test Company",
            source="test"
        )
        
        invalid_result1 = ScrapingResult(source="test")  # No name or business
        invalid_result2 = ScrapingResult(owner_name="X", source="test")  # Too short
        invalid_result3 = ScrapingResult(owner_name="123", source="test")  # Invalid chars
        
        results = [valid_result, invalid_result1, invalid_result2, invalid_result3]
        validated = self.processor._validate_results(results)
        
        self.assertEqual(len(validated), 1)
        self.assertEqual(validated[0].owner_name, "John Smith")


if __name__ == '__main__':
    # Create test directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    
    unittest.main()
