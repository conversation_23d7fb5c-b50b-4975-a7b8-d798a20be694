<!DOCTYPE html>
<html lang="en">
  <head><script>(function(w,i,g){w[g]=w[g]||[];if(typeof w[g].push=='function')w[g].push(i)})
(window,'GTM-R7B4','google_tags_first_party');</script><script>(function(w,d,s,l){w[l]=w[l]||[];(function(){w[l].push(arguments);})('set', 'developer_id.dY2E1Nz', true);
		var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s);j.async=true;j.src='/pxhh/';
		f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer');</script>
      <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Consumer Services | Dallas, TX - Manta.com</title>
<link rel="icon" href="https://cc3.manta-r3.com/assets-gz/2e2a97f56/img/favicon.png" type="image/png">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com">
<link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/app.css">
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css"></noscript>
<style>
  iframe[name=d_ifrm] {
  display: none;
  }
</style>
        <script rel="gtm-render">
      var screenWidth = screen.width;
      var sSz = 'lg';
      var wsSz = 'lg';
      var wSz = 'lg';
      if (screenWidth < 768) {
        sSz = wsSz = wSz = 'xs';
      } else if (screenWidth < 992) {
        sSz = wsSz = wSz = 'sm';
      } else if (screenWidth < 1200) {
        sSz = wsSz = wSz = 'md';
      }

      var gtmData = {
        ua_property: "UA-10299948-11",
        googleExperimentId: "",
        googleExperimentVariation: "-1",
        pageTitle: "Consumer Services | Dallas, TX - Manta.com",
        page_type: "company-content", // TODO: make this work when we do search/browse
        is_pagespeed: Boolean('false'),

        
        visitor_id: "d62401ef-094c-475a-adbe-e3b0cc313fd3",
        customer_segment: "consumer",
        page_depth: "1",
        scr_win_width: sSz + '-' + wSz,

        
        treatment: "no-test",

                  altTreatment1: "LM $49 Price Test CONTROL",
                  altTreatment2: "",
                  altTreatment3: "",
        
        ip: "*************",

                  // Older cookies might not have stateAbbrv and countryAbbrv,
          // so fall back to state and country
          user_state: "null",
          user_country: "RU",
        
        url_hash: window.location.hash,
        timestamp: new Date().toString(),
        sbi: "false",
        statusCode: "200",  // TODO: actual status
      };

              var dataLayer = [gtmData];
      
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-R7B4');
    </script>
    <script>
      var gaTrack = function (category, action, label, value, interactive) {
        if (arguments.length === 1) {
          // Non-event tracking stuff
          dataLayer.push(category);
          return;
        } else if (arguments.length === 3) {
          value = 1;
          interactive = true;
        }
        if (arguments.length === 4) {
          if (typeof value === 'boolean') {
            interactive = value;
            value = 1;
          } else {
            interactive = true;
          }
        }

        // if label is an object, serialize it into name=value pairs
        if (typeof label === 'object' && !Array.isArray(label)) {
          var pairs = [];
          for (var key in label) {
            pairs.push(key + '=' + label[key]);
          }
          label = pairs.join(',');
        }

        dataLayer.push({
          eventData: {
            category: category,
            action: action,
            label: label,
            value: value
          },
          event: (interactive ? 'interactive' : 'non-interactive') + ' event'
        });
      };
    </script>
  <script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/cash.min.js"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/js.cookie.min.js" onload="Cookies.defaults = { path: '/', domain: '.manta.com' }"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/axios.min.js" onload="window.axios = window.redaxios"></script>
<script>var loadScript=(function(d,s,c,e,f){return function(u){if(c[u]){return c[u];}e=d.createElement(s);e.async=!0;e.src=u;f=d.getElementsByTagName(s)[0];f.parentNode.insertBefore(e,f);return c[u]=e;};})(document,'script',{});</script>
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css"></noscript>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/simpleLightbox.min.js"></script>
<script>
  window.__PRELOADED_STATE__ = {
    toggledFeatures: {"experimentId":"","experimentVariation":"-1","forceGzip":false,"adsenseChannel":"1","scrollTracking":false,"exitTracking":true,"fakeMixpanel":true,"fakeGa":false,"useSubscriptionTestMode":false,"yextAllowDuplicatesMode":false,"allowSurveys":true,"isOffHoursOverride":false,"isBusinessHoursOverride":false,"skipDittoVendorRequests":false,"marketplaceJobs":false,"claimCtaText":"Own This Business?","claimCtaColor":"btn-primary","useLongBundleDesc":false,"isSiteMaintenanceMode":false,"homeyouLeads":false,"updoptProdOverride":false,"enableUpdoptDossier":true,"adsenseSlotMobile":"1160948055","adsenseSlotDesktop":"5730748454","useFapiForSubscriptions":false,"popOverlayOnCheckout":true,"useBuilderToAdd":false,"checkoutTemplate":"checkout","buyerlink_treatmentA":true,"adsenseHeroPlacement":true,"refreshAds":false,"show300x250":true,"showTaboola":true,"showDesktopAdhesionBanner":true,"homeyouWidgetPlacement":"about","newStack":true,"similarBusinessesUp":true,"reducedAdDensity":true,"navAffiliateBusinessCreditAd":true,"trackNumbers":true,"homeyouNumberTest":true,"homeyouTrackingNumber":"+***********","unclaimedStatic":true,"includeGMBScan":true,"postponeMerchantScans":true,"embedYextDashboard":true,"showPaywallPage":false,"checkoutPaywallTreatment":"paywall-control","enableWebsiteAddOn":true,"useBriteVerify":true,"useGooglePlacesInClaimBuilder":true,"useGoogleMaps":true,"useGoogleAutocomplete":true,"enableFacebookSignIn":true,"enableGoogleSignIn":true,"logCookieErrors":true,"rightRailDoubleAds":true,"triggerDemoScheduler":true,"showCovid":true,"covidShow":true,"es_search":true,"es_related":true,"useNewMemberDashboard":true,"showDetailedDescription":true,"useInternationalSearch":true,"useNewEditPage":true,"useElasticMb":true,"showMarketStats":false,"useNewAnalyticsService":true,"useElasticWorld":true,"requireTermsOfService":true,"useTaboolaAds":false,"usePlaywire":true,"adSenseSearchPages":true,"adSenseProfilePages":true,"oldSurveyModal":true,"useRepSalesDashboard":true,"blockSICM":true,"useNewCheckout":true,"showBanner":false,"copyTextBanner":"Promo Banner","redeemByTextBanner":"Redeem by 19th march","expiryDateBanner":"03/18/2023","catchPhraseBanner":"Special Offer!","validForBanner":"hasDitto, hasLmReviews, hasWebsite, hasPpcAds, hasDisplayAds, hasFeaturelessPlan, freeUser","toggleUrlCheckout":true,"changeVersion3":true,"showReviewTile":false,"showAdapexAds":true},
    referral_source: '',
    abTreatment: 'no-test',
    gamNetworkCode: '6009',
    visitor: {"ip":"*************","id":"d62401ef-094c-475a-adbe-e3b0cc313fd3","pageDepth":1,"customerSegment":{"threshold":1,"id":"c","label":"consumer"},"smallBusinessInterest":false,"xri":"369446ff1eb940be6de518f3e72a112e"},
    pageComponents: {},
    clientIp: '*************',
    isCalifornia: false,
    isDev: false,
    env: 'production',
    member: undefined  };
</script>
<script>
  var cache = {};
  window.logError = function(e, info) {
    var lines = (e.stack || '').split('\n');
    var callsite = lines.length > 1 ? lines[1].match(/(app\.js:\d+:\d+)/) : null;
    var key = e.message + (callsite && callsite[1] ? ' at ' + callsite[1] : '');

    if (!cache[key]) {
      try {
        window.axios && axios.post('/fapi/errors', {
          message: e.message || 'Unknown error',
          stack: e.stack || 'No stack trace available',
          info: info,
                    userAgent: (window.navigator && window.navigator.userAgent) || 'unknown'
        }).catch(function() {});
      } catch (e) {
        // Obviously, this isn't async/await, so it won't catch the
        // axios call, but I just want to _assure_ we don't throw
        // from the onerror handler.
      }
    }
  };
  window.onerror = function(message, source, lineno, colno, error) {
    // Don't log errors that come from ads and crap like that
    if (source.indexOf('manta.com') > -1) {
      logError(error, { source: source, lineno: lineno, colno: colno });
    }
  };
</script>
<!-- The script below is going to be commented until we figure out what could be a better implementation talking about performance -->
<!-- <script defer src="//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js" importance="low"></script> --><script defer src="https://btloader.com/tag?o=5150306120761344&upapi=true"></script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>

      <script data-cfasync="false">
        window.ramp = window.ramp || {};
        window.ramp.que = window.ramp.que || [];
      </script>
    
    <script type="text/javascript">
      window.ramp = window.ramp || {};
      window.ramp.que = window.ramp.que || [];
    </script>
          <script>
  if($) {
    $.fn.swapWithNext = function() {
      this.hide();
      this.next().removeClass('hidden');
    }
  }
</script>
  <script>
    var maTrack = (function() { 
      return function(xri, type, listings, event, stopPropagation) {
        if (event && stopPropagation) {
          event.stopPropagation();
        }
        var params = {
          listings: typeof listings === 'string' ? JSON.parse(listings) : listings,
          t: type + (screen.width < 992 ? '_mobile' : ''),
          ts: Date.now(),
          total: listings.length
        };

        var fp = JSON.stringify(params);

        typeof axios === 'function' && axios({
          url: '/track',
          method: 'GET',
          params: { fp: fp },
          withCredentials: true,
          headers: { 'x-request-id': xri }
        }).catch(function(e) {
          logError(e, { trackData: params });
        });
      };
    })();
  </script>
  <script>
    var mantaTrack = (function() {
      var mat = {};

      mat.xri = '369446ff1eb940be6de518f3e72a112e';
      mat.device = screen.width < 992 ? 'mobile' : 'desktop';
                        
      mat.trackView = (function() {
        return function(context) {
          var events = [{
            emid: mat.emid,
            type: 'view',
            data: {
              context: context,
              device: mat.device,
              sicm: mat.sicm,
              city_code: mat.city_code,
              claim_source: mat.claim_source
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackClick = (function() {
        return function(category, context, section) {
          var events = [{
            emid: mat.emid,
            type: 'click',
            data: {
              context: context,
              device: mat.device,
              city_code: mat.city_code,
              sicm: mat.sicm,
              claim_source: mat.claim_source,
              category: category,
              section: section
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackSearch = (function() {
        return function(context, city, sicm, emid) {
          let events = [];
          var values = [{
            emid: emid,
            type: "view",
            data: {
              context: context,
              device: mat.device,
              city: city,
              sicm: sicm
            }
          }];
          values.forEach(val => {
            events.push(val)
          });
          return mantaTrack(events);
        }
      })();
      
      mat.trackAndGo = (function() {
        return function(location, category, context, section) {
          mat.trackClick(category, context, section);
          window.location.href = location;
        }
      })();

      var mantaTrack = (function() {
          return function(events) {
                  return true;
                };
      })();

      return mat;

    })();
  </script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        .store-logo {
          position: relative;
          overflow: hidden;
          width: 100px;
        }
        .store-logo:before {
          content: "";
          display: block;
          padding-top: 100%;
        }
        .store-logo > div {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          text-align: center;
        }
      </style>
  </head>
  <body class="bg-primary-light-v1 text-gray-800">
    <div class="relative">
  <a href="#start-of-content" class="text-xs text-darks-v1 focus:text-white absolute right-100">Skip to Content</a>
</div>
<style>
      .desktop-search-wrapper {
      width: 380px;
    }
    @media(min-width: 1110px) {
      .desktop-search-wrapper {
        width: 480px;
      }
    }
  
</style>

<header>
  <div class="mobile-menu hidden fixed w-screen h-screen bg-white p-4 z-50 overflow-auto">
    <div class="float-right" onclick="$('.mobile-menu').addClass('hidden'); $('body').removeClass('overflow-hidden')"><i class="text-text-darks-v1 text-3xl fa fa-times"></i></div>
    <ul class="text-gray-600 my-16 text-lg">
  <li class="mb-2 hover:font-bold">
    <a href="/services">For Businesses</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/free-business-listing">Free Company Listing</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/listings-management">Premium Business Listings</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/small-business-marketing/websites">Websites</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/organic-seo-company">SEO</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/affordable-local-seo">Local SEO</a>
  </li>
  <li>
    <a href="/services/national-seo-company">National SEO</a>
  </li>
</ul>
      <div class="flex flex-col lg:flex-row">
    <a class="btn bg-primary-light-v1 text-gray-800 font-bold flex-1 mb-4 py-3" href="/member/login">Log In</a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="mb-4 btn bg-primary-v1 text-white inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
      </div>
  </div>

  <div class="mobile-search hidden fixed w-screen h-screen bg-white z-50 overflow-y-scroll">
  <div class="justify-center py-3 mx-auto max-w-header flex items-center bg-darks-v1 px-4 relative">
    <div onclick="$('.mobile-search').addClass('hidden');$('.pre-mobile-search').removeClass('hidden')" class="lg:hidden text-primary-light-v1 cursor-pointer absolute left-0 top-0 mt-6 ml-5">Cancel</div>
    <div class="flex sm:pr-3">
      <a href="/" data-test="btn-logo-navbar">
        <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo" width="109" height="25">
      </a>
    </div>
  </div>
  <div class="bg-darks-v1 text-gray-dark search-component-mobile"></div>
</div>

  <div class="px-6 bg-darks-v1 h-auto text-white">
    <div class="justify-between py-3 mx-auto max-w-header flex items-center">

      <div onclick="$('.pre-mobile-search').addClass('hidden');loadSearchBar('.mobile-search')" class="flex md:hidden"><i class="text-2xl fa fa-search"></i></div>
      <div>
  <a href="/" data-test="btn-logo-navbar">
    <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo"  width="109" height="25" >
    <span class="sr-only">Manta Home</span>
  </a>
</div>
      <div class="hidden md:inline-block desktop-search-wrapper">
        <div class="rounded text-gray-dark hidden md:inline-block search-component w-full">
          <form name="searchForm">
  <div class="flex flex-col sm:flex-row px-3 sm:px-0" style="border-radius: 4px 4px 4px 0px;">
    <div class="flex sm:w-1/2 relative px-3 py-2 bg-white my-1 sm:my-0 rounded sm:rounded-l-lg sm:rounded-r-none">
      <div class="flex justify-center items-center mr-4 w-5">
        <span class="text-primary-v1 fa fa-search text-xl"></span>
      </div>
      <div class="flex w-full">
        <label for="header-search" class="sr-only">Search</label>
        <input
          id="header-search"
          name="search"
          placeholder="I'm looking for..."
          class="w-full outline-none"
          onfocus="loadSearchBar('.search-services-menu')"
          autocomplete="off"
          value=""
        />
      </div>
      <div class="absolute search-services-menu hidden" style="z-index: 10000">
        <ul class="p-0 m-0 text-gray-600">
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-utensils mr-3"></span
            ></span>
            <span class="text-small">Restaurants</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-stethoscope mr-3"></span
            ></span>
            <span class="text-small">Doctors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-gavel mr-3"></span
            ></span>
            <span class="text-small">Lawyers</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-hammer mr-3"></span
            ></span>
            <span class="text-small">Contractors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"><span class="fa fa-car mr-3"></span></span>
            <span class="text-small">Automotive</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-tooth mr-3"></span
            ></span>
            <span class="text-small">Dentists</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex py-2 sm:py-0 sm:w-1/2">
      <div class="flex w-full relative px-3 sm:pl-0 sm:pr-3 py-2 bg-white rounded-l-lg sm:rounded-none">
        <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
        <div class="self-center w-full text-gray-800">
          <label for="header-location" class="sr-only">Location</label>
          <input
            id="header-location"
            name="location"
            placeholder="City, State, Country, Zip"
            class="w-full outline-none"
            onfocus="loadSearchBar('.search-location-menu')"
            autocomplete="off"
            value=""
          />
        </div>
        <div class="search-location-menu hidden" style="z-index: 10000">
          <ul class="m-0 p-0 locations">
            <li
              class="px-4 py-3 text-primary-v1 hover:bg-gray-200 cursor-pointer"
            >
              <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
              <span class="small loc-name">Current Location</span>
            </li>
          </ul>
        </div>
      </div>
      <button
        type="submit"
        class="sm:flex items-center justify-center bg-primary-v1 text-white px-3 py-2 overflow-hidden rounded-r-lg">
        <span class="sr-only">Search</span>
        <span class="not-sr-only text-white fa fa-search text-xl"></span>
      </button>
    </div>
  </div>
</form>
<script>
  (function () {
    var loc = localStorage.getItem("locHistory");
    if (loc) {
      var li = $(
        '<li class="px-4 py-3 text-gray-600 hover:bg-gray-200 cursor-pointer border-t border-gray-300 sm:border-none"><span class="fa fa-clock mr-4"></span><span class="small loc-name">Current Location</span></li>'
      );
      JSON.parse(loc).forEach(function (l) {
        if (l.stateAbbrv) {
          li.find(".loc-name").text(l.formatted);
          $(".locations").append(li.clone());
        }
      });
    }
  })();
  var loadSearchBar = (function () {
    return function (c) {
      $(c).removeClass("hidden");
      loadScript("//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js");
    };
  })();
  $("form[name=searchForm]").on("submit", function (e) {
    e.preventDefault();
    var search = $("input[name=search]").val();
    var locationInput = $("input[name=location]").val().trim();
    if (!locationInput) {
      const lastGeo = Cookies.get('lastGeo');
      if (lastGeo) {
        try {
          const geo = JSON.parse(lastGeo);
          if (geo && geo.city && geo.stateAbbrv) locationInput = `${geo.city}, ${geo.stateAbbrv}`;
        } catch (e) {
          return;
        }
      }
    };

    if (!locationInput) return;

    var device = "desktop";
    if (window.screen.availWidth <= 500) {
      device = "mobile";
    } else if (window.screen.availWidth <= 1024) {
      device = "tablet";
    }
    var parts = locationInput.split(/[, ]+/);
    var state = parts.pop();
    var city = parts.join(" ");
    window.location =
      "/search?search_source=nav&search=" +
      encodeURIComponent(search) +
      "&city=" +
      encodeURIComponent(city) +
      "&state=" +
      encodeURIComponent(state) +
      "&device=" +
      device +
      "&screenResolution=" +
      window.screen.availWidth +
      "x" +
      window.screen.availHeight;
  });
</script>
        </div>
      </div>

      <div class="hidden lg:block text-sm">
        <div data-test="btn-products-navbar-desktop" class="dropdown inline-block py-4 text-primary-light-v1">
          <a data-test="btn-findBusiness-navbar-desktop" class="hover:underline font-bold px-3" href="/services">For Businesses <i class="fa fa-angle-down"></i></a>
          <div class="dropdown-tri"></div>
          <ul class="dropdown-menu py-2 text-nordic-v1">
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/free-business-listing">Free Company Listing</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/listings-management">Premium Business Listings</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/small-business-marketing/websites">Websites</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/organic-seo-company">SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/affordable-local-seo">Local SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/national-seo-company">National SEO</a></li>
          </ul>
        </div>
            <a data-test="btn-login-navbar-desktop" class="hover:underline text-primary-light-v1 font-bold"
    href="/member/login"><span class="lg:px-1 px-3 xl:px-3">Log In</span></a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="px-5 lg:px-2 xl:px-5 py-2 w-auto rounded cursor-pointer text-center  bg-primary-v1 text-white mx-3 lg:ml-3 lg:mr-2 xl:mx-3 inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
        </div>

      <div onclick="$('.mobile-menu').removeClass('hidden');" class="flex lg:hidden"><i class="text-2xl fa fa-bars"></i></div>
    </div>
      </div>

  <div class="pl-0 lg:px-6 bg-primary-dark text-white overflow-x-hidden faded faded-x-primary-dark hidden">
  <div class="py-1 mx-auto max-w-header flex items-center overflow-x-auto">
    <div class="inline-block py-2 text-sm whitespace-no-wrap ml-5">
      <a class="cursor-pointer" href="/mb_33_A6_000/professional_services">Business Services<span class="align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_C4_000/restaurants_and_bars">Food & Beverage<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_B3_000/consumer_services">Consumer Products & Services<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_D0_000/healthcare">Health<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_G4_000/information_technology">Tech & Communications<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer mr-5" href="/mb_33_E6_000/industrial_machinery">Industrial</a>
    </div>
  </div>
</div>
  

</header>
    <main>
      <div class="bg-white w-screen lg:mx-auto pb-8 pt-12 flex flex-row justify-center">
        <div class="flex flex-col w-page mx-4 xl:mx-0">
          <h1 class="text-3xl font-serif text-gray-900">Consumer Services</h1>
          <p>
            <span>Manta has 6,403 businesses under Consumer Services in</span>
                          <span>Dallas, TX</span>
                      </p>
        </div>
      </div>
      <div class="w-screen lg:w-page lg:mx-auto">
                <h1 class="text-2xl mx-4 xl:mx-0 font-serif text-gray-900">All Company Listings</h1>
                  <div data-test="mb-result-card-mkh848h" class="md:rounded bg-white border-b  border-t  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkh848h" href="/c/mkh848h/w-lawther-lock-and-keys" class="cursor-pointer font-serif text-gray-900 mr-2">W Lawther Lock and Keys</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">D4009 W Lawther Dr</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.locksmiths.guru&s=0f4911901599137d1bb3754b73e3b721&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4692107379" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=W%20Lawther%20Lock%20and%20Keys,+D4009%20W%20Lawther%20Dr+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.locksmiths.guru&s=0f4911901599137d1bb3754b73e3b721&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Locks and Locksmiths</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mxcqzy9" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20111203rCIVWKbd4W)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mxcqzy9" href="/c/mxcqzy9/dorrance-development-group" class="cursor-pointer font-serif text-gray-900 mr-2">Dorrance Development Group</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">10935 Estate Lane S278</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=www.dorrancedevelopment.com&s=6b9c590c7abf4e5dbf3c5b086ec4da83&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2148849230" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Dorrance%20Development%20Group,+10935%20Estate%20Lane%20S278+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=www.dorrancedevelopment.com&s=6b9c590c7abf4e5dbf3c5b086ec4da83&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Consulting Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Credit Repair Services</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm0x4gj" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm0x4gj" href="/c/mm0x4gj/shindigz" class="cursor-pointer font-serif text-gray-900 mr-2">Shindigz</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">7575 S Wstmrlnd Road # 1211</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2142890265" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Shindigz,+7575%20S%20Wstmrlnd%20Road%20%23%201211+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Party Planning Services</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx62m16" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx62m16" href="/c/mx62m16/maxwell-heating-and-air" class="cursor-pointer font-serif text-gray-900 mr-2">Maxwell Heating and Air</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">Dallas TX 75204</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9496317859" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Maxwell%20Heating%20and%20Air,+Dallas%20TX%2075204+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Air Conditioning Equipment Repairs</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Were the best Hvac Service in Dallas Tx. We can service all your Hvac, heating service and Air conditioning needs. Our local technicians will help address any issue you may have. We are back by our 100% customer satisfaction program.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm0zykq" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm0zykq" href="/c/mm0zykq/southwest-jewel" class="cursor-pointer font-serif text-gray-900 mr-2">Southwest Jewel</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4340 N Central Expressway</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2145228300" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Southwest%20Jewel,+4340%20N%20Central%20Expressway+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Watch Clock and Jewelry Repairs</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx61qq0" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/201610108yZe4_AnyT)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx61qq0" href="/c/mx61qq0/beautyshopsnearme-com" class="cursor-pointer font-serif text-gray-900 mr-2">BeautyShopsNearMe.Com</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">P.O. BOX 612247</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.BeautyShopsNearMe.Com&s=8a30a52ea21f1199ac69194239942768&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4698443177" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=BeautyShopsNearMe.Com,+P.O.%20BOX%20612247+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.BeautyShopsNearMe.Com&s=8a30a52ea21f1199ac69194239942768&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Beauty Salons</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">BeautyShopsNearMe.Com makes it easy for your to find Hair Salons, Nails Salons, Make Up Artist, and More Near You!  Its Fast, Easy & Free!  We invite all beauty professionals to join our beauty network!  Visit our website to learn more.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkh88b2" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkh88b2" href="/c/mkh88b2/garage-door-repairs-dallas-pkwy" class="cursor-pointer font-serif text-gray-900 mr-2">Garage Door Repairs Dallas Pkwy</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">13350 Dallas Pkwy</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fnytxservices.com%2Four-service%2F&s=d320c842b9e6b34faa8e029c01014171&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2144830161" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Garage%20Door%20Repairs%20Dallas%20Pkwy,+13350%20Dallas%20Pkwy+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fnytxservices.com%2Four-service%2F&s=d320c842b9e6b34faa8e029c01014171&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Garage Door Repairs</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">. Free Service Fee w/Any Repair Offer. 5-Star Reviews & BBB A+ Rated! Call. Open 7 Days. BBB Accredited. Senior Citizen Discount. Highlights: 24 Hour Emergency Service, Same Day Service. Garage Door Spring Repair Online Door Designer Svc Fee Waived w/ Repair</div>
    </div>
  </div>                  <div data-test="mb-result-card-mh1g6ns" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20170710xLX0CsiYhn)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mh1g6ns" href="/c/mh1g6ns/oak-lawn-tx-locksmith-store" class="cursor-pointer font-serif text-gray-900 mr-2">Oak Lawn TX Locksmith Store</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">4.5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:90%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(4)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3802 Cedar Springs Rd (Suite: 695D)</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.OakLawn.Dallas-Locksmith-Store.com&s=547638419c85dfa59ab1ccc94a1287bc&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2144452110" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Oak%20Lawn%20TX%20Locksmith%20Store,+3802%20Cedar%20Springs%20Rd%20(Suite%3A%20695D)+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.OakLawn.Dallas-Locksmith-Store.com&s=547638419c85dfa59ab1ccc94a1287bc&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Locksmith</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Locksmithsequipment And Supplies</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Locksmith Services</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Oak Lawn TX Locksmith Store are Experts in Commercial Locksmith, Residential Locksmith and Auto Locksmith Services and products. Our friendly, knowledgeable technicians and master Locksmiths will be happy to help you today With Any Problems in Oak Lawn, TX Metro area. 
Oak Lawn, TX Locksmith Service.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mx2rr14" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20120731lPecArhuOG)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx2rr14" href="/c/mx2rr14/delta-lockey" class="cursor-pointer font-serif text-gray-900 mr-2">Delta Lockey</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">215 Turin Drive</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2145166206" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Delta%20Lockey,+215%20Turin%20Drive+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">24 Hour Locksmith</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Locksmith Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Security Devices, Locks</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">
</div>
    </div>
  </div>                  <div data-test="mb-result-card-mwkxp4j" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20111014pjGYUiVMQD)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mwkxp4j" href="/c/mwkxp4j/union-s-family-daycare" class="cursor-pointer font-serif text-gray-900 mr-2">Union's Family Daycare</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">9506 Sophora Drive</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=www.unionfamilydaycare.com&s=9aefbe4673980509cd5d28262d43e723&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4699414488" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Union's%20Family%20Daycare,+9506%20Sophora%20Drive+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=www.unionfamilydaycare.com&s=9aefbe4673980509cd5d28262d43e723&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Potty Training</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sign Language Instruction</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">I am a registered Home Daycare Provider that offers quality childcare at an affordable price. I serve 3 meals and 2 snacks a day. I offer a daily curriculum that is age appropriate for the proper development of your child, and I have licensed professional teachers and staff to work with your school age and after school children, a tutor to assist your children with completing hone work assignments. CCA acceptance, American Sign Language taught, Potty training, Pre-School ready and much much more.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkxmkfj" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkxmkfj" href="/c/mkxmkfj/locksmith-24-hours" class="cursor-pointer font-serif text-gray-900 mr-2">Locksmith 24 Hours</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1515 South Buckner Boulevard</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2144524656" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Locksmith%2024%20Hours,+1515%20South%20Buckner%20Boulevard+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Locks and Locksmiths</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm61nrr" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm61nrr" href="/c/mm61nrr/studio-21-salon" class="cursor-pointer font-serif text-gray-900 mr-2">Studio 21 Salon</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">17721 Dallas Parkway # 110</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=www.studiotwentyonesalon.com&s=58d12618528585421c723269cc95fa9f&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9722676364" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Studio%2021%20Salon,+17721%20Dallas%20Parkway%20%23%20110+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=www.studiotwentyonesalon.com&s=58d12618528585421c723269cc95fa9f&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Hair Cut.</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Styles</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Perms</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Hilites</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Facial Services</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">beauty salon</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmccncn" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20190912ZUQNhm96Ma)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmccncn" href="/c/mmccncn/buckner-barber-school" class="cursor-pointer font-serif text-gray-900 mr-2">Buckner Barber School</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1515 South Buckner Boulevard</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.bucknerbarberschool.com&s=dce9a8bf724f7dff8258c26de401a8f2&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2143986416" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Buckner%20Barber%20School,+1515%20South%20Buckner%20Boulevard+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.bucknerbarberschool.com&s=dce9a8bf724f7dff8258c26de401a8f2&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Barber Schools</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Barbering in this generation is taking off and proving extremely profitable for the students who pursue it. For us here at Buckner Barber School we believe that the future is already here. Our mission is to train and prepare students through classroom instructions and daily practical floor training to work confidently and competently as barber professionals after their training.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mt96vww" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20151012lxpobX31Bi)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mt96vww" href="/c/mt96vww/karen-campbell-photography" class="cursor-pointer font-serif text-gray-900 mr-2">Karen Campbell Photography</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2408 Dorrington Drive</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.karencampbellphoto.com&s=30a515bdf8b0c9498672ba4529c841f5&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2142158666" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Karen%20Campbell%20Photography,+2408%20Dorrington%20Drive+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.karencampbellphoto.com&s=30a515bdf8b0c9498672ba4529c841f5&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Portrait Photographers</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx5rccb" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx5rccb" href="/c/mx5rccb/kitty-banks-creative-artistry" class="cursor-pointer font-serif text-gray-900 mr-2">Kitty Banks Creative Artistry</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2928 Main Street,</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:**********" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Kitty%20Banks%20Creative%20Artistry,+2928%20Main%20Street%2C+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Hair Replacement</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Book an appointment with Celebrity Hairstylist & Makeup artist Kitty Banks. She specializes in Certified Brazilian Blowouts, Creative Hair Cuts, Color Correction, Makeup, Hair Extensions and Special Effects Makeup. Call today to book your appointment! 214-713-9482</div>
    </div>
  </div>                  <div data-test="mb-result-card-mvvlmg7" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20101001wrRiAKf06K)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mvvlmg7" href="/c/mvvlmg7/debt-consolidation-dallas" class="cursor-pointer font-serif text-gray-900 mr-2">Debt Consolidation Dallas</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2626 Cole Ave</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fdebtconsolidation-dallas.com&s=17a5d66c54cf78eb7f930d41aa944f17&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2142722557" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Debt%20Consolidation%20Dallas,+2626%20Cole%20Ave+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fdebtconsolidation-dallas.com&s=17a5d66c54cf78eb7f930d41aa944f17&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Debt Reduction Dallas</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Debt Solutions Dallas</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Consolidate Debt Dallas</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Debt Consolidation Loans</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Debt Counseling</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">It is time to think about Debt Consolidation programs, for example Debt Consolidation Dallas. A reliable and efficient debt consolidation program in our company may help you in taking the right step towards becoming debt-free. Debt Consolidation Dallas, TX - Free Debt Consolidation Programs & Services: Manage Your Debt Systematically.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mh1bnwd" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mh1bnwd" href="/c/mh1bnwd/the-finest-barbers-and-beauty-salon" class="cursor-pointer font-serif text-gray-900 mr-2">The Finest Barbers And Beauty Salon</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">12125 Abrams Rd Ste 104</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fthefinestbarbersdallas.com%2F&s=53a7bc9b6789269394bfc55815298309&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9724267765" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=The%20Finest%20Barbers%20And%20Beauty%20Salon,+12125%20Abrams%20Rd%20Ste%20104+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fthefinestbarbersdallas.com%2F&s=53a7bc9b6789269394bfc55815298309&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Barber Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Barber Shop</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Local Barber Shop</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Beauty Salon</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Barberschairs</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">You can have peace of mind knowing that your hair is in one of the most capable pair of hands around. Working with us comes with its advantages!
Local Beauty Salon, Professional Beauty Salon, Men Haircut, Women Hairdo, Local Hair Salon, Affordable Hair Salon Service, Local Barber Shop, Professional Barber Shop, Haircut Service, Affordable Haircut Service, Beauty Salon, Local Barber Shop, Barber Services, Barber Shop, Barbers.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmnhm7j" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmnhm7j" href="/c/mmnhm7j/shoutrageous" class="cursor-pointer font-serif text-gray-900 mr-2">Shoutrageous</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=shoutrageous.com&s=47d9fd92b2840159b9ed457695ffb63b&cb=5615152" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9728490199" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                                  <a href="/urlverify?redirect=shoutrageous.com&s=47d9fd92b2840159b9ed457695ffb63b&cb=5615152" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Positive Influence</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Education</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Hair Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Trains</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mxjg56m" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mxjg56m" href="/c/mxjg56m/salon-6062" class="cursor-pointer font-serif text-gray-900 mr-2">Salon 6062</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">6062 Sherry Lane</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                                  <div class="flex md:hidden mt-3">
                                      <a href="https://maps.google.com/maps?q=Salon%206062,+6062%20Sherry%20Lane+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Beauty Salons</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">this business is closed----please stop this ad and information and number thank you ==owner]]]2013</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk7mb6n" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk7mb6n" href="/c/mk7mb6n/the-good-guys-garage-door-repair" class="cursor-pointer font-serif text-gray-900 mr-2">The Good Guys Garage Door Repair</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3621 Frankford Rd #1137</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fthegoodguysdoor.com&s=4bc6b44e06a62499babcb635508ed0d7&cb=5615157" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4697327999" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=The%20Good%20Guys%20Garage%20Door%20Repair,+3621%20Frankford%20Rd%20%231137+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fthegoodguysdoor.com&s=4bc6b44e06a62499babcb635508ed0d7&cb=5615157" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Garage Door Repairs</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">The Good Guys Garage Door Repair is a TRUE local Texas business serving the Dallas-Fort Worth Metroplex and surrounding areas. We specialize in residential garage door repairs, maintenance, and broken garage door spring replacement. We are committed to the highest quality service while providing a fair affordable price with no games, gimmicks, and or surprises at the end of the work.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmyr6wy" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmyr6wy" href="/c/mmyr6wy/america-debt-resolutions-llc" class="cursor-pointer font-serif text-gray-900 mr-2">America Debt Resolutions LLC</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">13140 Coit Road # 522</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9728844700" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=America%20Debt%20Resolutions%20LLC,+13140%20Coit%20Road%20%23%20522+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Credit and Debt Counseling Services</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mtl80t1" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtl80t1" href="/c/mtl80t1/insurance-services" class="cursor-pointer font-serif text-gray-900 mr-2">Insurance Services</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Any Types Of Cleaning</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm3l47h" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm3l47h" href="/c/mm3l47h/dart-marketing-distributors-inc-" class="cursor-pointer font-serif text-gray-900 mr-2">Dart Marketing, & Distributors, Inc.</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2526 W Mockingbird Lane</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=www.diapermart.net&s=291f06b43a013ab6d1cfad5819f46236&cb=5615157" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2143582059" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Dart%20Marketing%2C%20%26%20Distributors%2C%20Inc.,+2526%20W%20Mockingbird%20Lane+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=www.diapermart.net&s=291f06b43a013ab6d1cfad5819f46236&cb=5615157" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Disposable Products</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mr44wxb" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mr44wxb" href="/c/mr44wxb/at-the-moment-cleaning" class="cursor-pointer font-serif text-gray-900 mr-2">At The Moment Cleaning</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2148376244" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                              </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Cleaning Services</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm6d809" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm6d809" href="/c/mm6d809/balloons-beyond" class="cursor-pointer font-serif text-gray-900 mr-2">Balloons & Beyond</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9728419428" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                              </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Party Planning</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mxfy68d" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/201206120AbsE5M8zp)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mxfy68d" href="/c/mxfy68d/shakar-photography" class="cursor-pointer font-serif text-gray-900 mr-2">Shakar Photography</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4755 Gramercy Oaks Dr # 454</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=www.shakarphotography.com&s=241a6626fa936cf2e8985ecdc2ced9dd&cb=5615158" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                                      <a href="https://maps.google.com/maps?q=Shakar%20Photography,+4755%20Gramercy%20Oaks%20Dr%20%23%20454+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=www.shakarphotography.com&s=241a6626fa936cf2e8985ecdc2ced9dd&cb=5615158" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Wedding Photographers</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mxfr17f" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20161114UiJjP65WRR)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mxfr17f" href="/c/mxfr17f/elmo-paw-patrol-mickey-mouse-kids-party-in-dallas-" class="cursor-pointer font-serif text-gray-900 mr-2">Elmo paw patrol Mickey mouse KIDS PARTY IN DALLAS)</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.itsapartytoday.com&s=f2d7c7301e9158aa830b748f462215a3&cb=5615158" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2143954250" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                                  <a href="/urlverify?redirect=http%3A%2F%2Fwww.itsapartytoday.com&s=f2d7c7301e9158aa830b748f462215a3&cb=5615158" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Mouse Cleaners</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Super Hero</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Elmo</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Costume Character dallas snoppy pokemon</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkh8p8r" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkh8p8r" href="/c/mkh8p8r/walnut-hill-ln-lock-and-keys" class="cursor-pointer font-serif text-gray-900 mr-2">Walnut Hill Ln Lock and Keys</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2661 Walnut Hill Ln, Dallas, TX</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:4692107407" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Walnut%20Hill%20Ln%20Lock%20and%20Keys,+2661%20Walnut%20Hill%20Ln%2C%20Dallas%2C%20TX+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Locks and Locksmiths</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx84nv0" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx84nv0" href="/c/mx84nv0/spotless-industry-" class="cursor-pointer font-serif text-gray-900 mr-2">Spotless Industry </a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2309 Tealford Dr</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2145185201" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Spotless%20Industry%20,+2309%20Tealford%20Dr+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Carpet Cleaning Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Clean Room Cleaning Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Commercial Cleaning</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx4j9z6" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20200211hRN5OPRfZD)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx4j9z6" href="/c/mx4j9z6/premier-locksmith-dallas" class="cursor-pointer font-serif text-gray-900 mr-2">Premier Locksmith Dallas</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">300 N Akard St</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.locksmithindallas.net%2F&s=7a3ccb2c04fbfc13f8f863e5ad4fcf74&cb=5615158" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2149227011" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Premier%20Locksmith%20Dallas,+300%20N%20Akard%20St+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.locksmithindallas.net%2F&s=7a3ccb2c04fbfc13f8f863e5ad4fcf74&cb=5615158" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Safe Opening</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Keyless Remote</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Home Lockout</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Rekey</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">24 Hour Locksmith</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">If you can't think of a Dallas locksmith who can handle your most challenging home locksmith issue, you can always turn to the efficient and always reliable services of Locksmith in Dallas. Locksmith in Dallas has been offering our services for many years in Dallas, Texas. When homeowners want affordable services, they know they can find it at Locksmith in Dallas. In addition to our residential locksmith services, we also offer commercial and automotive locksmith services. We have a team of expertly trained and experienced locksmith technicians who have been able to solve many locksmith issues. They have received their training in different areas of the locksmith industry, thus making it possible for them to address any type of locksmith issue.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm04vhp" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20151012hzb4hmq13Z)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm04vhp" href="/c/mm04vhp/specialized-electronic-service-llc" class="cursor-pointer font-serif text-gray-900 mr-2">Specialized Electronic Service LLC</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">10890 Alder Circle</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9726809210" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Specialized%20Electronic%20Service%20LLC,+10890%20Alder%20Circle+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Industrial Equipment Services</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm63xwt" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm63xwt" href="/c/mm63xwt/a-1-lock-doc" class="cursor-pointer font-serif text-gray-900 mr-2">A-1 Lock Doc</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">13330 Meandering Way</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9729342450" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=A-1%20Lock%20Doc,+13330%20Meandering%20Way+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Locks and Locksmiths</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mxfzhlr" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20120702QI5J6kZrlz)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mxfzhlr" href="/c/mxfzhlr/city-locksmith-dallas" class="cursor-pointer font-serif text-gray-900 mr-2">City Locksmith Dallas</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2603 Oak Lawn Avenue</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.citylocksmithdallastx.com%2F&s=caefb86680930b4b7281128f528e6ef0&cb=5615158" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2146354701" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=City%20Locksmith%20Dallas,+2603%20Oak%20Lawn%20Avenue+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.citylocksmithdallastx.com%2F&s=caefb86680930b4b7281128f528e6ef0&cb=5615158" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">City Locksmith Dallas</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">The locksmiths in city locksmith Dallas are the best locksmiths you can find when it comes to the security of your house, car or business. The two things we are mostly proud of in city locksmith Dallas locksmith are the great service and the good prices we provide to our customers. Our locksmiths at city locksmith Dallas  work 24/7, every day in order to be available to every customer in need. Not only that we, at city locksmith Dallas pick the finest locksmiths to work for us, but we also want to make sure that they are skilled and qualified to deal with any kind of locking situation and emergency lockout and with any kind of lock that exists. city locksmith Dallas puts most of its resources in the training of its locksmiths in order to make sure that the service they provide is good and </div>
    </div>
  </div>                  <div data-test="mb-result-card-mtb9fc5" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtb9fc5" href="/c/mtb9fc5/le-noyle" class="cursor-pointer font-serif text-gray-900 mr-2">Le'noyle</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=www.thelenoyle.com&s=7fb96422143288b86b6a3a18772bf90d&cb=5615158" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2144316720" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                                  <a href="/urlverify?redirect=www.thelenoyle.com&s=7fb96422143288b86b6a3a18772bf90d&cb=5615158" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Errand Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Concierge Service</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Event Business Management Software</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Personal Data Assistants</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Personal Shopping Services</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm01knb" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm01knb" href="/c/mm01knb/joseph-s-furniture-care-inc" class="cursor-pointer font-serif text-gray-900 mr-2">Joseph's Furniture Care Inc</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1339 East Levee St</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2147481907" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Joseph's%20Furniture%20Care%20Inc,+1339%20East%20Levee%20St+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Upholsterers</span>
          </div>
              </div>
    </div>
  </div>
  </div>                <div id="more-results">
        </div>
        <div data-test="mb-infinite-scroll-trigger" id="infinite-scroll-trigger"></div>
        <div data-test="mb-spinner-loader" id="loading-more-content" class="hidden my-8">
          <img class="mx-auto" width="100" height="100" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/spinner.svg" />
        </div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb_51_ALL_KCQ/dallas_tx">Companies</a>
                              <span class="mx-1 fa fa-angle-right"></span>
                          </span>
                      <span>
              <a class="hover:underline" href="/mb_53_B3_KCQ/consumer_services/dallas_tx">Consumer Services</a>
                          </span>
                  </div>
        <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse Subcategories</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B3209_KCQ/automobile_parking/dallas_tx">Automobile Parking</a> (34)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30F1_KCQ/barber_shops/dallas_tx">Barber Shops</a> (270)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30E7_KCQ/beauty_shops/dallas_tx">Beauty Shops</a> (1,227)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30D9_KCQ/carpet_and_upholstery_cleaning/dallas_tx">Carpet and Upholstery Cleaning</a> (179)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B3229_KCQ/cemetery_subdividers_and_developers/dallas_tx">Cemetery Subdividers and Developers</a> (16)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B315F_KCQ/child_day_care_services/dallas_tx">Child Day Care Services</a> (283)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B33C3_KCQ/direct_selling_establishments/dallas_tx">Direct Selling Establishments</a> (127)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30D8_KCQ/dry_cleaning_plants/dallas_tx">Dry Cleaning Plants</a> (55)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B33DD_KCQ/firewoord_and_coal_for_sale/dallas_tx">Firewoord and Coal For Sale</a> (4)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B33D7_KCQ/fuel_oil_dealers/dallas_tx">Fuel Oil Dealers</a> (17)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30D4_KCQ/garment_pressing_and_agents_for_laundries_and_drycleaners/dallas_tx">Garment Pressing, and Agents for Laundries and Drycleaners</a> (73)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30D7_KCQ/laundromats_and_drycleaners/dallas_tx">Laundromats and Drycleaners</a> (50)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30DB_KCQ/laundry_services/dallas_tx">Laundry Services</a> (46)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30D5_KCQ/linen_supply/dallas_tx">Linen Supply</a> (11)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B33D8_KCQ/liquefied_petroleum_gas_bottled_gas_dealers/dallas_tx">Liquefied Petroleum Gas (Bottled Gas) Dealers</a> (5)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B312B_KCQ/miscellaneous_personal_services_nec/dallas_tx">Miscellaneous Personal Services, NEC</a> (1,185)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B310F_KCQ/mobile_home_dealers/dallas_tx">Mobile Home Dealers</a> (4)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30DD_KCQ/photographic_studios_portrait/dallas_tx">Photographic Studios, Portrait</a> (645)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30D3_KCQ/power_laundries_family_and_commercial/dallas_tx">Power Laundries, Family and Commercial</a> (6)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B332B_KCQ/private_households/dallas_tx">Private Households</a> (1)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B326E_KCQ/radio_and_television_repair_shops/dallas_tx">Radio and Television Repair Shops</a> (33)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B326F_KCQ/refrigeration_and_airconditioning_service_and_repair_shops/dallas_tx">Refrigeration and Airconditioning Service and Repair Shops</a> (93)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B32BB_KCQ/repair_shops/dallas_tx">Repair Shops</a> (1,853)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B3281_KCQ/reupholstery_and_furniture_repair/dallas_tx">Reupholstery and Furniture Repair</a> (66)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B30FB_KCQ/shoe_repair_shops_and_shoeshine_parlors/dallas_tx">Shoe Repair Shops and Shoeshine Parlors</a> (15)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B33C2_KCQ/vending_machine_operator/dallas_tx">Vending Machine Operator</a> (41)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B3277_KCQ/watch_clock_and_jewelry_repair/dallas_tx">Watch, Clock, and Jewelry Repair</a> (21)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_54_B32B4_KCQ/welding_repair/dallas_tx">Welding Repair</a> (43)
          </div>
              </div>
    </div>
  </div>
</div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb_33_B3_000/consumer_services">United States</a>
                              <span class="mx-1 fa fa-angle-right"></span>
                          </span>
                      <span>
              <a class="hover:underline" href="/mb_43_B3_44/consumer_services/texas">Texas</a>
                              <span class="mx-1 fa fa-angle-right"></span>
                          </span>
                      <span>
              <a class="hover:underline" href="/mb_53_B3_KCQ/consumer_services/dallas_tx">Dallas</a>
                          </span>
                  </div>
                  <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse Cities</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_KKS/consumer_services/houston_tx">Houston</a> (11,215)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_L0V/consumer_services/san_antonio_tx">San Antonio</a> (5,359)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_K51/consumer_services/austin_tx">Austin</a> (4,516)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_KGU/consumer_services/fort_worth_tx">Fort Worth</a> (2,591)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_KF1/consumer_services/el_paso_tx">El Paso</a> (1,724)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_KWV/consumer_services/plano_tx">Plano</a> (1,724)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_K4P/consumer_services/arlington_tx">Arlington</a> (1,534)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_L3C/consumer_services/spring_tx">Spring</a> (1,237)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_KMA/consumer_services/katy_tx">Katy</a> (1,009)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_53_B3_KBS/consumer_services/corpus_christi_tx">Corpus Christi</a> (919)
          </div>
              </div>
    </div>
  </div>
</div>
                          <div class="mb-8 mx-4 xl:mx-0">
            <div>
              <span>There are 1,435 cities in</span>
              <span>
                                  Texas                              </span>
              <span>with businesses in the Consumer Services category. We've listed the top ten (based on number of businesses) above.</span>
            </div>
            <a class="text-darks-v1" href="/mb_53_B3_KCQ/consumer_services/dallas_tx?pg=2&show_all_cities=1">
              <span>See all cities for Consumer Services in</span>
              <span>
                                  Texas                .
              </span>
            </a>
          </div>
              </div>
    </main>
    <style>
  @media (max-width: 360px) {
    .xs-device {
      font-size: 0.875rem;
    }
    .xs-8 {
      height: 2rem;
      width: 2rem;
    }
  }
</style>
<footer>
  <div class="w-full border-t bg-darks-v1 text-primary-light-v1 py-8 lg:py-16 flex flex-col justify-center items-center px-4 sm:px-6">
    <div class="grid grid-cols-8 gap-4 sm:gap-8 max-w-header w-full">
      <div class="hidden lg:flex justify-between mt-6 flex-col col-span-2">
        <div>
          <div>
            <a href="/" data-test="btn-logo-navbar">
              <img loading="lazy" width="162" height="37" class="mb-6 mr-3" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo">
              <span class="sr-only">Manta Home</span>
            </a>
          </div>
          <div class="flex">
            <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
                <span class="sr-only">Manta on Twitter</span>
              </span>
            </a>
            <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
                <span class="sr-only">Manta on Facebook</span>
              </span>
            </a>
            <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
                <span class="sr-only">Manta on LinkedIn</span>
              </span>
            </a>
          </div>
        </div>
        <div class="text-primary-light-v1 xs-device">
          © 2025 Manta Media Inc.<br>All rights reserved.
                  </div>
      </div>
      <div class="grid grid-cols-2 sm:grid-cols-3 gap-8 col-span-8 sm:col-span-5 lg:col-span-4">
        <div class="flex flex-col">
          <span class="font-serif">Manta</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-contact-footer-desktop" href="/contact" class="hover:font-bold py-1">Contact Us</a>
            <a data-test="btn-about-footer-desktop" href="/about-us" class="hover:font-bold py-1">About Us</a>
            <a data-test="btn-reviews-footer-desktop" href="/manta-reviews" class="hover:font-bold py-1">Reviews</a>
            <a data-test="btn-careers-footer-desktop" href="/careers" class="hover:font-bold py-1">Careers</a>
            <a data-test="btn-termsConditions-footer-desktop" href="/terms-and-conditions" class="hover:font-bold py-1">Terms & Conditions</a>
            <a data-test="btn-privacyPolicy-footer-desktop" href="/privacy-policy" class="hover:font-bold py-1">Privacy Policy</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Services</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-digital-mkt-footer-desktop" href="/digital-marketing-services" class="hover:font-bold py-1">Digital Marketing Services</a>
            <a data-test="btn-seo-services-footer-desktop" href="/services/organic-seo-company" class="hover:font-bold py-1">SEO Services</a>
            <a data-test="btn-local-seo-footer-desktop" href="/services/affordable-local-seo" class="hover:font-bold py-1">Local SEO</a>
            <a data-test="btn-national-seo-footer-desktop" href="/services/national-seo-company" class="hover:font-bold py-1">National SEO</a>
            <a data-test="btn-free-seo-footer-desktop" href="/free-seo-website-test" class="hover:font-bold py-1">Free SEO Website Test</a>
            <a data-test="btn-listings-footer-desktop" href="/business-listings/listings-management" class="hover:font-bold py-1">Listings Management</a>
            <a data-test="btn-display-ads-footer-desktop" href="/services/display-advertising" class="hover:font-bold py-1">Display Ads</a>
            <a data-test="btn-ppc-footer-desktop" href="/services/ppc-consulting" class="hover:font-bold py-1">PPC Consulting</a>
            <a data-test="btn-websites-footer-desktop" href="/small-business-marketing/websites" class="hover:font-bold py-1">Website Creation</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Resources</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-seo-faq-footer-desktop" href="/seo-faqs" class="hover:font-bold py-1">SEO FAQ</a>
            <a data-test="btn-ecommerce-footer-desktop" href="/seo-industry-guide/ecommerce-seo-guide" class="hover:font-bold py-1">Ecommerce SEO Guide</a>
            <a data-test="btn-construction-footer-desktop" href="/seo-industry-guide/seo-for-construction-companies" class="hover:font-bold py-1">Construction SEO Guide</a>
            <a data-test="btn-hvac-footer-desktop" href="/seo-industry-guide/seo-for-hvac" class="hover:font-bold py-1">HVAC SEO Guide</a>
            <a data-test="btn-homeyou-footer-desktop" href="/costs" class="hover:font-bold py-1">Home Services Cost</a>
          </div>
        </div>
      </div>
      <div class="border-t border-primary-light-v1 sm:border-none pt-6 sm:pt-0 flex flex-col col-span-8 sm:col-span-3 lg:col-span-2">
        <span class="font-serif">Manta Members</span>
                  <div class="flex items-center mt-6">
            <a data-test="btn-login-footer-desktop" class="hover:font-bold mr-4" href="/member/login">Log In</a>
            <a data-test="btn-login-footer-desktop" class="btn bg-primary-v1 text-white inline-block hover:font-bold" href="/member/register">Sign Up</a>
          </div>
                  <div class="mt-6">
                      <p class="mb-2">Search Manta's Directory to find the Small Business you're looking for</p>
            <a data-test="btn-index-footer-desktop" class="bg-primary-v1 py-2 px-4 rounded text-white inline-block hover:font-bold" href="/">Find a Business Near You</a>
                  </div>
      </div>
    </div>
    <div class="lg:hidden border-b border-t border-primary-light-v1 py-4 my-6 w-full">
      <div class="flex text-primary-light-v1">
        <a data-test="btn-help-footer-desktop-mobile" href="/contact" class="mr-6">Help</a>
        <a data-test="btn-termsConditions-footer-mobile" href="/terms-and-conditions" class="mr-6">Terms</a>
        <a data-test="btn-privacyPolicy-footer-mobile" href="/privacy-policy" class="mr-6">Privacy</a>
      </div>
    </div>
    <div class="flex lg:hidden justify-between items-center w-full">
      <div class="text-primary-light-v1 xs-device">
        © 2025 Manta Media Inc.<br>All rights reserved.
              </div>
      <div class="flex">
        <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
            <span class="sr-only">Manta on Twitter</span>
          </span>
        </a>
        <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
            <span class="sr-only">Manta on Facebook</span>
          </span>
        </a>
        <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
            <span class="sr-only">Manta on LinkedIn</span>
          </span>
        </a>
      </div>
    </div>
  </div>
  
      <script data-cfasync="false" async src="//cdn.intergient.com/1024347/72853/ramp.js"></script>
         </footer>    <script id="lazy-load-images">
  (function(hasObserver) {
    var createObservers = function(list, loadFn) {
      if (hasObserver) {
        var observer = new IntersectionObserver(function(entries, self) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              loadFn(entry.target);
              self.unobserve(entry.target);
            }
          });
        }, { rootMargin: '0px 0px 200px 0px' });

        list.forEach(function(el) {
          observer.observe(el);
        });
      } else {
        list.forEach(loadFn);
      }
    };

    var imgs = document.querySelectorAll('[lazy-load]');
    if (imgs.length) {
      createObservers(imgs, function(el) {
        el.setAttribute('src', el.getAttribute('lazy-load'));
      });
    }

    var bgs = document.querySelectorAll('.lazy');
    if (bgs.length) {
      createObservers(bgs, function(el) {
        el.classList.remove('lazy');
      });
    }
  })(typeof IntersectionObserver !== 'undefined');
</script>
    <script>
  const createCompany = company => {
    return (`
    <div class="flex items-start justify-between">
      <div>
        <div class="border border-gray-200 store-logo rounded">
          ${
            company.logo ? `<div class="bg-no-repeat bg-center bg-contain" style="background-image: url(${company.logo})"></div>`
            : `<div class="hidden md:block">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400" style="font-size: 4rem"></i>
            </div>
          </div>
          <div class="md:hidden">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400 text-6xl"></i>
            </div>
          </div>`
          }
        </div>
      </div>
      <div class="flex flex-grow justify-center">
        <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
          <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
            <a data-test="mb-result-card-title-${company.emid}" href="${company.url}" class="cursor-pointer font-serif text-gray-900 mr-2 break-words">${company.name}</a>
            ${company.claimStatus === 'PBL' 
            ? `<div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>`
            : company.claimStatus === 'CLAIMED'
            ? `<div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>`
            : ''
            }
          </div>
          ${company.recommendations.averageRating.ratingCount > 0
          ? `<div class="flex items-center">
              <div class="text-primary-v1 font-bold mr-4">${company.recommendations.averageRating.ratingValue}</div>
              {stars({ stars: ${company.recommendations.averageRating.ratingValue} })/}
              <div class="ml-4">(${company.recommendations.averageRating.ratingCount})</div>
            </div>` 
          : ''}
          <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
              ${company.location.address !== undefined ? `<div class="hidden md:block">${company.location.address}</div>` : ''}
              ${company.location.country && company.location.country !== 'United States' 
              ? `<div>${company.location.city}, ${company.location.country}</div>`
              : `<div>${company.location.city}, ${company.location.stateAbbrv}</div>`}
            </div>
          </div>
          ${company.contactInfo.phone ? `<div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>${company.contactInfo.phone}</div>
          </div>` : ''}
          ${company.contactInfo.website ? `<div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="${company.contactInfo.website}" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>` : ''}
          ${company.contactInfo.phone || company.contactInfo.website || company.location.address 
            ? `
            <div class="flex md:hidden mt-3">
              ${company.contactInfo.phone ? `<a href="tel:${company.contactInfo.phone}" rel="nofollow noopener"class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
              ${company.location.address ? `
                <a href="${`https://maps.google.com/maps?q=${encodeURIComponent(company.name)},` + '+' + `${encodeURIComponent(company.location.address)}` + '+' + `${encodeURIComponent(company.location.city)}`}" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                ` : ''}
                ${company.contactInfo.website ? `
                  <a href="${company.contactInfo.website}" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
            </div>` 
            : ''}
        </div>
        <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
          <div class="flex rounded w-full mb-4">
            <div class="flex justify-between w-full">
              <div class="flex justify-between items-center">
                ${company.claimStatus === 'PBL' 
                ? `
                <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
                  <i class="text-white fa fa-badge-check"></i>
                </div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                  MANTA VERIFIED
                </div>` 
                : company.claimStatus === 'CLAIMED'
                ? `<div class="mt-1 rounded-l bg-success px-2">
                    <i class="text-xs text-white fa fa-check"></i>
                  </div>
                  <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                    CLAIMED
                  </div>`
                : ''
                }
                ${company?.serviceAreasString && `<p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving ${company.serviceAreasString.split(',')[0]} and the Surrounding Area</p>`}
              </div>
            </div>
          </div>
          ${company.products.productTerms.list.length > 0 
            ? company.products.productTerms.list.forEach(term => {
              return `
                <div class="flex items-baseline mb-1">
                  <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
                  <span class="text-gray-800">${term.name}</span>
                </div>
              `
            })
            : `<div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under ${company.industry.sicm.description}</span>
          </div>`}
        </div>
      </div>
    </div>
    <div>
    ${company.detailedDescription 
      ? `<div class="hidden lg:block">
    <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">${company.detailedDescription}</div>
    </div>` 
      : ''}
    </div>
    `);
  }
</script>    <script>

  const trigger = document.querySelector('#infinite-scroll-trigger');
  const loader = document.querySelector('#loading-more-content');
  const results = document.querySelector('#more-results');

  const config = {
    root: null,
    threshold: 0.1,
    rootMargin: '0px'
  };

  let search = 2;

  const sicAndGeoLastIndex = window.location.pathname.lastIndexOf('/');
  const sicAndGeoFirstIndex = window.location.pathname.indexOf('_');
  const sicAndGeo = window.location.pathname.slice(sicAndGeoFirstIndex + 1, sicAndGeoLastIndex);

  const observer = new IntersectionObserver(async (entries, self) => {
    try {
      const [entry] = entries;
      if (!entry.isIntersecting) return;
      loader.classList.toggle('hidden');
      const response = await axios.get(`/more-results/${sicAndGeo}?pg=${search}`, {
        headers: { 'x-request-id': '369446ff1eb940be6de518f3e72a112e' }
      });
      if (response.data.companies.list.length === 0) {
        self.unobserve(entry.target);
      }
      loader.classList.toggle('hidden');
      let companies = [];
      response.data.companies.list.forEach((company) => {
        const resultContainer = document.createElement('div');
        resultContainer.setAttribute('data-test', `mb-result-card-${company.emid}`);
        resultContainer.classList.add('md:rounded', 'bg-white', 'border-b', 'border-primary-light-v1', 'px-3', 'py-4', 'md:p-8', 'md:mt-4', 'mx-4', 'xl:mx-0');
        resultContainer.innerHTML = createCompany(company);
        companies.push(resultContainer);
      })
      results.append(...companies);
      search++;
    } catch(error) {
      loader.classList.toggle('hidden');
      self.unobserve(entry.target);
    }
  });

  observer.observe(trigger, config);

</script>  </body>
</html>
