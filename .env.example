# Environment Configuration for Business Owner Scraper

# Proxy Configuration (Optional)
HTTP_PROXY=
HTTPS_PROXY=
SOCKS_PROXY=

# API Keys (if needed for any services)
GOOGLE_API_KEY=
BING_API_KEY=

# Database Configuration (Optional - for storing results)
DATABASE_URL=

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=scraper.log

# Rate Limiting
REQUESTS_PER_MINUTE=30
CONCURRENT_REQUESTS=5

# Browser Configuration
CHROME_BINARY_PATH=
CHROMEDRIVER_PATH=

# Output Configuration
OUTPUT_DIRECTORY=./results
BACKUP_DIRECTORY=./backups
