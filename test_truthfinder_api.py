#!/usr/bin/env python3
"""
Test script for the updated TruthFinder API integration.

This script tests the TruthFinder browser scraper with the exact headers
and cookies from the working curl request.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_truthfinder_api_integration():
    """Test the TruthFinder API integration with real API calls."""
    print("🧪 Testing TruthFinder API Integration")
    print("=" * 50)
    
    try:
        from src.scrapers.truthfinder_browser_scraper import TruthFinderBrowserScraper
        from src.core import ScrapingEngine
        from src.people_search import PersonSearchQuery
        
        # Initialize the scraper
        engine = ScrapingEngine("config.yaml")
        scraper = TruthFinderBrowserScraper(engine)
        
        print("✅ TruthFinder browser scraper initialized")
        
        # Test 1: Direct API call with the exact parameters from curl
        print("\n📋 Test 1: Direct API call for 'Saad Momin'")
        
        # Create a test query
        query = PersonSearchQuery(
            first_name="Saad",
            last_name="Momin",
            city="",
            state=""
        )
        
        # Perform the search
        results = scraper.search_person(query)
        
        print(f"✅ API call completed, found {len(results)} results")
        
        # Display results
        if results:
            for i, result in enumerate(results[:3], 1):  # Show first 3 results
                print(f"\n📊 Result {i}:")
                print(f"   Name: {result.owner_name}")
                print(f"   Age: {result.owner_age or 'N/A'}")
                print(f"   Address: {result.address or 'N/A'}")
                print(f"   Phone: {result.phone or 'N/A'}")
                print(f"   Email: {result.email or 'N/A'}")
                print(f"   Source: {result.source}")
                print(f"   Confidence: {result.match_confidence or 'N/A'}")
                
                if result.aliases:
                    print(f"   Aliases: {', '.join(result.aliases)}")
                
                if result.family_members:
                    print(f"   Family: {len(result.family_members)} members")
                    for member in result.family_members[:2]:
                        print(f"     - {member.get('name', 'N/A')} ({member.get('relationship', 'N/A')})")
        else:
            print("⚠️  No results found")
        
        # Test 2: Test with different name
        print("\n📋 Test 2: API call for 'John Smith'")
        
        query2 = PersonSearchQuery(
            first_name="John",
            last_name="Smith",
            city="Houston",
            state="TX"
        )
        
        results2 = scraper.search_person(query2)
        print(f"✅ Second API call completed, found {len(results2)} results")
        
        if results2:
            result = results2[0]
            print(f"   Sample result: {result.owner_name} - {result.location or 'N/A'}")
        
        # Clean up
        scraper.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ TruthFinder API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_people_search_engine_with_truthfinder():
    """Test the people search engine with TruthFinder integration."""
    print("\n🔍 Testing People Search Engine with TruthFinder")
    print("=" * 50)
    
    try:
        from src.people_search import PersonSearchEngine, PersonSearchQuery
        
        # Initialize the engine
        engine = PersonSearchEngine("config.yaml")
        
        print("✅ People search engine initialized")
        
        # Create a test query
        query = PersonSearchQuery(
            first_name="Saad",
            last_name="Momin"
        )
        
        # Perform search with TruthFinder only
        results = engine.search_person(query, sources=['truthfinder_browser'])
        
        print(f"✅ People search completed, found {len(results)} results")
        
        if results:
            result = results[0]
            print(f"\n📊 Sample Result:")
            print(f"   Name: {result.owner_name}")
            print(f"   Search Type: {result.search_type}")
            print(f"   Data Quality: {result.data_quality or 'N/A'}")
            print(f"   Completeness: {result.data_completeness or 'N/A'}")
            print(f"   Source: {result.source}")
        
        # Clean up
        engine.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ People search engine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_scraper_integration():
    """Test the main BusinessOwnerScraper with people search."""
    print("\n🏢 Testing Main Scraper Integration")
    print("=" * 50)
    
    try:
        from main import BusinessOwnerScraper
        
        # Initialize the scraper
        scraper = BusinessOwnerScraper("config.yaml")
        
        print("✅ BusinessOwnerScraper initialized")
        
        # Test people search
        results = scraper.search_person(
            first_name="Saad",
            last_name="Momin",
            sources=['truthfinder_browser']
        )
        
        print(f"✅ Main scraper people search completed, found {len(results)} results")
        
        if results:
            result = results[0]
            print(f"   Result: {result.owner_name} from {result.source}")
        
        # Clean up
        scraper.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ Main scraper integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cookie_refresh_mechanism():
    """Test the cookie refresh mechanism."""
    print("\n🍪 Testing Cookie Refresh Mechanism")
    print("=" * 50)
    
    try:
        from src.scrapers.truthfinder_browser_scraper import TruthFinderBrowserScraper
        from src.core import ScrapingEngine
        
        # Initialize the scraper
        engine = ScrapingEngine("config.yaml")
        scraper = TruthFinderBrowserScraper(engine)
        
        print("✅ TruthFinder scraper initialized")
        
        # Test fresh cookie retrieval
        print("📋 Testing fresh cookie retrieval...")
        
        # This is an async method, so we need to run it properly
        async def test_cookies():
            fresh_cookies = await scraper._get_fresh_truthfinder_cookies()
            return fresh_cookies
        
        fresh_cookies = asyncio.run(test_cookies())
        
        print(f"✅ Retrieved {len(fresh_cookies)} fresh cookies")
        
        for cookie in fresh_cookies[:3]:  # Show first 3 cookies
            print(f"   - {cookie['name']}: {cookie['value'][:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Cookie refresh test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all TruthFinder API tests."""
    print("🚀 TruthFinder API Integration Test Suite")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    tests = [
        test_cookie_refresh_mechanism,
        test_truthfinder_api_integration,
        test_people_search_engine_with_truthfinder,
        test_main_scraper_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"   ❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All TruthFinder API tests passed!")
        print("")
        print("💡 The TruthFinder integration is working with:")
        print("- Exact headers from your working curl request")
        print("- Proper cookie management with fallback refresh")
        print("- Mobile user agent matching your setup")
        print("- API key and app-id from the working request")
        print("")
        print("🔧 Next steps:")
        print("1. Test with real names in your area")
        print("2. Monitor API response rates and adjust as needed")
        print("3. Update cookies if they expire (automatic refresh included)")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
        print("")
        print("🔧 Troubleshooting:")
        print("1. Ensure you have a stable internet connection")
        print("2. Check if TruthFinder has changed their API")
        print("3. Verify the API key is still valid")
        print("4. Try updating the cookies from a fresh browser session")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
