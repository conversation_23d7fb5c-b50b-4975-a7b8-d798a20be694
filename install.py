#!/usr/bin/env python3
"""
Installation and setup script for Business Owner Scraper.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_pip():
    """Check if pip is available."""
    try:
        import pip
        print("✅ pip is available")
        return True
    except ImportError:
        print("❌ Error: pip is not available")
        return False


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = [
        "logs",
        "results",
        "backups",
        "examples"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"   Created: {directory}/")
    
    print("✅ Directories created")


def setup_configuration():
    """Set up configuration files."""
    print("\n⚙️  Setting up configuration...")
    
    # Copy .env.example to .env if it doesn't exist
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            shutil.copy(".env.example", ".env")
            print("   Created: .env")
        else:
            print("   Warning: .env.example not found")
    else:
        print("   .env already exists")
    
    # Check config.yaml
    if os.path.exists("config.yaml"):
        print("   config.yaml exists")
    else:
        print("   Warning: config.yaml not found")
    
    print("✅ Configuration setup complete")


def test_installation():
    """Test the installation."""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from src.core import ScrapingEngine
        from src.scrapers.bbb_scraper import BBBScraper
        from src.utils.data_processor import DataProcessor
        from src.exporters.csv_exporter import CSVExporter
        
        print("   ✅ Core modules import successfully")
        
        # Test basic functionality
        config = {
            'general': {'output_directory': './results'},
            'sources': {'bbb': {'enabled': True, 'base_url': 'https://www.bbb.org'}},
            'output': {'deduplication': {'enabled': True}}
        }
        
        # Test engine initialization (without actual network calls)
        print("   ✅ Basic functionality test passed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False


def show_usage_instructions():
    """Show usage instructions."""
    print("\n📖 Usage Instructions:")
    print("=" * 50)
    print()
    print("1. Basic usage:")
    print('   python main.py --business-type "lawn care" --location "dallas tx"')
    print()
    print("2. Interactive mode:")
    print("   python main.py --interactive")
    print()
    print("3. Multiple sources:")
    print('   python main.py -b "restaurant" -l "houston tx" -s bbb -s manta')
    print()
    print("4. Export to Excel:")
    print('   python main.py -b "construction" -l "austin tx" --format xlsx')
    print()
    print("5. Run examples:")
    print("   python examples/example_usage.py")
    print()
    print("6. Run tests:")
    print("   python run_tests.py")
    print()
    print("📚 For more information, see README.md")


def main():
    """Main installation function."""
    print("🤖 Business Owner Scraper - Installation Script")
    print("=" * 60)
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed at dependency installation")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Setup configuration
    setup_configuration()
    
    # Test installation
    if not test_installation():
        print("\n⚠️  Installation completed with warnings")
        print("   Some functionality may not work correctly")
    else:
        print("\n✅ Installation completed successfully!")
    
    # Show usage instructions
    show_usage_instructions()
    
    print("\n🎉 Ready to scrape business owner information!")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
