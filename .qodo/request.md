curl 'https://www.manta.com/more-results/54_B83AE_KCQ/dolls_and_stuffed_toys?pg=2' \
  -H 'Accept: */*' \
  -H 'Accept-Language: en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -b 'refer_id=0000; sess_refer=1; cust_id=a2b03c09-72f5-4d7a-b6c8-dc32a8a6abb6; _gcl_au=1.1.629188657.1754337795; _fbp=fb.1.1754337795481.554633048990336608; _ga=GA1.1.441186518.1754337796; cf_clearance=67lzCpUWG7xbBEFllgeswp1CMxszKGtS2Nbdw128.Zs-1754337837-1.2.1.1-lWhdW2m.CnlJ3plKWu_..wXwivAOHoIA35vil9Om_Xp.bBKwCYWQnerm.UZstA0Ns_rh5KN3eIZatCc_3R4h1Rx8QvfKiMzqd6thVyc77x8CSGRHL0GyL8hDwc3by2J5XKvLihFwS5vQzezhhRyp3BFm7eVYSF3GjyNL36EKwQXjq6Lx9GOFcPL6HorpArF9AakGzUHLg1T02bYMpT37XjCnKI0BnTwvoVIWPk.7MrM; pwBotScore=99; usprivacy=1---; ad_clicker=false; _sharedid=************************************; _sharedid_cst=zix7LPQsHA%3D%3D; _li_dcdm_c=.manta.com; _lc2_fpi=be57377c7350--01k1vb4mvrry8hrvmx9ntrryck; _lc2_fpi_meta=%7B%22w%22%3A1754338055032%7D; _lr_retry_request=true; _lr_env_src_ats=false; _cc_id=d17daefcd430680e1ba617cad920f3f; panoramaId=2b713ef3dedfd3c724324dcea6a7a9fb927a02311075d3027f6ddcd7d55bfda3; panoramaId_expiry=1754424455377; panoramaIdType=panoDevice; _lr_sampling_rate=100; mako_fpc_id=d6d8565f-4a6e-48f7-b750-92dd1a361357; ftoggle-frontend-production=1741960748658zaCPNceWGIDCSYAPQWWSTaHTBBORAz1; cust_id=a2b03c09-72f5-4d7a-b6c8-dc32a8a6abb6; refer_id=0000; _tt_enable_cookie=1; _ttp=01K1VB9M8F1GQX99RT5G47WKTY_.tt.1; __gads=ID=d2ea9ee89f7b16bc:T=**********:RT=**********:S=ALNI_Mbe198JLE1RO5FoDhM1Lvtbnl2fEQ; __gpi=UID=0000117b238e7f2d:T=**********:RT=**********:S=ALNI_MYSnpIS44C79S5DYj4AmmU1uqTGjQ; __eoi=ID=aeb4c47fdb569a6b:T=**********:RT=**********:S=AA-AfjY1TWEpb_4pwdqBe9Z7mCE8; pageDepth=17; _ga_B0C8Y3EM2T=GS2.1.s1754337795$o1$g1$t**********$j25$l0$h2020782189$dSowaSwx9YQk85SA-qD98nJBoMJiI3VToDg; _ga_2YQERZ6LZV=GS2.1.s1754337795$o1$g1$t**********$j26$l0$h495208163$dDW5h6klonv7YNi2uKZV8qx3Cll28rOR4-Q; ttcsid=1754338218262::3_ZQYnBnVCnBIDj7nPd4.1.**********220; ttcsid_CBI0833C77U5CF09APL0=1754338218260::hdslJaYMOg_ZG7IRJonE.1.**********473; _awl=2.**********.5-785a00c7f9b8c9ea348a9ca4b5eac5f2-6763652d617369612d6561737431-0; FCNEC=%5B%5B%22AKsRol_G4lMl6NW3bDD9DgRYCc1smakQPjqXPcN70n88hDC8bO8MkEBtMCcasR1cncmE0E8rqwtgo7m6P2PoWYMf9tfhVP3eMpGimDUmdxOdfEyhTkBZWbqvuo6eiuIr2ALHWfR_Sstv1jUOT0XhvMKHpGnkv5PVSw%3D%3D%22%5D%5D; cto_bundle=OllE4V90Yks1bjRzQ2REQnEyRTJvb1hUZ05HSTdWaHJPcXlWUndSYUFtVUlZMXZYVzk2ZWIwTmRGOTFIejdJMFR4RlI2Rk5FQW5BR3RXWmdnaCUyRlc2ZlV1d0tVcUZDUzhVaHI1TzFvWjAyRkxmU1klMkI2MGl5NE9PZyUyRkQ3M1NTU0oxSkFXRTBPRkglMkYlMkZUdlM3ZFdIeU9jVExMYnNMJTJGbjJnMGM5d29IODd1cHZOMjFVSTBQTllua1MwenZrb0VZejRaMDNlUVNxN2tacjZyZmcxVU9BN2J5dmJEcFBqem83Mzk3dUVKOU9CdEZyUXVTU080ZjFGTUVnZDFVaGNMUXRYSlRqVGJS; cto_bidid=Y4CbZV9nbnhla2FXMFBlVVdaeDRjZ2FwVGd6M0NnMDA4SEhuQUYycWNqYWZ1NDhib1E3Y2RFekxpZEVaREZzVXk5bk9YJTJCY1FZWll5MnA0akxqQ24waWVzS3p6Tm1ubnNzNkZSUGMyTnZqcUZFd1RnJTNE; __cf_bm=sBY0RPR73V3O.wosQVenrgVWeLmoFbzin9pA.9S2hpQ-1754338924-*******-CE147cjX5Ltb_E4AXUf_qtmyhXpnhD10jmxtDjQ7DdJY3iT9Qc6I59qhD3it5tHHlZAWhRvly40udokHB59RFdM1NnP21sGcP.Vwzle0mFZnEf.lJTgWnOV_GMeXzSWu' \
  -H 'Pragma: no-cache' \
  -H 'Referer: https://www.manta.com/mb_54_B83AE_KCQ/dolls_and_stuffed_toys/dallas_tx' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-arch: ""' \
  -H 'sec-ch-ua-bitness: "64"' \
  -H 'sec-ch-ua-full-version: "138.0.7204.184"' \
  -H 'sec-ch-ua-full-version-list: "Not)A;Brand";v="8.0.0.0", "Chromium";v="138.0.7204.184", "Google Chrome";v="138.0.7204.184"' \
  -H 'sec-ch-ua-mobile: ?1' \
  -H 'sec-ch-ua-model: "Nexus 5"' \
  -H 'sec-ch-ua-platform: "Android"' \
  -H 'sec-ch-ua-platform-version: "6.0"' \
  -H 'x-request-id: 0373b80bae180c42ea71d1d5776b91be'



  response body 
  <html lang="en">
  <head>
    <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>404 Not Found</title>
<link rel="icon" href="https://cc3.manta-r3.com/assets-gz/2e2a97f56/img/favicon.png" type="image/png">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com">
<link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/app.css">
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css"></noscript>
<style>
  iframe[name=d_ifrm] {
  display: none;
  }
</style>
        <script rel="gtm-render">
      var screenWidth = screen.width;
      var sSz = 'lg';
      var wsSz = 'lg';
      var wSz = 'lg';
      if (screenWidth < 768) {
        sSz = wsSz = wSz = 'xs';
      } else if (screenWidth < 992) {
        sSz = wsSz = wSz = 'sm';
      } else if (screenWidth < 1200) {
        sSz = wsSz = wSz = 'md';
      }

      var gtmData = {
        ua_property: "UA-10299948-11",
        googleExperimentId: "",
        googleExperimentVariation: "-1",
        pageTitle: "404 Not Found",
        page_type: "company-content", // TODO: make this work when we do search/browse
        is_pagespeed: Boolean('false'),

        
        visitor_id: "a2b03c09-72f5-4d7a-b6c8-dc32a8a6abb6",
        customer_segment: "prospector",
        page_depth: "18",
        scr_win_width: sSz + '-' + wSz,

        
        treatment: "no-test",

                  altTreatment1: "LM $49 Price Test CONTROL",
                  altTreatment2: "",
                  altTreatment3: "",
        
        ip: "**************",

                  // Older cookies might not have stateAbbrv and countryAbbrv,
          // so fall back to state and country
          user_state: "MH",
          user_country: "IN",
        
        url_hash: window.location.hash,
        timestamp: new Date().toString(),
        sbi: "false",
        statusCode: "200",  // TODO: actual status
      };

              var dataLayer = [gtmData];
      
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-R7B4');
    </script>
    <script>
      var gaTrack = function (category, action, label, value, interactive) {
        if (arguments.length === 1) {
          // Non-event tracking stuff
          dataLayer.push(category);
          return;
        } else if (arguments.length === 3) {
          value = 1;
          interactive = true;
        }
        if (arguments.length === 4) {
          if (typeof value === 'boolean') {
            interactive = value;
            value = 1;
          } else {
            interactive = true;
          }
        }

        // if label is an object, serialize it into name=value pairs
        if (typeof label === 'object' && !Array.isArray(label)) {
          var pairs = [];
          for (var key in label) {
            pairs.push(key + '=' + label[key]);
          }
          label = pairs.join(',');
        }

        dataLayer.push({
          eventData: {
            category: category,
            action: action,
            label: label,
            value: value
          },
          event: (interactive ? 'interactive' : 'non-interactive') + ' event'
        });
      };
    </script>
  <script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/cash.min.js"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/js.cookie.min.js" onload="Cookies.defaults = { path: '/', domain: '.manta.com' }"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/axios.min.js" onload="window.axios = window.redaxios"></script>
<script>var loadScript=(function(d,s,c,e,f){return function(u){if(c[u]){return c[u];}e=d.createElement(s);e.async=!0;e.src=u;f=d.getElementsByTagName(s)[0];f.parentNode.insertBefore(e,f);return c[u]=e;};})(document,'script',{});</script>
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css"></noscript>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/simpleLightbox.min.js"></script>
<script>
  window.__PRELOADED_STATE__ = {
    toggledFeatures: {"experimentId":"","experimentVariation":"-1","forceGzip":false,"adsenseChannel":"1","scrollTracking":false,"exitTracking":true,"fakeMixpanel":true,"fakeGa":false,"useSubscriptionTestMode":false,"yextAllowDuplicatesMode":false,"allowSurveys":true,"isOffHoursOverride":false,"isBusinessHoursOverride":false,"skipDittoVendorRequests":false,"marketplaceJobs":false,"claimCtaText":"Own This Business?","claimCtaColor":"btn-primary","useLongBundleDesc":false,"isSiteMaintenanceMode":false,"homeyouLeads":false,"updoptProdOverride":false,"enableUpdoptDossier":true,"adsenseSlotMobile":"1160948055","adsenseSlotDesktop":"5730748454","useFapiForSubscriptions":false,"popOverlayOnCheckout":true,"useBuilderToAdd":false,"checkoutTemplate":"checkout","buyerlink_treatmentA":true,"adsenseHeroPlacement":true,"refreshAds":false,"show300x250":true,"showTaboola":true,"showDesktopAdhesionBanner":true,"homeyouWidgetPlacement":"about","newStack":true,"similarBusinessesUp":true,"reducedAdDensity":true,"navAffiliateBusinessCreditAd":true,"triggerDemoScheduler":true,"blockSICM":true,"useBriteVerify":true,"changeVersion3":true,"toggleUrlCheckout":true,"showCovid":true,"covidShow":true,"showReviewTile":false,"includeGMBScan":true,"embedYextDashboard":true,"postponeMerchantScans":true,"es_related":true,"es_search":true,"useGoogleAutocomplete":true,"useGoogleMaps":true,"useGooglePlacesInClaimBuilder":true,"homeyouNumberTest":true,"homeyouTrackingNumber":"+***********","useInternationalSearch":true,"showMarketStats":false,"useNewAnalyticsService":true,"useElasticMb":true,"useNewCheckout":true,"useNewEditPage":true,"useNewMemberDashboard":true,"useElasticWorld":true,"showPaywallPage":false,"checkoutPaywallTreatment":"paywall-control","usePlaywire":true,"showBanner":false,"copyTextBanner":"Promo Banner","redeemByTextBanner":"Redeem by 19th march","expiryDateBanner":"03/18/2023","catchPhraseBanner":"Special Offer!","validForBanner":"hasDitto, hasLmReviews, hasWebsite, hasPpcAds, hasDisplayAds, hasFeaturelessPlan, freeUser","requireTermsOfService":true,"adSenseProfilePages":true,"adSenseSearchPages":true,"showAdapexAds":true,"showDetailedDescription":true,"oldSurveyModal":true,"useRepSalesDashboard":true,"useTaboolaAds":false,"enableFacebookSignIn":true,"enableGoogleSignIn":true,"unclaimedStatic":true,"logCookieErrors":true,"rightRailDoubleAds":true,"trackNumbers":true,"enableWebsiteAddOn":true},
    referral_source: '',
    abTreatment: 'no-test',
    gamNetworkCode: '6009',
    visitor: {"ip":"**************","id":"a2b03c09-72f5-4d7a-b6c8-dc32a8a6abb6","pageDepth":18,"customerSegment":{"threshold":23,"id":"p","label":"prospector"},"smallBusinessInterest":false,"xri":"93e3d5d35c8a69aaa2c4d42ebe27c3f2"},
    pageComponents: {},
    clientIp: '**************',
    isCalifornia: false,
    isDev: false,
    env: 'production',
    member: undefined  };
</script>
<script>
  var cache = {};
  window.logError = function(e, info) {
    var lines = (e.stack || '').split('\n');
    var callsite = lines.length > 1 ? lines[1].match(/(app\.js:\d+:\d+)/) : null;
    var key = e.message + (callsite && callsite[1] ? ' at ' + callsite[1] : '');

    if (!cache[key]) {
      try {
        window.axios && axios.post('/fapi/errors', {
          message: e.message || 'Unknown error',
          stack: e.stack || 'No stack trace available',
          info: info,
                    userAgent: (window.navigator && window.navigator.userAgent) || 'unknown'
        }).catch(function() {});
      } catch (e) {
        // Obviously, this isn't async/await, so it won't catch the
        // axios call, but I just want to _assure_ we don't throw
        // from the onerror handler.
      }
    }
  };
  window.onerror = function(message, source, lineno, colno, error) {
    // Don't log errors that come from ads and crap like that
    if (source.indexOf('manta.com') > -1) {
      logError(error, { source: source, lineno: lineno, colno: colno });
    }
  };
</script>
<!-- The script below is going to be commented until we figure out what could be a better implementation talking about performance -->
<!-- <script defer src="//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js" importance="low"></script> --><script defer src="https://btloader.com/tag?o=5150306120761344&upapi=true"></script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>

      <script data-cfasync="false">
        window.ramp = window.ramp || {};
        window.ramp.que = window.ramp.que || [];
      </script>
    
    <script type="text/javascript">
      window.ramp = window.ramp || {};
      window.ramp.que = window.ramp.que || [];
    </script>
        <style>
      .error-title {
        font-size: 9rem; line-height: 7rem;
      }
      .main-error-msg {
        line-height: 2.25rem;
      }
      @media screen and (min-height: 1024px) {
        .screen {
          height: 70vh;
        }
      }
    </style>
  </head>
  <body>
    <div class="relative">
  <a href="#start-of-content" class="text-xs text-darks-v1 focus:text-white absolute right-100">Skip to Content</a>
</div>
<style>
      .desktop-search-wrapper {
      width: 380px;
    }
    @media(min-width: 1110px) {
      .desktop-search-wrapper {
        width: 480px;
      }
    }
  
</style>

<header>
  <div class="mobile-menu hidden fixed w-screen h-screen bg-white p-4 z-50 overflow-auto">
    <div class="float-right" onclick="$('.mobile-menu').addClass('hidden'); $('body').removeClass('overflow-hidden')"><i class="text-text-darks-v1 text-3xl fa fa-times"></i></div>
    <ul class="text-gray-600 my-16 text-lg">
  <li class="mb-2 hover:font-bold">
    <a href="/services">For Businesses</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/free-business-listing">Free Company Listing</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/listings-management">Premium Business Listings</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/small-business-marketing/websites">Websites</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/organic-seo-company">SEO</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/affordable-local-seo">Local SEO</a>
  </li>
  <li>
    <a href="/services/national-seo-company">National SEO</a>
  </li>
</ul>
      <div class="flex flex-col lg:flex-row">
    <a class="btn bg-primary-light-v1 text-gray-800 font-bold flex-1 mb-4 py-3" href="/member/login">Log In</a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="mb-4 btn bg-primary-v1 text-white inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
      </div>
  </div>

  <div class="mobile-search hidden fixed w-screen h-screen bg-white z-50 overflow-y-scroll">
  <div class="justify-center py-3 mx-auto max-w-header flex items-center bg-darks-v1 px-4 relative">
    <div onclick="$('.mobile-search').addClass('hidden');$('.pre-mobile-search').removeClass('hidden')" class="lg:hidden text-primary-light-v1 cursor-pointer absolute left-0 top-0 mt-6 ml-5">Cancel</div>
    <div class="flex sm:pr-3">
      <a href="/" data-test="btn-logo-navbar">
        <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo" width="109" height="25">
      </a>
    </div>
  </div>
  <div class="bg-darks-v1 text-gray-dark search-component-mobile"></div>
</div>

  <div class="px-6 bg-darks-v1 h-auto text-white">
    <div class="justify-between py-3 mx-auto max-w-header flex items-center">

      <div onclick="$('.pre-mobile-search').addClass('hidden');loadSearchBar('.mobile-search')" class="flex md:hidden"><i class="text-2xl fa fa-search"></i></div>
      <div>
  <a href="/" data-test="btn-logo-navbar">
    <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo"  width="109" height="25" >
    <span class="sr-only">Manta Home</span>
  </a>
</div>
      <div class="hidden md:inline-block desktop-search-wrapper">
        <div class="rounded text-gray-dark hidden md:inline-block search-component w-full">
          <form name="searchForm">
  <div class="flex flex-col sm:flex-row px-3 sm:px-0" style="border-radius: 4px 4px 4px 0px;">
    <div class="flex sm:w-1/2 relative px-3 py-2 bg-white my-1 sm:my-0 rounded sm:rounded-l-lg sm:rounded-r-none">
      <div class="flex justify-center items-center mr-4 w-5">
        <span class="text-primary-v1 fa fa-search text-xl"></span>
      </div>
      <div class="flex w-full">
        <label for="header-search" class="sr-only">Search</label>
        <input
          id="header-search"
          name="search"
          placeholder="I'm looking for..."
          class="w-full outline-none"
          onfocus="loadSearchBar('.search-services-menu')"
          autocomplete="off"
          value=""
        />
      </div>
      <div class="absolute search-services-menu hidden" style="z-index: 10000">
        <ul class="p-0 m-0 text-gray-600">
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-utensils mr-3"></span
            ></span>
            <span class="text-small">Restaurants</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-stethoscope mr-3"></span
            ></span>
            <span class="text-small">Doctors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-gavel mr-3"></span
            ></span>
            <span class="text-small">Lawyers</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-hammer mr-3"></span
            ></span>
            <span class="text-small">Contractors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"><span class="fa fa-car mr-3"></span></span>
            <span class="text-small">Automotive</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-tooth mr-3"></span
            ></span>
            <span class="text-small">Dentists</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex py-2 sm:py-0 sm:w-1/2">
      <div class="flex w-full relative px-3 sm:pl-0 sm:pr-3 py-2 bg-white rounded-l-lg sm:rounded-none">
        <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
        <div class="self-center w-full text-gray-800">
          <label for="header-location" class="sr-only">Location</label>
          <input
            id="header-location"
            name="location"
            placeholder="City, State, Country, Zip"
            class="w-full outline-none"
            onfocus="loadSearchBar('.search-location-menu')"
            autocomplete="off"
            value=""
          />
        </div>
        <div class="search-location-menu hidden" style="z-index: 10000">
          <ul class="m-0 p-0 locations">
            <li
              class="px-4 py-3 text-primary-v1 hover:bg-gray-200 cursor-pointer"
            >
              <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
              <span class="small loc-name">Current Location</span>
            </li>
          </ul>
        </div>
      </div>
      <button
        type="submit"
        class="sm:flex items-center justify-center bg-primary-v1 text-white px-3 py-2 overflow-hidden rounded-r-lg">
        <span class="sr-only">Search</span>
        <span class="not-sr-only text-white fa fa-search text-xl"></span>
      </button>
    </div>
  </div>
</form>
<script>
  (function () {
    var loc = localStorage.getItem("locHistory");
    if (loc) {
      var li = $(
        '<li class="px-4 py-3 text-gray-600 hover:bg-gray-200 cursor-pointer border-t border-gray-300 sm:border-none"><span class="fa fa-clock mr-4"></span><span class="small loc-name">Current Location</span></li>'
      );
      JSON.parse(loc).forEach(function (l) {
        if (l.stateAbbrv) {
          li.find(".loc-name").text(l.formatted);
          $(".locations").append(li.clone());
        }
      });
    }
  })();
  var loadSearchBar = (function () {
    return function (c) {
      $(c).removeClass("hidden");
      loadScript("//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js");
    };
  })();
  $("form[name=searchForm]").on("submit", function (e) {
    e.preventDefault();
    var search = $("input[name=search]").val();
    var locationInput = $("input[name=location]").val().trim();
    if (!locationInput) {
      const lastGeo = Cookies.get('lastGeo');
      if (lastGeo) {
        try {
          const geo = JSON.parse(lastGeo);
          if (geo && geo.city && geo.stateAbbrv) locationInput = `${geo.city}, ${geo.stateAbbrv}`;
        } catch (e) {
          return;
        }
      }
    };

    if (!locationInput) return;

    var device = "desktop";
    if (window.screen.availWidth <= 500) {
      device = "mobile";
    } else if (window.screen.availWidth <= 1024) {
      device = "tablet";
    }
    var parts = locationInput.split(/[, ]+/);
    var state = parts.pop();
    var city = parts.join(" ");
    window.location =
      "/search?search_source=nav&search=" +
      encodeURIComponent(search) +
      "&city=" +
      encodeURIComponent(city) +
      "&state=" +
      encodeURIComponent(state) +
      "&device=" +
      device +
      "&screenResolution=" +
      window.screen.availWidth +
      "x" +
      window.screen.availHeight;
  });
</script>
        </div>
      </div>

      <div class="hidden lg:block text-sm">
        <div data-test="btn-products-navbar-desktop" class="dropdown inline-block py-4 text-primary-light-v1">
          <a data-test="btn-findBusiness-navbar-desktop" class="hover:underline font-bold px-3" href="/services">For Businesses <i class="fa fa-angle-down"></i></a>
          <div class="dropdown-tri"></div>
          <ul class="dropdown-menu py-2 text-nordic-v1">
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/free-business-listing">Free Company Listing</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/listings-management">Premium Business Listings</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/small-business-marketing/websites">Websites</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/organic-seo-company">SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/affordable-local-seo">Local SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/national-seo-company">National SEO</a></li>
          </ul>
        </div>
            <a data-test="btn-login-navbar-desktop" class="hover:underline text-primary-light-v1 font-bold"
    href="/member/login"><span class="lg:px-1 px-3 xl:px-3">Log In</span></a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="px-5 lg:px-2 xl:px-5 py-2 w-auto rounded cursor-pointer text-center  bg-primary-v1 text-white mx-3 lg:ml-3 lg:mr-2 xl:mx-3 inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
        </div>

      <div onclick="$('.mobile-menu').removeClass('hidden');" class="flex lg:hidden"><i class="text-2xl fa fa-bars"></i></div>
    </div>
      </div>

  <div class="pl-0 lg:px-6 bg-primary-dark text-white overflow-x-hidden faded faded-x-primary-dark hidden">
  <div class="py-1 mx-auto max-w-header flex items-center overflow-x-auto">
    <div class="inline-block py-2 text-sm whitespace-no-wrap ml-5">
      <a class="cursor-pointer" href="/mb_33_A6_000/professional_services">Business Services<span class="align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_C4_000/restaurants_and_bars">Food & Beverage<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_B3_000/consumer_services">Consumer Products & Services<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_D0_000/healthcare">Health<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_G4_000/information_technology">Tech & Communications<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer mr-5" href="/mb_33_E6_000/industrial_machinery">Industrial</a>
    </div>
  </div>
</div>
  

</header>
    <main>
      <span id="start-of-content"></span>
      <div class="flex flex-col align-center justify-center text-center px-4 py-24 sm:py-32 sm:px-0 screen">
        <h1 class="font-serif text-darks-v1 error-title">404</h1>
        <div class="mb-12 mt-10">
          <p class="text-darks-v1 text-3xl font-medium font-serif main-error-msg mb-2">The page you were looking for doesn’t exist.</p>
          <p class="text-gray-800">You may have mistyped the address or the page may have moved.</p>
        </div>
        <div>
          <a href="/" class="block sm:inline rounded border-2 border-primary-v1 text-primary-v1 py-2 px-4 font-bold cursor-pointer hover:bg-primary-v1 hover:text-white">Go Back Home</a>
        </div>
      </div>
    </main>
    <style>
  @media (max-width: 360px) {
    .xs-device {
      font-size: 0.875rem;
    }
    .xs-8 {
      height: 2rem;
      width: 2rem;
    }
  }
</style>
<footer>
  <div class="w-full border-t bg-darks-v1 text-primary-light-v1 py-8 lg:py-16 flex flex-col justify-center items-center px-4 sm:px-6">
    <div class="grid grid-cols-8 gap-4 sm:gap-8 max-w-header w-full">
      <div class="hidden lg:flex justify-between mt-6 flex-col col-span-2">
        <div>
          <div>
            <a href="/" data-test="btn-logo-navbar">
              <img loading="lazy" width="162" height="37" class="mb-6 mr-3" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo">
              <span class="sr-only">Manta Home</span>
            </a>
          </div>
          <div class="flex">
            <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
                <span class="sr-only">Manta on Twitter</span>
              </span>
            </a>
            <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
                <span class="sr-only">Manta on Facebook</span>
              </span>
            </a>
            <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
                <span class="sr-only">Manta on LinkedIn</span>
              </span>
            </a>
          </div>
        </div>
        <div class="text-primary-light-v1 xs-device">
          © 2025 Manta Media Inc.<br>All rights reserved.
                  </div>
      </div>
      <div class="grid grid-cols-2 sm:grid-cols-3 gap-8 col-span-8 sm:col-span-5 lg:col-span-4">
        <div class="flex flex-col">
          <span class="font-serif">Manta</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-contact-footer-desktop" href="/contact" class="hover:font-bold py-1">Contact Us</a>
            <a data-test="btn-about-footer-desktop" href="/about-us" class="hover:font-bold py-1">About Us</a>
            <a data-test="btn-reviews-footer-desktop" href="/manta-reviews" class="hover:font-bold py-1">Reviews</a>
            <a data-test="btn-careers-footer-desktop" href="/careers" class="hover:font-bold py-1">Careers</a>
            <a data-test="btn-termsConditions-footer-desktop" href="/terms-and-conditions" class="hover:font-bold py-1">Terms & Conditions</a>
            <a data-test="btn-privacyPolicy-footer-desktop" href="/privacy-policy" class="hover:font-bold py-1">Privacy Policy</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Services</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-digital-mkt-footer-desktop" href="/digital-marketing-services" class="hover:font-bold py-1">Digital Marketing Services</a>
            <a data-test="btn-seo-services-footer-desktop" href="/services/organic-seo-company" class="hover:font-bold py-1">SEO Services</a>
            <a data-test="btn-local-seo-footer-desktop" href="/services/affordable-local-seo" class="hover:font-bold py-1">Local SEO</a>
            <a data-test="btn-national-seo-footer-desktop" href="/services/national-seo-company" class="hover:font-bold py-1">National SEO</a>
            <a data-test="btn-free-seo-footer-desktop" href="/free-seo-website-test" class="hover:font-bold py-1">Free SEO Website Test</a>
            <a data-test="btn-listings-footer-desktop" href="/business-listings/listings-management" class="hover:font-bold py-1">Listings Management</a>
            <a data-test="btn-display-ads-footer-desktop" href="/services/display-advertising" class="hover:font-bold py-1">Display Ads</a>
            <a data-test="btn-ppc-footer-desktop" href="/services/ppc-consulting" class="hover:font-bold py-1">PPC Consulting</a>
            <a data-test="btn-websites-footer-desktop" href="/small-business-marketing/websites" class="hover:font-bold py-1">Website Creation</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Resources</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-seo-faq-footer-desktop" href="/seo-faqs" class="hover:font-bold py-1">SEO FAQ</a>
            <a data-test="btn-ecommerce-footer-desktop" href="/seo-industry-guide/ecommerce-seo-guide" class="hover:font-bold py-1">Ecommerce SEO Guide</a>
            <a data-test="btn-construction-footer-desktop" href="/seo-industry-guide/seo-for-construction-companies" class="hover:font-bold py-1">Construction SEO Guide</a>
            <a data-test="btn-hvac-footer-desktop" href="/seo-industry-guide/seo-for-hvac" class="hover:font-bold py-1">HVAC SEO Guide</a>
            <a data-test="btn-homeyou-footer-desktop" href="/costs" class="hover:font-bold py-1">Home Services Cost</a>
          </div>
        </div>
      </div>
      <div class="border-t border-primary-light-v1 sm:border-none pt-6 sm:pt-0 flex flex-col col-span-8 sm:col-span-3 lg:col-span-2">
        <span class="font-serif">Manta Members</span>
                  <div class="flex items-center mt-6">
            <a data-test="btn-login-footer-desktop" class="hover:font-bold mr-4" href="/member/login">Log In</a>
            <a data-test="btn-login-footer-desktop" class="btn bg-primary-v1 text-white inline-block hover:font-bold" href="/member/register">Sign Up</a>
          </div>
                  <div class="mt-6">
                      <p class="mb-2">Search Manta's Directory to find the Small Business you're looking for</p>
            <a data-test="btn-index-footer-desktop" class="bg-primary-v1 py-2 px-4 rounded text-white inline-block hover:font-bold" href="/">Find a Business Near You</a>
                  </div>
      </div>
    </div>
    <div class="lg:hidden border-b border-t border-primary-light-v1 py-4 my-6 w-full">
      <div class="flex text-primary-light-v1">
        <a data-test="btn-help-footer-desktop-mobile" href="/contact" class="mr-6">Help</a>
        <a data-test="btn-termsConditions-footer-mobile" href="/terms-and-conditions" class="mr-6">Terms</a>
        <a data-test="btn-privacyPolicy-footer-mobile" href="/privacy-policy" class="mr-6">Privacy</a>
      </div>
    </div>
    <div class="flex lg:hidden justify-between items-center w-full">
      <div class="text-primary-light-v1 xs-device">
        © 2025 Manta Media Inc.<br>All rights reserved.
              </div>
      <div class="flex">
        <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
            <span class="sr-only">Manta on Twitter</span>
          </span>
        </a>
        <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
            <span class="sr-only">Manta on Facebook</span>
          </span>
        </a>
        <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
            <span class="sr-only">Manta on LinkedIn</span>
          </span>
        </a>
      </div>
    </div>
  </div>
  
      <script data-cfasync="false" async src="//cdn.intergient.com/1024347/72853/ramp.js"></script>
         </footer>          <script id="lazy-load-images">
  (function(hasObserver) {
    var createObservers = function(list, loadFn) {
      if (hasObserver) {
        var observer = new IntersectionObserver(function(entries, self) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              loadFn(entry.target);
              self.unobserve(entry.target);
            }
          });
        }, { rootMargin: '0px 0px 200px 0px' });

        list.forEach(function(el) {
          observer.observe(el);
        });
      } else {
        list.forEach(loadFn);
      }
    };

    var imgs = document.querySelectorAll('[lazy-load]');
    if (imgs.length) {
      createObservers(imgs, function(el) {
        el.setAttribute('src', el.getAttribute('lazy-load'));
      });
    }

    var bgs = document.querySelectorAll('.lazy');
    if (bgs.length) {
      createObservers(bgs, function(el) {
        el.classList.remove('lazy');
      });
    }
  })(typeof IntersectionObserver !== 'undefined');
</script>
  </body>
</html>



2)(base) saadmomin@Saads-MacBook-Air11 finder % >....                                       
  -H 'api-key: B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98' \
  -H 'app-id: tf-web' \
  -H 'cache-control: no-cache' \
  -b '__cf_bm=bkdw99zciEqn6FGYmLj7rJmqYS3VQquSkftfVGJ8zLg-1754335447-*******-wmg2Ihf9bclu1gQKZIK0sA_oubH3NvFioBx.CVND9nDns_R8QGo4myKVR06UndzAG8ZPqLhlcBUehE027zfqQiXaA7s5h0cM.fTBsP3BXZVwbehHK_45QWtyhrgwu4m5; sessionId=0d275f9f-783d-4bec-919c-388db8466e0b; sessionCreated=2025-08-04T19%3A24%3A08%2B00%3A00; device-id=704be081-0db7-4eab-a3ef-211a24dec5f6; utm_source=VBDA; utm_medium=affiliate; utm_campaign=truepeoplesearch; utm_term=first; ck_rsid=3757101803' \
  -H 'origin: https://www.truthfinder.com' \
  -H 'pragma: no-cache' \
  -H 'priority: u=1, i' \
  -H 'referer: https://www.truthfinder.com/search/?utm_source=VBDA&traffic[source]=VBDA&utm_medium=affiliate&traffic[medium]=affiliate&utm_campaign=truepeoplesearch&traffic[campaign]=Banner:truepeoplesearch&utm_term=first&traffic[term]=first&utm_content=&traffic[content]=&s1=truepeoplesearch&s2=Banner&s3=first&s4=&s5=&traffic[placement]=&traffic[funnel]=bg&ck_rsid=3757101803&firstName=saad&lastName=momin' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
  -H 'sec-ch-ua-mobile: ?1' \
  -H 'sec-ch-ua-platform: "Android"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36'
[{"names":[{"data_point_id":"adf18c17ddafe6779cd5a1357f221ecb","first":"Saad","middle":"Husen","last":"Momin","date_first_seen":{"date":{"month":2,"day":16,"year":2024}},"date_last_seen":{"date":{"month":6,"day":1,"year":2025}},"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM="}],"locations":[{"data_point_id":"c01be252058fe0ec363b0df6a0ed0b97","address":{"country":"US","city":"Phoenix","state":"Arizona","state_code":"AZ","provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM="},"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM=","searchPointer":"4:eJyKVlLSUQrIyE_Ny6xQ0lFyLMqsys9LBLGilHSUkJEBGMcCAgAA__9G4guX"}],"related_persons":[{"data_point_id":"da2a525fc851d1a581880072c12301cb","sub_type":"Parent","dobs":[{"data_point_id":"7b97e1cd430c3fe9a32aa0e7bd2516a7","date_range":{"start":{"month":8,"day":5,"year":1971},"end":{"month":8,"day":4,"year":1972}},"age":53,"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM="}],"names":[{"data_point_id":"eb983922c18c5dcab473f40fec8b90ca","first":"Mahebub","last":"Momin","date_first_seen":{"date":{"month":9,"day":1,"year":2022}},"date_last_seen":{"date":{"month":6,"day":1,"year":2025}}}],"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM=","searchPointer":"4:eJyKVvJNzEhNKk1S0lFS0lHyzc_NzAPREEEFGB-CDC3NDXUNLHQNTCEcIzDHBCJpZGFoYm5iaWFiYmxpbAlSkZmSCZKKBQQAAP__EdEXnQ=="},{"data_point_id":"8ed1f8c48d0d1b112d11711668a5e950","sub_type":"Parent","dobs":[{"data_point_id":"7b97e1cd430c3fe9a32aa0e7bd2516a7","date_range":{"start":{"month":8,"day":5,"year":1971},"end":{"month":8,"day":4,"year":1972}},"age":53,"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM="}],"names":[{"data_point_id":"20ec910c89919198b7f2352b93d8e8f8","first":"Maherun Nisha","last":"Momin","date_first_seen":{"date":{"month":9,"day":1,"year":2022}},"date_last_seen":{"date":{"month":6,"day":1,"year":2025}}}],"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM=","searchPointer":"4:eJyKVvJNzEgtKs1T8MsszkhU0lFS0lHyzc_NzAPRyFIKMFEIMrQ0N9Q1sNA1MIVwjMAcE4ikqZmRpYmlpZmhqaWppTlIMDMlEyQVCwgAAP__2H4cBQ=="},{"data_point_id":"fbf87775fe44cc40c725c97c069fa1cc","sub_type":"Sibling","dobs":[{"data_point_id":"d9cc6a49905d72c218b20a1b917e7dee","date_range":{"start":{"month":8,"day":5,"year":2001},"end":{"month":8,"day":4,"year":2002}},"age":23,"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM="}],"names":[{"data_point_id":"67b7861234d7d7e32f04dd2330db8e8c","first":"Shifa","last":"Momin","date_last_seen":{"date":{"month":6,"day":1,"year":2025}}}],"provider":"uh95dNfZT1UcbA5Zb4cCZ8DNaBM=","searchPointer":"4:eJyKVgrOyExLVNJRUtJR8s3PzcxT0oEIKcB4EGRkYGCoa2Cha2AK4RiBOSYQSTMzcwNLUzNLE0tLA3MzE0uQcGZKJkgyFhAAAP__pKIWYQ=="}]},{"names":[{"data_point_id":"2cd2dd0bf793e037d33564b47c88ef02","first":"Shahzad","middle":"Sadruddin","last":"Momin","date_first_seen":{"date":{"month":6,"day":11,"year":2010}},"date_last_seen":{"date":{"month":7,"day":29,"year":2024}},"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="},{"data_point_id":"f697a3174f8cefac3daabef85802de6e","first":"Shahzad","middle":"S","last":"Momin","date_first_seen":{"date":{"month":9,"day":2,"year":2000}},"date_last_seen":{"date":{"month":6,"day":2,"year":2025}},"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"locations":[{"data_point_id":"6cc7b21ba0e9a3a45b08d200a44fdb4e","address":{"country":"US","county":"Gwinnett","city":"Buford","state":"Georgia","state_code":"GA","street":"**** Antares Dr","zip_code":"*****","zip4":"****","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="},"date_first_seen":{"date":{"month":9,"day":6,"year":2007}},"date_last_seen":{"date":{"month":7,"day":31,"year":2025}},"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVjI2NzNXcMwrSSxKLVZwKVLSUXIqTcsvSlHSUXJPzS9Kz0wEsRyVdJSMDUwNLZV0lEzMDcyVdJQgyACMYwEBAAD__8VPEWQ="},{"data_point_id":"54f7563044ea1bddb3e9f9074bc0c762","address":{"country":"US","county":"Gwinnett","city":"Duluth","state":"Georgia","state_code":"GA","street":"**** Ridge Brook Trl","zip_code":"*****","zip4":"****","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="},"date_first_seen":{"date":{"month":8,"day":1,"year":2002}},"date_last_seen":{"date":{"month":10,"day":25,"year":2024}},"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVjI0MTJWCMpMSU9VcCrKz89WCCnKUdJRcinNKS3JUNJRck_NL0rPTASxHJV0lIwNDCzNlHSUzCwMDZR0lCDIAIxjAQEAAP__QKwTHg=="},{"data_point_id":"f2c986a8b1bc12246d2eda6681e265fe","address":{"country":"US","county":"Gwinnett","city":"Lawrenceville","state":"Georgia","state_code":"GA","street":"*** Overlook Park Ln","zip_code":"*****","zip4":"****","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="},"date_first_seen":{"date_range":{"start":{"month":3,"day":1,"year":2010},"end":{"month":3,"day":31,"year":2010}}},"date_last_seen":{"date":{"month":1,"day":7,"year":2012}},"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVjIxMFfwL0stysnPz1YISCzKVvDJU9JR8kksL0rNS04ty8zJSVXSUXJPzS9Kz0wEsRyVdJSMDQxMjJV0lMyNzUAUBBmAcSwgAAD__ytCFkE="},{"data_point_id":"2f929a2453cd4ab624f6b8f082abbf8e","address":{"country":"US","county":"Gwinnett","city":"Lawrenceville","state":"Georgia","state_code":"GA","street":"**** Downyshire Dr","zip_code":"*****","zip4":"****","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="},"date_first_seen":{"date":{"month":6,"day":22,"year":2004}},"date_last_seen":{"date":{"month":1,"day":13,"year":2011}},"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVjI0NDdQcMkvz6sszsgsSlVwKVLSUfJJLC9KzUtOLcvMyUlV0lFyT80vSs9MBLEclXSUjA0MTEyUdJTMDCxNlXSUIMgAjGMBAQAA___zBxWa"}],"related_persons":[{"dobs":[{"data_point_id":"f80a6ad9c829f43fb7ba935e028d7e09","date_range":{"start":{"month":8,"day":5,"year":1983},"end":{"month":8,"day":4,"year":1984}},"age":41,"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"names":[{"data_point_id":"4f484641d469a3ff17b0a7aa758b75b0","first":"Anisa","middle":"A","last":"Momin","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVnLMyyxOVNJRclTSUfLNz83MA7FBYgqOCjA-BBlaWhjrGljoGphCOCZgjglE0s3Cw1zX2MnEXElHqaQoMa-4NC8zH6w3FhAAAP__74IYug=="},{"dobs":[{"data_point_id":"eca42f352da4914d4e4a50155b332557","date_range":{"start":{"month":8,"day":5,"year":1977},"end":{"month":8,"day":4,"year":1978}},"age":47,"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"names":[{"data_point_id":"4ff5129972d23118e6226c1353704d6e","first":"Anisha","middle":"B","last":"Momin","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVnLMyyzOSFTSUXJS0lHyzc_NzFPSgQoqOCnABCDI0NLcXNfAQtfAFMKxAHNMIJImRuEeumF-JmZKOkolRYl5xaV5mflgvbGAAAAA__9EyBnI"},{"dobs":[{"data_point_id":"42d3a4741c18635a73ed8c6bb4c85ce6","date_range":{"start":{"month":8,"day":5,"year":1987},"end":{"month":8,"day":4,"year":1988}},"age":37,"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"names":[{"data_point_id":"05893c294b64212950c00fac648bba48","first":"Rozina","middle":"Sadruddin","last":"Momin","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVgrKr8rMS1TSUQpOTCkqTUnJzFPSUfLNzwXTEEkFuJQCTAKCDC0tzHUNLHQNTCEcCzDHBCJp6hbhpesRaGyppKNUUpSYV1yal5kP1hsLCAAA__8akyDP"},{"dobs":[{"data_point_id":"323d8ba9d49a6a0f875b0d0d56523d3e","date_range":{"start":{"month":8,"day":5,"year":1956},"end":{"month":8,"day":4,"year":1957}},"age":68,"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"names":[{"data_point_id":"8dc7a512f4b859a1b68b4af02d5e6133","first":"Sadruddin","middle":"N","last":"Momin","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVgpOTCkqTUnJzFPSUfJT0lHyzc8Fs-HiCn4KMDEIMrQ0NdM1sNA1MIVwzMEcE4ikm0-Ima6vobGRko5SSVFiXnFpXmY-WG8sIAAA__9N4hxa"},{"dobs":[{"data_point_id":"8d1ae0a9895f92f1475a6d683cc5a925","date_range":{"start":{"month":8,"day":5,"year":1949},"end":{"month":8,"day":4,"year":1950}},"age":75,"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"names":[{"data_point_id":"cb5a2a4824349ef353c85e0451d77741","first":"Sakina","middle":"S","last":"Momin","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVgpOzM7MS1TSUQpW0lHyzc_NzAOxwYIKwQowAQgytDSx1DWw0DUwBXNMDcAcE4ikn6GLoa6lqTGIW1KUmFdcmpeZD9YbCwgAAP__SeQZmw=="},{"dobs":[{"data_point_id":"117449aaf072918ccfb67c56e1a6db24","date_range":{"start":{"month":8,"day":5,"year":1959},"end":{"month":8,"day":4,"year":1960}},"age":65,"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"names":[{"data_point_id":"33d9cd35818e13ef101bf362e569bc17","first":"Zahira","middle":"Sadruddin","last":"Momin","provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ="}],"provider":"LJNTWZk5o6krjp0YPJDJzVy3KwQ=","searchPointer":"4:eJyKVopKzMgsSlTSUQpOTCkqTUnJzFPSUfLNzwXTEEkFuJQCTAKCDC1NLXUNLHQNTMEcMwMwxwQiaWnp7KNrGGDsraSjVFKUmFdcmpeZD9YbCwgAAP__BbUgeg=="}]}]%                            
(base) saadmomin@Saads-MacBook-Air11 finder % 


