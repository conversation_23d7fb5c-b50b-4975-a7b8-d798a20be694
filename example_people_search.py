#!/usr/bin/env python3
"""
Example usage of the new People Search functionality.

This script demonstrates how to use the enhanced Business Owner Scraper
to search for people by name and location, and cross-reference with business data.
"""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import BusinessOwnerScraper
from src.people_search import PersonSearchQuery

def example_basic_people_search():
    """Example of basic people search functionality."""
    print("🔍 Example: Basic People Search")
    print("=" * 50)
    
    try:
        # Initialize the scraper
        scraper = BusinessOwnerScraper("config.yaml")
        print("✅ BusinessOwnerScraper initialized")
        
        # Example 1: Search for a person by name and location
        print("\n📋 Searching for: <PERSON> in Houston, TX")
        
        results = scraper.search_person(
            first_name="<PERSON>",
            last_name="Smith", 
            city="Houston",
            state="TX"
        )
        
        print(f"✅ Found {len(results)} results for <PERSON>")
        
        # Display first result if available
        if results:
            result = results[0]
            print(f"\n📊 Sample Result:")
            print(f"   Name: {result.owner_name}")
            print(f"   Age: {result.owner_age or 'N/A'}")
            print(f"   Address: {result.address or 'N/A'}")
            print(f"   Phone: {result.phone or 'N/A'}")
            print(f"   Email: {result.email or 'N/A'}")
            print(f"   Source: {result.source}")
            print(f"   Confidence: {result.match_confidence or 'N/A'}")
            
            if result.aliases:
                print(f"   Aliases: {', '.join(result.aliases)}")
            
            if result.family_members:
                print(f"   Family: {len(result.family_members)} members")
                for member in result.family_members[:2]:  # Show first 2
                    print(f"     - {member.get('name', 'N/A')} ({member.get('relationship', 'N/A')})")
        
        # Clean up
        scraper.cleanup()
        
    except Exception as e:
        print(f"❌ Error in basic people search: {e}")

def example_cross_reference_search():
    """Example of cross-referenced people and business search."""
    print("\n🔗 Example: Cross-Referenced People & Business Search")
    print("=" * 50)
    
    try:
        # Initialize the scraper
        scraper = BusinessOwnerScraper("config.yaml")
        print("✅ BusinessOwnerScraper initialized")
        
        # Example: Search for a person and cross-reference with business data
        print("\n📋 Cross-referencing: John Smith with construction businesses in Houston, TX")
        
        results = scraper.search_person_with_cross_reference(
            first_name="John",
            last_name="Smith",
            city="Houston", 
            state="TX",
            business_type="construction",
            location="houston tx"
        )
        
        people_results = results.get('people', [])
        business_results = results.get('businesses', [])
        
        print(f"✅ Found {len(people_results)} people and {len(business_results)} businesses")
        
        # Display cross-referenced results
        if people_results:
            person = people_results[0]
            print(f"\n👤 Person Information:")
            print(f"   Name: {person.owner_name}")
            print(f"   Location: {person.location or 'N/A'}")
            print(f"   Phone: {person.phone or 'N/A'}")
            
            # Show business connections if any
            if hasattr(person, 'related_businesses') and person.related_businesses:
                print(f"\n🏢 Business Connections:")
                for business in person.related_businesses[:3]:  # Show first 3
                    print(f"   - {business.get('business_name', 'N/A')} ({business.get('business_type', 'N/A')})")
                    print(f"     Role: {business.get('role', 'N/A')}")
                    print(f"     Address: {business.get('address', 'N/A')}")
        
        # Clean up
        scraper.cleanup()
        
    except Exception as e:
        print(f"❌ Error in cross-reference search: {e}")

def example_advanced_people_search():
    """Example of advanced people search with PersonSearchQuery."""
    print("\n🎯 Example: Advanced People Search with PersonSearchQuery")
    print("=" * 50)
    
    try:
        from src.people_search import PersonSearchEngine
        
        # Initialize the people search engine directly
        engine = PersonSearchEngine("config.yaml")
        print("✅ PersonSearchEngine initialized")
        
        # Create a detailed search query
        query = PersonSearchQuery(
            first_name="Michael",
            last_name="Johnson",
            middle_name="David",
            city="Dallas",
            state="TX",
            age_range=(35, 55),
            phone="(*************"
        )
        
        print(f"\n📋 Advanced search for: {query.first_name} {query.middle_name} {query.last_name}")
        print(f"   Location: {query.city}, {query.state}")
        print(f"   Age range: {query.age_range}")
        print(f"   Phone: {query.phone}")
        
        # Perform search with specific sources
        results = engine.search_person(
            query=query,
            sources=['truthfinder_browser', 'truepeoplesearch']
        )
        
        print(f"✅ Found {len(results)} results")
        
        # Display detailed results
        for i, result in enumerate(results[:2], 1):  # Show first 2 results
            print(f"\n📊 Result {i}:")
            print(f"   Name: {result.owner_name}")
            print(f"   Age: {result.owner_age or 'N/A'}")
            print(f"   Address: {result.address or 'N/A'}")
            print(f"   Source: {result.source}")
            print(f"   Data Quality: {result.data_quality or 'N/A'}")
            print(f"   Completeness: {result.data_completeness or 'N/A'}")
            
            # Show phone numbers if available
            if result.phone_numbers:
                print(f"   Phone Numbers:")
                for phone in result.phone_numbers[:2]:
                    print(f"     - {phone.get('number', 'N/A')} ({phone.get('type', 'N/A')})")
            
            # Show email addresses if available
            if result.email_addresses:
                print(f"   Email Addresses:")
                for email in result.email_addresses[:2]:
                    print(f"     - {email.get('email', 'N/A')} ({email.get('type', 'N/A')})")
        
        # Clean up
        engine.cleanup()
        
    except Exception as e:
        print(f"❌ Error in advanced people search: {e}")

def example_data_export():
    """Example of exporting people search results."""
    print("\n📁 Example: Exporting People Search Results")
    print("=" * 50)
    
    try:
        # Initialize the scraper
        scraper = BusinessOwnerScraper("config.yaml")
        print("✅ BusinessOwnerScraper initialized")
        
        # Perform a people search
        print("\n📋 Searching for people to export...")
        
        results = scraper.search_person(
            first_name="Sarah",
            last_name="Williams",
            city="Austin",
            state="TX"
        )
        
        print(f"✅ Found {len(results)} results to export")
        
        if results:
            # Export to Excel
            excel_file = scraper.process_and_export(results, "excel")
            if excel_file:
                print(f"📊 Results exported to Excel: {excel_file}")
            
            # Export to CSV
            csv_file = scraper.process_and_export(results, "csv")
            if csv_file:
                print(f"📊 Results exported to CSV: {csv_file}")
        else:
            print("⚠️  No results to export")
        
        # Clean up
        scraper.cleanup()
        
    except Exception as e:
        print(f"❌ Error in data export: {e}")

def example_streamlit_integration():
    """Example of how the Streamlit interface works with people search."""
    print("\n🌐 Example: Streamlit Integration")
    print("=" * 50)
    
    print("The new people search functionality is integrated into the Streamlit interface:")
    print("")
    print("1. 🔍 Business Search - Original business owner search functionality")
    print("2. 👤 People Search - NEW! Search for people by name and location")
    print("3. 📊 Results & Analytics - View and analyze both business and people results")
    print("4. 📈 Usage Dashboard - Monitor usage across both search types")
    print("")
    print("To use the people search in Streamlit:")
    print("1. Run: streamlit run streamlit_app.py")
    print("2. Select '👤 People Search' from the sidebar")
    print("3. Enter person's name and location details")
    print("4. Select data sources (TruthFinder, TruePeopleSearch, etc.)")
    print("5. Enable cross-reference with business data if desired")
    print("6. Click 'Start People Search' to begin")
    print("")
    print("The interface provides the same user experience as business search:")
    print("- Real-time progress indicators")
    print("- Source selection and configuration")
    print("- Results analytics and visualization")
    print("- Export options (Excel, CSV, JSON)")
    print("- Demo mode for testing")

def main():
    """Run all people search examples."""
    print("🚀 People Search Functionality Examples")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    # Run examples
    example_basic_people_search()
    example_cross_reference_search()
    example_advanced_people_search()
    example_data_export()
    example_streamlit_integration()
    
    print("\n" + "=" * 60)
    print("🎉 People Search Examples Complete!")
    print("")
    print("💡 Next Steps:")
    print("1. Try the Streamlit interface: streamlit run streamlit_app.py")
    print("2. Customize the configuration in config.yaml")
    print("3. Add more data sources as needed")
    print("4. Integrate with your existing workflows")
    print("")
    print("📚 Key Features Added:")
    print("- Direct people search by name and location")
    print("- Cross-reference people with business ownership data")
    print("- Enhanced data structure with personal information")
    print("- Sophisticated deduplication for people records")
    print("- Same anti-bot protection as business searches")
    print("- Streamlit UI integration")
    print("- Comprehensive data export options")

if __name__ == "__main__":
    main()
