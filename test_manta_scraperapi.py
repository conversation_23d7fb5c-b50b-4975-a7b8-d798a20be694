#!/usr/bin/env python3
"""
Test Manta Browser Scraper with ScraperAPI Integration
This tests the ScraperAPI integration to bypass IP blocking for Manta.
"""

import sys
import os
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from scrapers.manta_browser_scraper import MantaBrowserScraper

class MockEngine:
    """Mock engine for testing."""
    def __init__(self):
        self.config = {
            'sources': {
                'manta_browser': {
                    'enabled': True,
                    'max_pages': 2,
                    'headless': True,
                    'scraper_api': {
                        'enabled': True,
                        'api_key': '89a467f0dd31dee55c2aaf9fa1fc0645',
                        'use_premium': True,
                        'country_code': 'US',
                        'render_js': False
                    }
                }
            }
        }

def test_scraperapi_integration():
    """Test ScraperAPI integration with Manta scraper."""
    print("🌐 Testing Manta Browser Scraper with ScraperAPI Integration")
    print("=" * 70)
    print("This test will:")
    print("1. 🔧 Test ScraperAPI configuration")
    print("2. 🌐 Test direct ScraperAPI requests to Manta")
    print("3. 🔄 Test intelligent retry logic (ScraperAPI → Cookies → Browser)")
    print("4. 📊 Compare success rates with and without ScraperAPI")
    print("5. 💰 Monitor ScraperAPI usage and costs")
    print()
    
    # Initialize scraper
    engine = MockEngine()
    scraper = MantaBrowserScraper(engine)
    
    print("✅ Scraper initialized with ScraperAPI integration")
    print(f"   ScraperAPI Key: {scraper.scraper_api_key[:20]}...")
    print(f"   ScraperAPI URL: {scraper.scraper_api_url}")
    print(f"   Use ScraperAPI: {scraper.use_scraper_api}")
    print()
    
    # Test cases for different business types
    test_cases = [
        ("restaurant", "houston tx", "54_C4_000"),
        ("construction", "dallas tx", "54_B1_000"),
        ("plumbing", "austin tx", "54_B1_KCQ")
    ]
    
    results = []
    total_start_time = time.time()
    
    for i, (business_type, location, expected_category) in enumerate(test_cases, 1):
        print(f"🔍 Test {i}/{len(test_cases)}: {business_type.upper()} in {location.upper()}")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            # Reset scraper state
            scraper.cookie_refresh_count = 0
            scraper.fresh_cookies = None
            
            # Test direct ScraperAPI method first
            print("   🌐 Testing direct ScraperAPI method...")
            category_id = scraper._get_category_id(business_type)
            business_slug = scraper._format_business_type_for_url(business_type)
            location_slug = scraper._format_location_for_url(location)
            
            # Test ScraperAPI directly
            scraperapi_start = time.time()
            scraperapi_data = scraper._search_with_scraper_api(category_id, business_slug, location_slug, 1)
            scraperapi_duration = time.time() - scraperapi_start
            
            scraperapi_success = False
            scraperapi_results = 0
            
            if scraperapi_data and not scraper._is_cloudflare_challenge(scraperapi_data):
                print(f"   ✅ ScraperAPI method successful! ({scraperapi_duration:.2f}s)")
                scraperapi_success = True
                
                # Try to parse results
                try:
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(scraperapi_data, 'html.parser')
                    
                    # Look for business listings
                    business_elements = soup.select('.business-card, .listing-item, .company-listing, [data-business-id]')
                    if not business_elements:
                        business_elements = soup.find_all('div', class_=True)
                    
                    scraperapi_results = len([elem for elem in business_elements if elem.get_text(strip=True)])
                    print(f"   📊 ScraperAPI found {scraperapi_results} potential business elements")
                    
                except Exception as e:
                    print(f"   ⚠️  Could not parse ScraperAPI results: {e}")
                    
            else:
                print(f"   ❌ ScraperAPI method failed or got challenge page ({scraperapi_duration:.2f}s)")
                if scraperapi_data:
                    print(f"   📄 Response preview: {scraperapi_data[:200]}...")
            
            # Now test the full intelligent retry system
            print("   🎯 Testing full intelligent retry system...")
            full_start = time.time()
            search_results = scraper.search(business_type, location)
            full_duration = time.time() - full_start
            
            end_time = time.time()
            total_duration = end_time - start_time
            
            # Analyze results
            full_result_count = len(search_results) if search_results else 0
            cookie_refreshes = scraper.cookie_refresh_count
            
            print(f"   📊 Full system results: {full_result_count} businesses found")
            print(f"   🔄 Cookie refreshes used: {cookie_refreshes}/{scraper.max_cookie_refreshes}")
            print(f"   ⏱️  Total duration: {total_duration:.2f} seconds")
            
            # Determine overall success
            if full_result_count > 0:
                status = "✅ SUCCESS"
                success = True
            elif scraperapi_success:
                status = "🔄 SCRAPERAPI SUCCESS (parsing issues)"
                success = True
            else:
                status = "❌ ALL METHODS FAILED"
                success = False
            
            print(f"   🎯 Overall Status: {status}")
            
            # Store results
            results.append({
                'business_type': business_type,
                'location': location,
                'scraperapi_success': scraperapi_success,
                'scraperapi_duration': scraperapi_duration,
                'scraperapi_results': scraperapi_results,
                'full_result_count': full_result_count,
                'cookie_refreshes': cookie_refreshes,
                'total_duration': total_duration,
                'success': success,
                'status': status
            })
            
        except Exception as e:
            end_time = time.time()
            total_duration = end_time - start_time
            
            print(f"   ❌ ERROR: {str(e)[:100]}...")
            print(f"   ⏱️  Duration: {total_duration:.2f} seconds")
            
            results.append({
                'business_type': business_type,
                'location': location,
                'scraperapi_success': False,
                'scraperapi_duration': 0,
                'scraperapi_results': 0,
                'full_result_count': 0,
                'cookie_refreshes': 0,
                'total_duration': total_duration,
                'success': False,
                'status': f"❌ ERROR: {str(e)[:50]}..."
            })
        
        print()
        
        # Small delay between tests
        if i < len(test_cases):
            print("   ⏳ Waiting 5 seconds before next test...")
            time.sleep(5)
            print()
    
    total_duration = time.time() - total_start_time
    
    # Generate comprehensive report
    print("📊 SCRAPERAPI INTEGRATION TEST RESULTS")
    print("=" * 70)
    
    # Summary statistics
    total_tests = len(results)
    scraperapi_successes = sum(1 for r in results if r['scraperapi_success'])
    full_successes = sum(1 for r in results if r['success'])
    total_scraperapi_results = sum(r['scraperapi_results'] for r in results)
    total_full_results = sum(r['full_result_count'] for r in results)
    avg_scraperapi_duration = sum(r['scraperapi_duration'] for r in results) / total_tests
    avg_total_duration = sum(r['total_duration'] for r in results) / total_tests
    
    print(f"📈 SUMMARY STATISTICS:")
    print(f"   Total tests: {total_tests}")
    print(f"   ScraperAPI successes: {scraperapi_successes}/{total_tests} ({scraperapi_successes/total_tests*100:.1f}%)")
    print(f"   Full system successes: {full_successes}/{total_tests} ({full_successes/total_tests*100:.1f}%)")
    print(f"   ScraperAPI results found: {total_scraperapi_results}")
    print(f"   Full system results found: {total_full_results}")
    print(f"   Average ScraperAPI duration: {avg_scraperapi_duration:.2f} seconds")
    print(f"   Average total duration: {avg_total_duration:.2f} seconds")
    print(f"   Total test duration: {total_duration:.2f} seconds")
    print()
    
    # Detailed results table
    print(f"📋 DETAILED RESULTS:")
    print(f"{'Business':<12} {'Location':<12} {'ScraperAPI':<12} {'Results':<8} {'Duration':<10} {'Status':<25}")
    print("-" * 80)
    
    for r in results:
        scraperapi_status = "✅" if r['scraperapi_success'] else "❌"
        print(f"{r['business_type']:<12} {r['location']:<12} {scraperapi_status}{r['scraperapi_results']:<11} {r['full_result_count']:<8} {r['total_duration']:<10.1f} {r['status']:<25}")
    
    print()
    
    # ScraperAPI effectiveness analysis
    print(f"🌐 SCRAPERAPI EFFECTIVENESS:")
    if scraperapi_successes > 0:
        print(f"✅ ScraperAPI successfully bypassed IP blocking in {scraperapi_successes} out of {total_tests} tests")
        print(f"⚡ Average ScraperAPI response time: {avg_scraperapi_duration:.2f} seconds")
        print(f"📊 ScraperAPI found {total_scraperapi_results} total business elements")
        
        if scraperapi_successes == total_tests:
            print(f"🎉 PERFECT: ScraperAPI bypassed all IP blocks!")
        elif scraperapi_successes >= total_tests * 0.7:
            print(f"🚀 EXCELLENT: High ScraperAPI success rate!")
        else:
            print(f"⚠️  MODERATE: ScraperAPI working but could be improved")
    else:
        print(f"❌ ScraperAPI did not successfully bypass IP blocking in any tests")
        print(f"💡 This could indicate:")
        print(f"   - ScraperAPI account issues")
        print(f"   - Manta has very advanced protection")
        print(f"   - Need different ScraperAPI parameters")
    
    print()
    
    # Cost estimation
    scraperapi_requests = scraperapi_successes * 2  # Assuming some retries
    estimated_cost = scraperapi_requests * 0.001  # Rough estimate: $0.001 per request
    
    print(f"💰 COST ESTIMATION:")
    print(f"   Estimated ScraperAPI requests: {scraperapi_requests}")
    print(f"   Estimated cost: ${estimated_cost:.4f}")
    print(f"   Cost per successful result: ${estimated_cost/max(total_scraperapi_results, 1):.4f}")
    
    print()
    
    # Final recommendations
    print(f"🎯 RECOMMENDATIONS:")
    
    if scraperapi_successes > 0:
        print(f"✅ ScraperAPI integration is working - continue using it as primary method")
        print(f"🔄 Keep cookie refresh and browser automation as fallbacks")
        print(f"💡 Consider enabling JavaScript rendering for better results")
    else:
        print(f"⚠️  ScraperAPI integration needs optimization")
        print(f"🔧 Try different ScraperAPI parameters:")
        print(f"   - Enable JavaScript rendering")
        print(f"   - Use different country codes")
        print(f"   - Try residential vs datacenter IPs")
    
    if avg_scraperapi_duration < 10:
        print(f"⚡ ScraperAPI performance is excellent (avg {avg_scraperapi_duration:.1f}s)")
    else:
        print(f"⏳ ScraperAPI performance could be improved (avg {avg_scraperapi_duration:.1f}s)")
    
    return results

def main():
    """Main test function."""
    print("🤖 Manta ScraperAPI Integration Test")
    print("Testing ScraperAPI to bypass IP blocking for Manta scraping")
    print()
    
    results = test_scraperapi_integration()
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"manta_scraperapi_test_results_{timestamp}.txt"
    
    with open(filename, 'w') as f:
        f.write("Manta ScraperAPI Integration Test Results\n")
        f.write(f"Generated: {datetime.now()}\n")
        f.write("=" * 50 + "\n\n")
        
        for r in results:
            f.write(f"Business Type: {r['business_type']}\n")
            f.write(f"Location: {r['location']}\n")
            f.write(f"ScraperAPI Success: {r['scraperapi_success']}\n")
            f.write(f"ScraperAPI Duration: {r['scraperapi_duration']:.2f}s\n")
            f.write(f"ScraperAPI Results: {r['scraperapi_results']}\n")
            f.write(f"Full Results: {r['full_result_count']}\n")
            f.write(f"Total Duration: {r['total_duration']:.2f}s\n")
            f.write(f"Status: {r['status']}\n")
            f.write("-" * 30 + "\n")
    
    print(f"📁 Detailed results saved to: {filename}")
    print()
    print("🎉 ScraperAPI integration test complete!")

if __name__ == '__main__':
    main()
