#!/usr/bin/env python3
"""
Fresh Cookie Extractor - Simple tool to get fresh browser cookies for API calls.
Just opens Chrome, lets you browse, then extracts cookies for API updates.
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from playwright.async_api import async_playwright

async def extract_cookies_for_site(site_url: str, site_name: str, automated: bool = False):
    """
    Open browser, let user browse to site, then extract cookies.

    Args:
        site_url: URL to open (e.g., "https://www.truthfinder.com")
        site_name: Name for saving cookies (e.g., "truthfinder")
        automated: If True, run in headless mode for automated extraction
    """
    if not automated:
        print(f"🌐 Opening Chrome browser for {site_name}")
        print(f"📍 URL: {site_url}")
        print("=" * 60)
        print("INSTRUCTIONS:")
        print("1. <PERSON><PERSON><PERSON> will open to the website")
        print("2. Browse normally, accept any popups/terms")
        print("3. When ready, press ENTER in this terminal")
        print("4. Cookies will be extracted automatically")
        print("=" * 60)
    else:
        print(f"🤖 Automated cookie extraction for {site_name}")
        print(f"📍 URL: {site_url}")

    async with async_playwright() as p:
        # Launch browser - headless for automated mode
        browser = await p.chromium.launch(
            headless=automated,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage'
            ]
        )
        
        # Create context with realistic settings
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York'
        )
        
        # Add stealth script
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        page = await context.new_page()
        
        # Navigate to the site
        print(f"🚀 Navigating to {site_url}...")
        await page.goto(site_url, wait_until="networkidle")
        
        if not automated:
            # Wait for user to browse and interact
            print(f"\n✅ Browser opened! Browse {site_name} normally.")
            print("💡 Accept any terms, login if needed, browse around.")
            print("⏳ When you're done, press ENTER here to extract cookies...")

            # Wait for user input
            input()
        else:
            # Automated mode - wait a bit and try to handle popups automatically
            print("🤖 Automated mode: handling popups and extracting cookies...")
            await page.wait_for_timeout(5000)  # Wait 5 seconds

            # Try to handle common popups automatically
            try:
                # Look for common accept buttons
                accept_selectors = [
                    'button:has-text("I AGREE")',
                    'button:has-text("I Agree")',
                    'button:has-text("Accept")',
                    'button:has-text("OK")',
                    '.cookie-accept',
                    '.accept-button',
                    'button.green'
                ]

                for selector in accept_selectors:
                    button = await page.query_selector(selector)
                    if button:
                        await button.click()
                        print(f"✅ Clicked accept button: {selector}")
                        await page.wait_for_timeout(2000)
                        break
            except Exception as e:
                print(f"⚠️  Could not handle popups automatically: {e}")

            # Wait a bit more for any redirects/loading
            await page.wait_for_timeout(3000)
        
        # Extract cookies
        print("🍪 Extracting cookies...")
        cookies = await context.cookies()
        
        # Extract other useful info
        current_url = page.url
        user_agent = await page.evaluate('() => navigator.userAgent')
        local_storage = await page.evaluate('() => Object.assign({}, window.localStorage)')
        session_storage = await page.evaluate('() => Object.assign({}, window.sessionStorage)')
        
        # Close browser
        await browser.close()
        
        # Process cookies
        cookie_dict = {}
        for cookie in cookies:
            cookie_dict[cookie['name']] = cookie['value']
        
        # Create session data
        session_data = {
            'site': site_name,
            'url': current_url,
            'timestamp': datetime.now().isoformat(),
            'user_agent': user_agent,
            'cookies': cookie_dict,
            'local_storage': local_storage,
            'session_storage': session_storage,
            'cookie_count': len(cookies)
        }
        
        # Save to file
        os.makedirs('sessions', exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sessions/{site_name}_cookies_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(session_data, f, indent=2)
        
        print(f"✅ Cookies extracted successfully!")
        print(f"📁 Saved to: {filename}")
        print(f"🍪 Total cookies: {len(cookies)}")
        
        # Show important cookies
        important_cookies = ['__cf_bm', 'sessionId', 'device-id', 'sessionCreated', 'cf_clearance']
        print(f"\n🔑 Key cookies found:")
        for cookie_name in important_cookies:
            if cookie_name in cookie_dict:
                value = cookie_dict[cookie_name]
                print(f"   {cookie_name}: {value[:50]}{'...' if len(value) > 50 else ''}")
        
        return session_data

def generate_curl_command(session_data: dict, api_endpoint: str = ""):
    """Generate a curl command with the fresh cookies."""
    cookies = session_data.get('cookies', {})
    user_agent = session_data.get('user_agent', '')
    
    # Build cookie string
    cookie_string = '; '.join([f"{name}={value}" for name, value in cookies.items()])
    
    if session_data['site'] == 'truthfinder':
        api_endpoint = api_endpoint or "https://api2.truthfinder.com/v1/people/?firstName=John&lastName=Smith&fields=names%2Clocations%2Crelated_persons"
        
        curl_command = f"""curl '{api_endpoint}' \\
  -H 'accept: */*' \\
  -H 'accept-language: en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7' \\
  -H 'api-key: B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98' \\
  -H 'app-id: tf-web' \\
  -H 'cache-control: no-cache' \\
  -b '{cookie_string}' \\
  -H 'origin: https://www.truthfinder.com' \\
  -H 'pragma: no-cache' \\
  -H 'referer: https://www.truthfinder.com/search/' \\
  -H 'user-agent: {user_agent}'"""
        
    elif session_data['site'] == 'manta':
        api_endpoint = api_endpoint or "https://www.manta.com/more-results/54_C4_000/restaurant?pg=1"
        
        curl_command = f"""curl '{api_endpoint}' \\
  -H 'accept: */*' \\
  -H 'accept-language: en-GB,en-US;q=0.9,en;q=0.8' \\
  -H 'cache-control: no-cache' \\
  -b '{cookie_string}' \\
  -H 'connection: keep-alive' \\
  -H 'user-agent: {user_agent}'"""
    
    else:
        curl_command = f"# Cookies ready for {session_data['site']} API calls"
    
    return curl_command

async def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("🤖 Fresh Cookie Extractor")
        print("=" * 30)
        print("Usage:")
        print("  python get_fresh_cookies.py truthfinder [--automated]")
        print("  python get_fresh_cookies.py manta [--automated]")
        print("  python get_fresh_cookies.py both [--automated]")
        print()
        print("Options:")
        print("  --automated  Run in headless mode for automated cookie refresh")
        print()
        print("This will:")
        print("  1. Open Chrome browser")
        print("  2. Let you browse and accept terms normally")
        print("  3. Extract fresh cookies for API calls")
        print("  4. Generate ready-to-use curl commands")
        return

    command = sys.argv[1].lower()
    automated = "--automated" in sys.argv
    
    if command in ['truthfinder', 'both']:
        print("🔍 Getting TruthFinder cookies...")
        tf_session = await extract_cookies_for_site("https://www.truthfinder.com", "truthfinder", automated)
        
        # Generate curl command
        curl_cmd = generate_curl_command(tf_session)
        
        # Save curl command
        with open('sessions/truthfinder_curl_command.txt', 'w') as f:
            f.write(curl_cmd)
        
        print(f"\n📋 TruthFinder curl command saved to: sessions/truthfinder_curl_command.txt")
        print("🧪 Test it by running the command!")
    
    if command in ['manta', 'both']:
        print("\n🏢 Getting Manta cookies...")
        manta_session = await extract_cookies_for_site("https://www.manta.com", "manta", automated)
        
        # Generate curl command  
        curl_cmd = generate_curl_command(manta_session)
        
        # Save curl command
        with open('sessions/manta_curl_command.txt', 'w') as f:
            f.write(curl_cmd)
        
        print(f"\n📋 Manta curl command saved to: sessions/manta_curl_command.txt")
        print("🧪 Test it by running the command!")
    
    print(f"\n🎉 Cookie extraction completed!")
    print(f"💡 Use the generated curl commands to test API access")
    print(f"🔄 Update your API configurations with the fresh cookies")

if __name__ == '__main__':
    asyncio.run(main())
