"""
TruthFinder API-based scraper for business owner information.
Replaces traditional web scraping with reliable API calls.
"""

import logging
from typing import List, Optional
from datetime import datetime

from .base_scraper import BaseScraper
from ..core import ScrapingResult
from ..apis.truthfinder_api import TruthFinderAPI


class TruthFinderScraper(BaseScraper):
    """TruthFinder API-based scraper for people search."""

    def __init__(self, engine):
        """Initialize TruthFinder scraper with API client."""
        super().__init__(engine, "truthfinder_api")
        self.name = "TruthFinder API"
        self.source = "truthfinder_api"

        # Get TruthFinder configuration
        truthfinder_config = engine.config.get('sources', {}).get('truthfinder_api', {})

        # Initialize API client
        self.api_client = TruthFinderAPI(truthfinder_config)
        self.enabled = truthfinder_config.get('enabled', False) and self.api_client.enabled

        self.logger = logging.getLogger(__name__)

        if self.enabled:
            self.logger.info("TruthFinder API scraper initialized successfully")
        else:
            self.logger.warning("TruthFinder API scraper is disabled")
    
    def is_enabled(self) -> bool:
        """Check if TruthFinder scraper is enabled."""
        return self.enabled

    # Required abstract methods from BaseScraper
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search for business owners using TruthFinder API (implements abstract method)."""
        return self.scrape_business_owners(business_type, location)

    def extract_owner_info(self, soup, url: str) -> List[ScrapingResult]:
        """Extract owner info from HTML (not used for API-based scraper)."""
        # This method is not used for API-based scraping, but required by abstract base class
        return []

    def _extract_listing_urls(self, soup) -> List[str]:
        """Extract listing URLs from search results (not used for API-based scraper)."""
        # This method is not used for API-based scraping, but required by abstract base class
        return []
    
    def scrape_business_owners(self, business_type: str, location: str) -> List[ScrapingResult]:
        """
        Scrape business owners using TruthFinder API.
        
        Args:
            business_type: Type of business (e.g., 'restaurant', 'construction')
            location: Location to search (e.g., 'houston tx')
            
        Returns:
            List of ScrapingResult objects
        """
        if not self.enabled:
            self.logger.warning("TruthFinder scraper is disabled")
            return []
        
        try:
            self.logger.info(f"Searching TruthFinder API for {business_type} owners in {location}")
            
            # Use API to search for business owners
            results = self.api_client.search_business_owners(business_type, location)
            
            # Process and validate results
            processed_results = []
            for result in results:
                if self._validate_result(result, business_type, location):
                    # Add scraper-specific metadata
                    result.raw_data.update({
                        'scraper': self.name,
                        'scraper_version': '1.0',
                        'api_based': True,
                        'reliability_score': 0.9
                    })
                    processed_results.append(result)
            
            self.logger.info(f"TruthFinder API returned {len(processed_results)} valid results")
            return processed_results
            
        except Exception as e:
            self.logger.error(f"Error in TruthFinder scraping: {e}")
            return []
    
    def _validate_result(self, result: ScrapingResult, business_type: str, location: str) -> bool:
        """Validate a scraping result."""
        try:
            # Basic validation
            if not result.owner_name or len(result.owner_name.strip()) < 2:
                return False
            
            # Check if owner name looks realistic
            name_parts = result.owner_name.strip().split()
            if len(name_parts) < 2:  # Should have at least first and last name
                return False
            
            # Check for obvious invalid names
            invalid_patterns = ['test', 'example', 'sample', 'unknown', 'n/a']
            name_lower = result.owner_name.lower()
            if any(pattern in name_lower for pattern in invalid_patterns):
                return False
            
            # Validate location relevance if address is available
            if result.address:
                location_parts = location.lower().split()
                address_lower = result.address.lower()
                
                # Check if at least one location part is in the address
                location_match = any(part in address_lower for part in location_parts if len(part) > 2)
                if not location_match:
                    self.logger.debug(f"Location mismatch for {result.owner_name}: {result.address}")
                    # Don't reject, but lower confidence
                    if 'confidence_score' in result.raw_data:
                        result.raw_data['confidence_score'] *= 0.8
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating result: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test the API connection."""
        if not self.enabled:
            return False
        
        return self.api_client.test_api_connection()
    
    def get_scraper_status(self) -> dict:
        """Get current scraper status."""
        base_status = {
            'name': self.name,
            'source': self.source,
            'enabled': self.enabled,
            'scraper_type': 'api_based'
        }

        if self.enabled:
            api_status = self.api_client.get_api_status()
            base_status.update({
                'api_status': api_status,
                'connection_test': self.test_connection()
            })

        return base_status
    
    def search_specific_person(self, first_name: str, last_name: str, location: str) -> List[ScrapingResult]:
        """
        Search for a specific person by name and location.
        
        Args:
            first_name: Person's first name
            last_name: Person's last name
            location: Location to search
            
        Returns:
            List of ScrapingResult objects
        """
        if not self.enabled:
            return []
        
        try:
            self.logger.info(f"Searching for specific person: {first_name} {last_name} in {location}")
            
            # Parse location
            location_parts = location.strip().split()
            if len(location_parts) >= 2:
                state = location_parts[-1].upper()
                city = " ".join(location_parts[:-1]).title()
            else:
                city = location.title()
                state = ""
            
            # Make specific person search
            search_params = {
                "firstName": first_name,
                "lastName": last_name,
                "city": city,
                "state": state
            }
            
            import requests
            response = requests.post(
                f"{self.api_client.base_url}/search",
                headers=self.api_client.headers,
                json=search_params,
                timeout=self.api_client.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                if data:
                    # Parse results but don't infer business type
                    results = []
                    for person_data in data if isinstance(data, list) else [data]:
                        result = ScrapingResult(
                            owner_name=f"{first_name} {last_name}",
                            business_name="Unknown Business",
                            business_type="unknown",
                            location=location,
                            source="truthfinder_api",
                            scraped_at=datetime.now(),
                            raw_data={
                                'api_response': person_data,
                                'search_type': 'specific_person',
                                'api_source': 'truthfinder'
                            }
                        )
                        results.append(result)
                    
                    return results
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error searching for specific person: {e}")
            return []
    
    def get_usage_stats(self) -> dict:
        """Get API usage statistics."""
        if not self.enabled:
            return {'enabled': False}
        
        return {
            'enabled': True,
            'requests_made': self.api_client.request_count,
            'rate_limit': self.api_client.rate_limit,
            'last_request': self.api_client.last_request_time,
            'api_key_configured': bool(self.api_client.api_key)
        }
