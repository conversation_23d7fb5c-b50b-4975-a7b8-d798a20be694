"""
LinkedIn.com scraper for business owner information.
Uses Google search with site: operator as specified by client requirements.
Note: LinkedIn has strict anti-scraping measures, so we use Google search results.
"""

import re
import json
from typing import List
from urllib.parse import urljoin, quote_plus
import requests

from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..core import ScrapingResult
from ..utils.google_search import GoogleSearchEngine


class LinkedInScraper(BaseScraper):
    """Scraper for LinkedIn business owner information using Google search with site: operator."""

    def __init__(self, engine):
        super().__init__(engine, 'linkedin')
        self.google_search = GoogleSearchEngine(engine.config.get('anti_bot', {}))
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search for LinkedIn profiles using Google site: operator."""
        if not self.is_enabled():
            return []

        self.logger.info(f"Searching LinkedIn via Google for '{business_type}' owners in '{location}'")

        results = []

        # Use Google search with site: operator as specified by client
        # Pattern: site:linkedin.com "Owner" "lawn care" "Dallas"
        google_results = self.google_search.search_site_for_owners('linkedin.com', business_type, location)

        self.logger.info(f"Found {len(google_results)} LinkedIn profiles from Google search")

        # Extract owner information from Google search results
        for google_result in google_results:
            linkedin_results = self._extract_linkedin_info(google_result, business_type, location)
            results.extend(linkedin_results)

        return results
    
    def _build_search_queries(self, business_type: str, location: str) -> List[str]:
        """Build search queries for finding LinkedIn profiles."""
        queries = [
            f'site:linkedin.com/in "Owner" "{business_type}" "{location}"',
            f'site:linkedin.com/in "CEO" "{business_type}" "{location}"',
            f'site:linkedin.com/in "Founder" "{business_type}" "{location}"',
            f'site:linkedin.com/in "President" "{business_type}" "{location}"',
            f'site:linkedin.com/company "{business_type}" "{location}" owner',
            f'site:linkedin.com/company "{business_type}" "{location}" CEO'
        ]
        return queries
    
    def _google_search_linkedin(self, query: str) -> List[dict]:
        """Perform Google search for LinkedIn profiles."""
        results = []
        
        try:
            search_url = f"{self.search_engine_url}?q={quote_plus(query)}&num=20"
            
            # Use anti-bot protection for Google search
            response = self.make_request(search_url)
            if not response:
                return results
            
            soup = BeautifulSoup(response.text, 'lxml')
            
            # Extract search results
            search_results = soup.select('div.g, div[data-ved]')
            
            for result_div in search_results:
                link_elem = result_div.select_one('h3 a, a h3')
                if not link_elem:
                    continue
                
                url = link_elem.get('href')
                if not url or 'linkedin.com' not in url:
                    continue
                
                title = link_elem.get_text(strip=True)
                
                # Get snippet
                snippet_elem = result_div.select_one('.VwiC3b, .s3v9rd, .st')
                snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                
                results.append({
                    'url': url,
                    'title': title,
                    'snippet': snippet
                })
        
        except Exception as e:
            self.logger.error(f"Error in Google search for LinkedIn: {e}")
        
        return results
    
    def _extract_linkedin_info(self, search_result: dict, business_type: str, location: str) -> List[ScrapingResult]:
        """Extract owner information from LinkedIn search result."""
        results = []
        
        url = search_result['url']
        title = search_result['title']
        snippet = search_result['snippet']
        
        # Extract name from title (LinkedIn profile titles usually contain the person's name)
        owner_name = self._extract_name_from_title(title)
        
        # Extract business name from snippet or title
        business_name = self._extract_business_name_from_text(snippet + " " + title)
        
        # Extract job title/role
        job_title = self._extract_job_title(title, snippet)
        
        if owner_name:
            result = ScrapingResult(
                owner_name=self.clean_text(owner_name),
                business_name=self.clean_text(business_name) if business_name else None,
                business_type=business_type,
                location=location,
                source=self.source_name,
                url=url,
                raw_data={
                    'title': title,
                    'snippet': snippet,
                    'job_title': job_title
                }
            )
            results.append(result)
        
        return results
    
    def _extract_name_from_title(self, title: str) -> str:
        """Extract person name from LinkedIn profile title."""
        # LinkedIn profile titles are usually in format: "Name - Job Title at Company | LinkedIn"
        
        # Remove LinkedIn suffix
        title = re.sub(r'\s*\|\s*LinkedIn.*$', '', title, flags=re.IGNORECASE)
        
        # Extract name before the first dash or "at"
        name_match = re.match(r'^([^-]+?)(?:\s*-\s*|\s+at\s+)', title, re.IGNORECASE)
        if name_match:
            name = name_match.group(1).strip()
            # Validate it looks like a name
            if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*$', name):
                return name
        
        # Fallback: try to extract name from the beginning
        words = title.split()
        if len(words) >= 2:
            potential_name = ' '.join(words[:2])
            if re.match(r'^[A-Z][a-z]+\s+[A-Z][a-z]+$', potential_name):
                return potential_name
        
        return ""
    
    def _extract_business_name_from_text(self, text: str) -> str:
        """Extract business name from text."""
        # Look for "at Company" pattern
        at_match = re.search(r'\bat\s+([A-Z][A-Za-z0-9\s&,.-]+?)(?:\s*\||$)', text)
        if at_match:
            company = at_match.group(1).strip()
            # Clean up common suffixes
            company = re.sub(r'\s*(Inc\.|LLC|Corp\.|Corporation|Company).*$', '', company, flags=re.IGNORECASE)
            return company.strip()
        
        # Look for company names in quotes
        quote_match = re.search(r'"([A-Z][A-Za-z0-9\s&,.-]+?)"', text)
        if quote_match:
            return quote_match.group(1).strip()
        
        return ""
    
    def _extract_job_title(self, title: str, snippet: str) -> str:
        """Extract job title from title or snippet."""
        text = title + " " + snippet
        
        # Look for job titles
        job_patterns = [
            r'(Owner|CEO|President|Founder|Managing Director|Principal)',
            r'(Chief Executive Officer|Chief Operating Officer)',
            r'(Co-Founder|Co-Owner)'
        ]
        
        for pattern in job_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return ""
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build search URL (not used for LinkedIn scraper)."""
        return self.search_engine_url
    
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Not used for LinkedIn scraper."""
        return []
    
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Not used for LinkedIn scraper - we extract from search results."""
        return []
