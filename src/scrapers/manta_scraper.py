"""
Manta.com scraper for business owner information.
Uses Google search with site: operator as specified by client requirements.
"""

import re
import json
from typing import List, Dict, Any
from urllib.parse import urljoin, quote_plus
import requests

from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..core import ScrapingR<PERSON>ult
from ..utils.google_search import GoogleSearchEngine


class MantaScraper(BaseScraper):
    """Scraper for Manta.com business listings using Google search with site: operator."""

    def __init__(self, engine):
        super().__init__(engine, 'manta')
        self.google_search = GoogleSearchEngine(engine.config)

        # Enhanced Cloudflare bypass for Manta
        self.cloudflare_bypass_enabled = engine.config.get('sources', {}).get('manta', {}).get('cloudflare_bypass', True)
        if self.cloudflare_bypass_enabled:
            self.logger.info("Enhanced Cloudflare bypass enabled for Man<PERSON>")
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search Manta for business owners using Google site: operator."""
        if not self.is_enabled():
            return []

        self.logger.info(f"Searching Manta via Google for '{business_type}' owners in '{location}'")

        results = []

        # Use Google search with site: operator as specified by client
        # Pattern: site:manta.com "Owner" "lawn care" "Dallas"
        google_results = self.google_search.search_site_for_owners('manta.com', business_type, location)

        self.logger.info(f"Found {len(google_results)} Manta listings from Google search")

        # Extract URLs and scrape each listing
        for google_result in google_results:
            url = google_result['url']
            listing_results = self.scrape_listing(url, business_type, location)

            # Enhance results with Google snippet information
            for result in listing_results:
                result.google_title = google_result.get('title', '')
                result.google_snippet = google_result.get('snippet', '')
                result.search_pattern = f'site:manta.com "Owner" "{business_type}" "{location}"'

                # Add any extracted info from Google snippet
                if 'snippet_phone' in google_result and not result.phone:
                    result.phone = google_result['snippet_phone']
                if 'snippet_email' in google_result and not result.email:
                    result.email = google_result['snippet_email']
                if 'snippet_owner' in google_result and not result.owner_name:
                    result.owner_name = google_result['snippet_owner']
                if 'snippet_address' in google_result and not result.address:
                    result.address = google_result['snippet_address']
                if 'snippet_years_in_business' in google_result:
                    result.years_in_business = google_result['snippet_years_in_business']
                if 'snippet_rating' in google_result:
                    result.google_rating = google_result['snippet_rating']

            results.extend(listing_results)

        return results
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build Manta search URL."""
        # Manta search format: https://www.manta.com/search?search=lawn+care&city=dallas&state=tx
        encoded_query = quote_plus(f"{business_type} owner")
        
        # Parse location to extract city and state
        location_parts = location.lower().split()
        city = location_parts[0] if location_parts else location
        state = location_parts[-1] if len(location_parts) > 1 else ""
        
        return f"{self.base_url}/search?search={encoded_query}&city={city}&state={state}"
    
    def _build_page_url(self, base_url: str, page: int) -> str:
        """Build URL for specific page."""
        if page == 1:
            return base_url
        return f"{base_url}&pg={page}"
    
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Extract business listing URLs from Manta search results."""
        urls = []
        
        # Manta listing selectors
        selectors = [
            'a[href*="/c/"]',  # Manta company profile URLs
            '.search-result h3 a',
            '.company-name a',
            '.business-listing a[href*="/c/"]',
            'h2 a[href*="/c/"]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        href = urljoin(self.base_url, href)
                    
                    # Filter for company profile URLs
                    if '/c/' in href and 'manta.com' in href:
                        urls.append(href)
        
        return urls
    
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Extract comprehensive owner and business information from Manta business page."""
        results = []

        # Extract all business information
        business_info = self._extract_comprehensive_business_info(soup)
        owner_names = self._extract_owner_names(soup)

        if owner_names:
            for owner_name in owner_names:
                result = ScrapingResult(
                    owner_name=self.clean_text(owner_name),
                    source=self.source_name,
                    url=url,
                    **business_info  # Unpack all the comprehensive business info
                )
                results.append(result)
        else:
            # Create result with business info even if no owner found
            result = ScrapingResult(
                source=self.source_name,
                url=url,
                **business_info  # Unpack all the comprehensive business info
            )
            results.append(result)

        return results
    
    def _extract_business_name(self, soup: BeautifulSoup) -> str:
        """Extract business name from Manta page."""
        selectors = [
            'h1.company-name',
            'h1[data-testid="company-name"]',
            '.company-header h1',
            'h1.business-name',
            '.page-title h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Look for JSON-LD structured data
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict) and 'name' in data:
                    return data['name']
            except:
                continue
        
        # Fallback to title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Remove Manta suffix
            title_text = re.sub(r'\s*\|\s*Manta.*$', '', title_text)
            return title_text.strip()
        
        return ""
    
    def _extract_owner_names(self, soup: BeautifulSoup) -> List[str]:
        """Extract owner names from Manta page."""
        owner_names = []
        
        # Get all text content
        page_text = soup.get_text()
        
        # Owner extraction patterns
        patterns = [
            r'Owner[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Principal[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'CEO[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'President[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Founder[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Managing Director[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Contact[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            owner_names.extend(matches)
        
        # Look for owner info in specific sections
        owner_sections = soup.select('.company-details, .contact-info, .business-info, .company-profile')
        for section in owner_sections:
            section_text = section.get_text()
            for pattern in patterns:
                matches = re.findall(pattern, section_text, re.IGNORECASE)
                owner_names.extend(matches)
        
        # Look for structured data in JSON-LD
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict):
                    # Look for founder or employee information
                    if 'founder' in data:
                        founder = data['founder']
                        if isinstance(founder, dict) and 'name' in founder:
                            owner_names.append(founder['name'])
                        elif isinstance(founder, str):
                            owner_names.append(founder)
                    
                    if 'employee' in data:
                        employees = data['employee']
                        if isinstance(employees, list):
                            for emp in employees:
                                if isinstance(emp, dict) and 'name' in emp:
                                    # Check if this is likely an owner/executive
                                    job_title = emp.get('jobTitle', '').lower()
                                    if any(title in job_title for title in ['owner', 'ceo', 'president', 'founder']):
                                        owner_names.append(emp['name'])
            except:
                continue
        
        # Look for contact person information
        contact_sections = soup.select('.contact-person, .key-contact, .primary-contact')
        for section in contact_sections:
            name_elem = section.select_one('.name, .contact-name, .person-name')
            if name_elem:
                owner_names.append(name_elem.get_text(strip=True))
        
        # Clean and deduplicate
        cleaned_names = []
        for name in owner_names:
            cleaned_name = self.clean_text(name)
            if cleaned_name and len(cleaned_name) > 2 and cleaned_name not in cleaned_names:
                # Basic validation - should be a proper name
                if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*$', cleaned_name):
                    cleaned_names.append(cleaned_name)
        
        return cleaned_names

    def _extract_comprehensive_business_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract comprehensive business information from Manta page."""
        info = {}

        # Basic business information
        info['business_name'] = self.clean_text(self._extract_business_name(soup))

        # Contact information
        contact_info = self.extract_contact_info(soup)
        info.update(contact_info)

        # Extract additional details specific to Manta
        info['website'] = self._extract_manta_website(soup)
        info['business_description'] = self._extract_manta_description(soup)
        info['business_category'] = self._extract_manta_category(soup)
        info['years_in_business'] = self._extract_manta_years_in_business(soup)
        info['employee_count'] = self._extract_manta_employee_count(soup)
        info['annual_revenue'] = self._extract_manta_revenue(soup)

        # Extract address components
        address_info = self._extract_manta_detailed_address(soup)
        info.update(address_info)

        # Extract social media and online presence
        social_media = self._extract_manta_social_media(soup)
        info.update(social_media)

        # Extract business structure and legal info
        info['business_structure'] = self._extract_manta_business_structure(soup)
        info['business_license'] = self._extract_manta_license(soup)

        # Extract executives
        info['executives'] = self._extract_manta_executives(soup)

        return info

    def _extract_manta_website(self, soup: BeautifulSoup) -> str:
        """Extract website from Manta page."""
        # Look for website in Manta-specific selectors
        website_selectors = [
            '.website a',
            '.company-website a',
            '[data-website]',
            'a[href*="http"]:not([href*="manta.com"])'
        ]

        for selector in website_selectors:
            elem = soup.select_one(selector)
            if elem:
                href = elem.get('href', '')
                if href and 'manta.com' not in href:
                    return href

        return None

    def _extract_manta_description(self, soup: BeautifulSoup) -> str:
        """Extract business description from Manta page."""
        desc_selectors = [
            '.company-description',
            '.business-description',
            '.about-company',
            '.company-summary',
            '[class*="description"]'
        ]

        for selector in desc_selectors:
            elem = soup.select_one(selector)
            if elem:
                desc = elem.get_text(strip=True)
                if len(desc) > 30:
                    return desc

        return None

    def _extract_manta_category(self, soup: BeautifulSoup) -> str:
        """Extract business category from Manta page."""
        # Look for category in Manta-specific elements
        category_selectors = [
            '.company-category',
            '.business-category',
            '.industry',
            '.category-name'
        ]

        for selector in category_selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text(strip=True)

        # Look in breadcrumbs
        breadcrumbs = soup.select('.breadcrumb a')
        if len(breadcrumbs) > 1:
            return breadcrumbs[-2].get_text(strip=True)

        return None

    def _extract_manta_years_in_business(self, soup: BeautifulSoup) -> str:
        """Extract years in business from Manta page."""
        text = soup.get_text()

        patterns = [
            r'(\d+)\s*years?\s*in\s*business',
            r'established\s*(?:in\s*)?(\d{4})',
            r'since\s*(\d{4})',
            r'founded\s*(?:in\s*)?(\d{4})'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                year_or_years = match.group(1)
                if len(year_or_years) == 4:  # It's a year
                    from datetime import datetime
                    current_year = datetime.now().year
                    years = current_year - int(year_or_years)
                    return str(years)
                else:  # It's already years
                    return year_or_years

        return None

    def _extract_manta_employee_count(self, soup: BeautifulSoup) -> str:
        """Extract employee count from Manta page."""
        # Look for employee info in Manta-specific elements
        employee_selectors = [
            '.employee-count',
            '.company-size',
            '.employees'
        ]

        for selector in employee_selectors:
            elem = soup.select_one(selector)
            if elem:
                text = elem.get_text()
                numbers = re.findall(r'\d+', text)
                if numbers:
                    return numbers[0]

        # Look in general text
        text = soup.get_text()
        patterns = [
            r'(\d+)\s*employees?',
            r'staff\s*of\s*(\d+)',
            r'(\d+)\s*(?:full-time|part-time)?\s*(?:staff|workers?)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def _extract_manta_revenue(self, soup: BeautifulSoup) -> str:
        """Extract annual revenue from Manta page."""
        text = soup.get_text()

        revenue_patterns = [
            r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:million|M)',
            r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:billion|B)',
            r'revenue:?\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)',
            r'annual\s*sales:?\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)'
        ]

        for pattern in revenue_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return f"${match.group(1)}"

        return None

    def _extract_manta_detailed_address(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract detailed address from Manta page."""
        address_info = {}

        # Look for Manta-specific address elements
        address_selectors = [
            '.company-address',
            '.business-address',
            '.address',
            '[itemtype*="PostalAddress"]'
        ]

        for selector in address_selectors:
            elem = soup.select_one(selector)
            if elem:
                # Try structured data first
                street = elem.select_one('[itemprop="streetAddress"]')
                city = elem.select_one('[itemprop="addressLocality"]')
                state = elem.select_one('[itemprop="addressRegion"]')
                zip_code = elem.select_one('[itemprop="postalCode"]')

                if street:
                    address_info['street_address'] = street.get_text(strip=True)
                if city:
                    address_info['city'] = city.get_text(strip=True)
                if state:
                    address_info['state'] = state.get_text(strip=True)
                if zip_code:
                    address_info['zip_code'] = zip_code.get_text(strip=True)

                # If no structured data, parse full address
                if not any([street, city, state, zip_code]):
                    full_address = elem.get_text(strip=True)
                    address_info['address'] = full_address

                    # Parse components
                    parsed = self._parse_manta_address(full_address)
                    address_info.update(parsed)

                break

        return address_info

    def _parse_manta_address(self, address: str) -> Dict[str, str]:
        """Parse address components from Manta address string."""
        components = {}

        # Basic parsing
        zip_pattern = r'\b\d{5}(?:-\d{4})?\b'
        state_pattern = r'\b[A-Z]{2}\b'

        zip_match = re.search(zip_pattern, address)
        if zip_match:
            components['zip_code'] = zip_match.group()
            address = address.replace(zip_match.group(), '').strip()

        state_match = re.search(state_pattern, address)
        if state_match:
            components['state'] = state_match.group()
            address = address.replace(state_match.group(), '').strip()

        parts = [part.strip() for part in address.split(',') if part.strip()]
        if len(parts) >= 2:
            components['street_address'] = parts[0]
            components['city'] = parts[1]
        elif len(parts) == 1:
            components['street_address'] = parts[0]

        return components

    def _extract_manta_social_media(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract social media links from Manta page."""
        social_links = {}

        social_patterns = {
            'linkedin_url': r'linkedin\.com/(?:company/|in/)[^/\s]+',
            'facebook_url': r'facebook\.com/[^/\s]+',
            'twitter_url': r'twitter\.com/[^/\s]+',
            'instagram_url': r'instagram\.com/[^/\s]+',
            'youtube_url': r'youtube\.com/(?:channel/|user/|c/)[^/\s]+'
        }

        for link in soup.find_all('a', href=True):
            href = link.get('href')
            for platform, pattern in social_patterns.items():
                if re.search(pattern, href, re.IGNORECASE):
                    social_links[platform] = href
                    break

        return social_links

    def _extract_manta_business_structure(self, soup: BeautifulSoup) -> str:
        """Extract business structure from Manta page."""
        text = soup.get_text()

        structure_patterns = [
            r'\b(LLC|L\.L\.C\.)\b',
            r'\b(Corporation|Corp\.?)\b',
            r'\b(Inc\.?|Incorporated)\b',
            r'\b(Partnership|LLP)\b',
            r'\b(Sole Proprietorship)\b'
        ]

        for pattern in structure_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def _extract_manta_license(self, soup: BeautifulSoup) -> str:
        """Extract business license from Manta page."""
        text = soup.get_text()

        license_patterns = [
            r'License\s*#?:?\s*([A-Z0-9-]+)',
            r'Licensed\s*#?:?\s*([A-Z0-9-]+)',
            r'Permit\s*#?:?\s*([A-Z0-9-]+)'
        ]

        for pattern in license_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def _extract_manta_executives(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extract executives from Manta page."""
        executives = []

        # Look for management sections
        mgmt_selectors = [
            '.management',
            '.executives',
            '.key-personnel',
            '.company-officers'
        ]

        for selector in mgmt_selectors:
            section = soup.select_one(selector)
            if section:
                text = section.get_text()

                exec_patterns = [
                    r'([A-Z][a-z]+\s+[A-Z][a-z]+),?\s*(CEO|President|Owner|Manager|Director)',
                    r'(CEO|President|Owner|Manager|Director):?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)'
                ]

                for pattern in exec_patterns:
                    matches = re.findall(pattern, text, re.IGNORECASE)
                    for match in matches:
                        if len(match) == 2:
                            name, title = match if match[0].replace(' ', '').isalpha() else (match[1], match[0])
                            executives.append({
                                'name': name.strip(),
                                'title': title.strip()
                            })

        return executives[:5]
