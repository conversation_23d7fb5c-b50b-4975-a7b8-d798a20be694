#!/usr/bin/env python3
"""
Manta Browser Scraper - Uses Playwright to make API calls within browser context.
This bypasses Cloudflare protection for Manta's internal API endpoints.
"""

import asyncio
import logging
import subprocess
import json
import os
import time
import requests
from typing import List, Dict, Any
from datetime import datetime
from playwright.async_api import async_playwright

from src.scrapers.base_scraper import BaseScraper
from src.core import ScrapingResult

logger = logging.getLogger(__name__)

class MantaBrowserScraper(BaseScraper):
    """Manta scraper using browser-based API calls to bypass anti-bot protection."""
    
    def __init__(self, engine):
        super().__init__(engine, "manta_browser")
        self.base_url = "https://www.manta.com"
        self.fresh_cookies = None
        self.cookie_refresh_count = 0
        self.max_cookie_refreshes = 2  # Maximum times to refresh cookies per search

        # ScraperAPI configuration
        self.scraper_api_key = "89a467f0dd31dee55c2aaf9fa1fc0645"
        self.scraper_api_url = "https://api.scraperapi.com/"
        self.use_scraper_api = True  # Enable ScraperAPI by default

        # Business type to category ID mapping (from your curl example)
        self.category_mappings = {
            'restaurant': '54_C4_000',
            'construction': '54_B1_000', 
            'lawn care': '54_B3_KCQ',
            'plumbing': '54_B1_KCQ',
            'electrical': '54_B1_KCQ',
            'automotive': '54_A1_000',
            'dental': '54_D0_KCQ',
            'medical': '54_D0_000',
            'legal': '54_A6_KCQ',
            'accounting': '54_A6_000',
            'real estate': '54_A6_KCQ',
            'insurance': '54_A6_000',
            'dolls_and_stuffed_toys': '54_B83AE_KCQ',  # From your example
            'retail': '54_C0_000',
            'manufacturing': '54_B0_000',
            'services': '54_A0_000'
        }
        
        logger.info("Manta Browser scraper initialized")

    def _refresh_manta_cookies(self) -> bool:
        """
        Automatically refresh Manta cookies using the get_fresh_cookies.py script.
        Returns True if successful, False otherwise.
        """
        try:
            logger.info("🔄 Refreshing Manta cookies automatically...")

            # Run the cookie extraction script
            script_path = os.path.join(os.getcwd(), "get_fresh_cookies.py")
            if not os.path.exists(script_path):
                logger.error("❌ get_fresh_cookies.py script not found")
                return False

            # Run the script with automated mode (no user interaction)
            result = subprocess.run([
                "python3", script_path, "manta", "--automated"
            ], capture_output=True, text=True, timeout=120)

            if result.returncode == 0:
                # Load the fresh cookies
                sessions_dir = os.path.join(os.getcwd(), "sessions")
                cookie_files = [f for f in os.listdir(sessions_dir) if f.startswith("manta_cookies_") and f.endswith(".json")]

                if cookie_files:
                    # Get the most recent cookie file
                    latest_cookie_file = max(cookie_files, key=lambda x: os.path.getctime(os.path.join(sessions_dir, x)))
                    cookie_path = os.path.join(sessions_dir, latest_cookie_file)

                    with open(cookie_path, 'r') as f:
                        self.fresh_cookies = json.load(f)

                    logger.info(f"✅ Fresh Manta cookies loaded from {latest_cookie_file}")
                    return True
                else:
                    logger.error("❌ No cookie files found after extraction")
                    return False
            else:
                logger.error(f"❌ Cookie extraction failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ Cookie extraction timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Cookie refresh failed: {e}")
            return False

    def _get_cookie_string(self) -> str:
        """Convert fresh cookies to cookie string format for requests."""
        if not self.fresh_cookies:
            return ""

        cookie_parts = []

        # Handle both dictionary format and list format
        if isinstance(self.fresh_cookies, dict):
            # Dictionary format: {name: value}
            for name, value in self.fresh_cookies.items():
                if name and value:
                    cookie_parts.append(f"{name}={value}")
        elif isinstance(self.fresh_cookies, list):
            # List format: [{'name': 'x', 'value': 'y'}, ...]
            for cookie in self.fresh_cookies:
                if isinstance(cookie, dict) and cookie.get('name') and cookie.get('value'):
                    cookie_parts.append(f"{cookie['name']}={cookie['value']}")

        return "; ".join(cookie_parts)

    def _is_cloudflare_challenge(self, response_text: str) -> bool:
        """Check if response contains Cloudflare challenge page."""
        challenge_indicators = [
            "Just a moment...",
            "Cloudflare",
            "cf_chl_opt",
            "challenge-platform",
            "__cf_chl_tk"
        ]
        return any(indicator in response_text for indicator in challenge_indicators)

    def is_enabled(self) -> bool:
        """Check if Manta browser scraper is enabled."""
        return self.engine.config.get('sources', {}).get('manta_browser', {}).get('enabled', True)
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """
        Search for businesses using Manta's internal API via browser.
        
        Args:
            business_type: Type of business to search for
            location: Location to search in
            
        Returns:
            List of ScrapingResult objects
        """
        if not self.is_enabled():
            logger.info("Manta browser scraper is disabled")
            return []
        
        logger.info(f"Searching Manta browser API for {business_type} in {location}")
        
        try:
            # Get category ID for business type
            category_id = self._get_category_id(business_type)
            
            # Parse location for URL formatting
            location_slug = self._format_location_for_url(location)
            business_slug = self._format_business_type_for_url(business_type)
            
            # Search multiple pages
            max_pages = self.engine.config.get('sources', {}).get('manta_browser', {}).get('max_pages', 3)
            all_results = []
            
            for page in range(1, max_pages + 1):
                try:
                    logger.info(f"Searching Manta page {page} for {business_type} in {location}")

                    # Try the API call with retry logic for cookie refresh
                    api_data = self._search_with_cookie_retry(category_id, business_slug, location_slug, page)

                    if api_data:
                        # Convert API data to ScrapingResult objects
                        results = self._convert_api_data_to_results(api_data, business_type, location)
                        all_results.extend(results)

                        logger.info(f"Found {len(results)} results on page {page}")

                        # If no results on this page, stop searching
                        if not results:
                            break
                    else:
                        logger.info(f"No results found on page {page}")
                        break

                except Exception as e:
                        logger.error(f"Error searching page {page}: {e}")
                        continue
            
            logger.info(f"Manta browser API returned {len(all_results)} total results")
            return all_results

        except Exception as e:
            logger.error(f"Manta browser search failed: {e}")
            return []

    def _search_with_cookie_retry(self, category_id: str, business_slug: str, location_slug: str, page: int):
        """
        Search Manta API with intelligent retry logic:
        1. First try ScraperAPI (most effective for IP blocking)
        2. If that fails, try cookie refresh + direct request
        3. Finally fall back to browser automation
        """
        logger.info(f"🎯 Starting intelligent Manta API search with multiple fallback methods...")

        # Method 1: Try ScraperAPI first (most effective for bypassing IP blocks)
        if self.use_scraper_api:
            logger.info("🌐 Method 1: Trying ScraperAPI to bypass IP blocking...")
            try:
                api_data = self._search_with_scraper_api(category_id, business_slug, location_slug, page)

                if api_data and not self._is_cloudflare_challenge(api_data):
                    logger.info("✅ ScraperAPI method successful!")
                    return api_data
                else:
                    logger.warning("⚠️  ScraperAPI got blocked or returned challenge page")
            except Exception as e:
                logger.error(f"❌ ScraperAPI method failed: {e}")

        # Method 2: Try cookie refresh + direct request
        logger.info("🍪 Method 2: Trying cookie refresh + direct request...")
        max_cookie_attempts = 2

        for attempt in range(max_cookie_attempts):
            try:
                # Use asyncio to run the browser search with cookies
                api_data = asyncio.run(self._search_browser_api_with_cookies(category_id, business_slug, location_slug, page))

                # Check if we got a Cloudflare challenge
                if api_data and isinstance(api_data, str) and self._is_cloudflare_challenge(api_data):
                    logger.warning(f"🛡️  Cloudflare challenge detected on cookie attempt {attempt + 1}")

                    if attempt < max_cookie_attempts - 1 and self.cookie_refresh_count < self.max_cookie_refreshes:
                        logger.info("🔄 Attempting cookie refresh and retry...")

                        # Refresh cookies
                        if self._refresh_manta_cookies():
                            self.cookie_refresh_count += 1
                            logger.info(f"✅ Cookies refreshed, retrying... (refresh count: {self.cookie_refresh_count})")
                            continue
                        else:
                            logger.error("❌ Cookie refresh failed")
                            break
                    else:
                        logger.warning("❌ Max cookie refreshes reached")
                        break
                else:
                    # Success with cookies
                    if api_data:
                        logger.info("✅ Cookie method successful!")
                        return api_data

            except Exception as e:
                logger.error(f"Cookie attempt {attempt + 1} failed: {e}")

                if attempt < max_cookie_attempts - 1:
                    logger.info("🔄 Retrying with fresh cookies...")
                    if self._refresh_manta_cookies():
                        self.cookie_refresh_count += 1
                    else:
                        break

        # Method 3: Fall back to full browser automation
        logger.info("🌐 Method 3: Falling back to full browser automation...")
        try:
            api_data = asyncio.run(self._search_browser_api_fallback(category_id, business_slug, location_slug, page))

            if api_data and not self._is_cloudflare_challenge(api_data):
                logger.info("✅ Browser automation method successful!")
                return api_data
            else:
                logger.warning("⚠️  Browser automation also got blocked")

        except Exception as e:
            logger.error(f"❌ Browser automation failed: {e}")

        # All methods failed
        logger.error("❌ All methods failed - Manta protection is very aggressive")
        return None

    def _search_with_scraper_api(self, category_id: str, business_slug: str, location_slug: str, page: int):
        """
        Search Manta API using ScraperAPI to bypass IP blocking.
        This is the most effective method for bypassing Cloudflare protection.
        """
        try:
            logger.info(f"🌐 Using ScraperAPI to bypass IP blocking for Manta...")

            # Build the target Manta URL using working patterns from ScraperAPI test
            # Pattern 1: Try the mb_ format that worked in tests
            target_url = f"{self.base_url}/mb_{category_id}/{business_slug}/{location_slug}"

            # If page > 1, add page parameter
            if page > 1:
                target_url += f"?pg={page}"

            logger.debug(f"Target Manta URL: {target_url}")

            # ScraperAPI payload - using working configuration from tests
            scraper_payload = {
                'api_key': self.scraper_api_key,
                'url': target_url,
                'render': 'false',  # Basic rendering worked in tests
                'country_code': 'US'  # Use US IP addresses
                # Note: Removed 'premium': 'true' as it caused 500 errors in tests
            }

            # Make request through ScraperAPI
            logger.debug(f"Making ScraperAPI request...")
            response = requests.get(self.scraper_api_url, params=scraper_payload, timeout=60)

            if response.status_code == 200:
                logger.info(f"✅ ScraperAPI request successful! Response length: {len(response.text)}")

                # Save response for debugging
                try:
                    import datetime
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_filename = f"scraperapi_manta_response_{timestamp}.html"
                    with open(debug_filename, 'w') as f:
                        f.write(response.text)
                    logger.debug(f"ScraperAPI response saved to: {debug_filename}")
                except:
                    pass

                # Check if we got actual data or Cloudflare challenge
                if self._is_cloudflare_challenge(response.text):
                    logger.warning("🛡️  ScraperAPI still got Cloudflare challenge - trying with different parameters")

                    # Try with different ScraperAPI parameters
                    scraper_payload.update({
                        'render': 'true',  # Enable JavaScript rendering
                        'wait_for': '2000',  # Wait 2 seconds for page load
                        'autoparse': 'false'
                    })

                    response = requests.get(self.scraper_api_url, params=scraper_payload, timeout=90)

                    if response.status_code == 200 and not self._is_cloudflare_challenge(response.text):
                        logger.info(f"✅ ScraperAPI with JS rendering successful!")
                        return response.text
                    else:
                        logger.warning(f"⚠️  ScraperAPI with JS rendering still blocked")
                        return response.text  # Return anyway for analysis
                else:
                    return response.text

            else:
                logger.error(f"❌ ScraperAPI request failed with status: {response.status_code}")
                logger.error(f"Response: {response.text[:200]}...")
                return None

        except Exception as e:
            logger.error(f"❌ ScraperAPI request failed: {e}")
            return None

    async def _search_browser_api_with_cookies(self, category_id: str, business_slug: str, location_slug: str, page: int):
        """Search Manta API using browser context with fresh cookies."""

        # If we don't have fresh cookies, try to get them
        if not self.fresh_cookies:
            logger.info("🍪 No fresh cookies available, attempting to get them...")
            if not self._refresh_manta_cookies():
                logger.error("❌ Failed to get fresh cookies, falling back to browser method")
                return await self._search_browser_api_fallback(category_id, business_slug, location_slug, page)

        # Use direct HTTP request with fresh cookies (faster than browser)
        try:

            # Build API URL
            api_url = f"{self.base_url}/more-results/{category_id}/{business_slug}"
            params = {'pg': page}

            # Get cookie string
            cookie_string = self._get_cookie_string()

            headers = {
                'Accept': '*/*',
                'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Cookie': cookie_string,
                'Pragma': 'no-cache',
                'Referer': f'https://www.manta.com/mb_{category_id}/{business_slug}/dallas_tx',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?1',
                'sec-ch-ua-platform': '"Android"'
            }

            logger.debug(f"Making request to: {api_url} with params: {params}")

            response = requests.get(api_url, params=params, headers=headers, timeout=30)

            if response.status_code == 200:
                logger.info(f"✅ Manta API call successful with cookies (status: {response.status_code})")
                return response.text
            else:
                logger.warning(f"⚠️  Manta API call failed with status: {response.status_code}")
                return response.text  # Return anyway to check for Cloudflare challenge

        except Exception as e:
            logger.error(f"❌ Cookie-based request failed: {e}")
            # Fallback to browser method
            return await self._search_browser_api_fallback(category_id, business_slug, location_slug, page)

    async def _search_browser_api_fallback(self, category_id: str, business_slug: str, location_slug: str, page: int):
        """Search Manta API using browser context as fallback when cookies fail."""
        
        async with async_playwright() as p:
            # Launch browser (headless for production)
            browser = await p.chromium.launch(
                headless=True,  # Set to False for debugging
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage'
                ]
            )
            
            # Create realistic browser context
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            # Add stealth script
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            page_obj = await context.new_page()
            
            try:
                # Navigate to Manta to establish session
                await page_obj.goto("https://www.manta.com", wait_until="networkidle")
                await page_obj.wait_for_timeout(3000)
                
                # Handle any modals/popups
                try:
                    # Look for cookie consent or other popups
                    accept_button = await page_obj.query_selector('button:has-text("Accept"), button:has-text("OK"), .cookie-accept')
                    if accept_button:
                        await accept_button.click()
                        await page_obj.wait_for_timeout(2000)
                except:
                    pass
                
                # Build API URL using the pattern from your curl command
                api_url = f"{self.base_url}/more-results/{category_id}/{business_slug}"
                if location_slug:
                    # Some endpoints use location in URL, some use query params
                    api_url = f"{self.base_url}/mb_{category_id}/{business_slug}/{location_slug}"
                
                params = {'pg': page}
                
                # Convert params to URL string
                param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
                full_url = f"{api_url}?{param_string}"
                
                logger.debug(f"Manta API URL: {full_url}")
                
                # Make the API call using browser's fetch API
                api_response = await page_obj.evaluate(f"""
                    async () => {{
                        try {{
                            const response = await fetch('{full_url}', {{
                                method: 'GET',
                                headers: {{
                                    'Accept': '*/*',
                                    'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                                    'Cache-Control': 'no-cache',
                                    'Connection': 'keep-alive',
                                    'Pragma': 'no-cache',
                                    'Referer': 'https://www.manta.com/',
                                    'Sec-Fetch-Dest': 'empty',
                                    'Sec-Fetch-Mode': 'cors',
                                    'Sec-Fetch-Site': 'same-origin'
                                }}
                            }});
                            
                            if (response.ok) {{
                                const text = await response.text();
                                return {{ success: true, data: text, status: response.status }};
                            }} else {{
                                const text = await response.text();
                                return {{ success: false, error: text, status: response.status }};
                            }}
                        }} catch (error) {{
                            return {{ success: false, error: error.message, status: 0 }};
                        }}
                    }}
                """)
                
                await browser.close()
                
                if api_response['success']:
                    return api_response['data']
                else:
                    logger.warning(f"Manta API call failed: Status {api_response['status']}")
                    return None
                    
            except Exception as e:
                logger.error(f"Manta browser API call failed: {e}")
                await browser.close()
                return None
    
    def _convert_api_data_to_results(self, html_data: str, business_type: str, location: str) -> List[ScrapingResult]:
        """Convert Manta API HTML response to ScrapingResult objects."""
        results = []
        
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_data, 'html.parser')
            
            # Look for business listings using Manta's specific pattern
            # Based on HTML analysis: data-test="mb-result-card-*"
            businesses = soup.select('[data-test^="mb-result-card-"]')
            logger.debug(f"Found {len(businesses)} businesses with Manta-specific selector")

            if not businesses:
                # Fallback selectors if the main pattern doesn't work
                business_selectors = [
                    '.business-card',
                    '.listing-item',
                    '.company-listing',
                    '[data-business-id]',
                    '.search-result',
                    '.result-item',
                    '.business-result'
                ]

                for selector in business_selectors:
                    businesses = soup.select(selector)
                    if businesses:
                        logger.debug(f"Found {len(businesses)} businesses with fallback selector: {selector}")
                        break

                if not businesses:
                    # Try more generic selectors
                    businesses = soup.select('div[class*="business"], div[class*="company"], div[class*="listing"]')
                    logger.debug(f"Found {len(businesses)} businesses with generic selectors")
            
            for business in businesses:
                try:
                    result = self._extract_business_data(business, business_type, location)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.debug(f"Error extracting business data: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error parsing Manta API response: {e}")
        
        return results
    
    def _extract_business_data(self, business_element, business_type: str, location: str) -> ScrapingResult:
        """Extract comprehensive business data from a Manta business listing element."""
        try:
            # Extract business name using Manta's specific structure
            # Pattern: <a data-test="mb-result-card-title-*" href="/c/*/business-name" class="cursor-pointer font-serif text-gray-900 mr-2">Business Name</a>
            business_name = self._extract_text(business_element, [
                '[data-test^="mb-result-card-title-"]',  # Manta-specific pattern
                '.font-serif.text-gray-900',  # Manta's business name styling
                '.business-name', '.company-name', '.listing-title', 'h2', 'h3', 'h4',
                '[class*="name"]', '.title'
            ])

            # Extract owner/contact name (Manta doesn't typically show owner names in listings)
            owner_name = self._extract_text(business_element, [
                '.owner-name', '.contact-name', '.manager-name', '.principal-name',
                '[class*="owner"]', '[class*="contact"]'
            ])
            
            # Extract phone (Manta shows phone numbers in specific patterns)
            phone = self._extract_text(business_element, [
                '[href^="tel:"]',  # Phone links
                '.phone', '.tel', '.contact-phone',
                '[class*="phone"]'
            ])

            # Extract email (less common in Manta listings)
            email = self._extract_text(business_element, [
                '[href^="mailto:"]',  # Email links
                '.email', '.contact-email',
                '[class*="email"]'
            ])

            # Extract website (Manta may have website links)
            website = self._extract_link(business_element, [
                '.website', '.url', '.company-website',
                'a[href*="http"]:not([href*="manta.com"])'  # External links only
            ])

            # Extract address using Manta's location structure
            # Manta shows addresses near map pin icons
            address = self._extract_text(business_element, [
                '.ml-4',  # Text next to map pin icon in Manta
                '.address', '.location', '.business-address', '.street-address',
                '[class*="address"]', '[class*="location"]'
            ])
            
            # Parse address components
            street_address, city, state, zip_code = self._parse_address(address)
            
            # Extract business URL using Manta's specific pattern
            # Pattern: <a data-test="mb-result-card-title-*" href="/c/*/business-name">
            business_url = self._extract_link(business_element, [
                '[data-test^="mb-result-card-title-"]',  # Manta-specific business link
                'a[href*="/c/"]',  # Manta business profile URLs
                '.business-link', '.company-link', '.profile-link'
            ])

            if business_url and not business_url.startswith('http'):
                business_url = f"https://www.manta.com{business_url}"
            
            # Extract business description
            business_description = self._extract_text(business_element, [
                '.description', '.business-description', '.company-description',
                '.summary', '[class*="description"]'
            ])
            
            # Extract years in business
            years_in_business = self._extract_years_in_business(business_element)
            
            # Extract employee count
            employee_count = self._extract_employee_count(business_element)
            
            # Create ScrapingResult
            result = ScrapingResult(
                owner_name=owner_name,
                business_name=business_name,
                business_type=business_type,
                location=location,
                source=self.source_name,
                url=business_url,
                phone=phone,
                email=email,
                website=website,
                address=address,
                street_address=street_address,
                city=city,
                state=state,
                zip_code=zip_code,
                business_description=business_description,
                years_in_business=years_in_business,
                employee_count=employee_count,
                scraped_at=datetime.now(),
                confidence_score=0.8,  # High confidence for API data
                data_quality='high',
                verification_status='api_verified'
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting business data: {e}")
            return None
    
    def _get_category_id(self, business_type: str) -> str:
        """Get Manta category ID for business type."""
        business_lower = business_type.lower()
        
        # Try exact match first
        if business_lower in self.category_mappings:
            return self.category_mappings[business_lower]
        
        # Try partial matches
        for key, category_id in self.category_mappings.items():
            if key in business_lower or business_lower in key:
                return category_id
        
        # Default to general services
        return '54_A0_000'
    
    def _format_location_for_url(self, location: str) -> str:
        """Format location for URL slug."""
        return location.lower().replace(' ', '_').replace(',', '').replace('.', '')
    
    def _format_business_type_for_url(self, business_type: str) -> str:
        """Format business type for URL slug."""
        return business_type.lower().replace(' ', '_').replace('&', 'and')
    
    def _extract_text(self, element, selectors: List[str]) -> str:
        """Extract text using multiple selector options."""
        for selector in selectors:
            found = element.select_one(selector)
            if found:
                text = found.get_text(strip=True)
                if text:
                    return text
        return ""
    
    def _extract_link(self, element, selectors: List[str]) -> str:
        """Extract link URL using multiple selector options."""
        for selector in selectors:
            found = element.select_one(selector)
            if found:
                href = found.get('href')
                if href:
                    return href
        return ""
    
    def _parse_address(self, address: str) -> tuple:
        """Parse address into components."""
        if not address:
            return "", "", "", ""
        
        try:
            # Simple address parsing
            parts = address.split(',')
            if len(parts) >= 3:
                street = parts[0].strip()
                city = parts[1].strip()
                state_zip = parts[2].strip().split()
                state = state_zip[0] if state_zip else ""
                zip_code = state_zip[1] if len(state_zip) > 1 else ""
                return street, city, state, zip_code
            else:
                return address, "", "", ""
        except:
            return address, "", "", ""
    
    def _extract_years_in_business(self, element) -> str:
        """Extract years in business from element."""
        import re
        text = element.get_text()
        
        patterns = [
            r'(\d+)\s*years?\s*in\s*business',
            r'established\s*(?:in\s*)?(\d{4})',
            r'since\s*(\d{4})',
            r'founded\s*(?:in\s*)?(\d{4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        return ""
    
    def _extract_employee_count(self, element) -> str:
        """Extract employee count from element."""
        import re
        text = element.get_text()
        
        patterns = [
            r'(\d+)\s*employees?',
            r'staff\s*of\s*(\d+)',
            r'(\d+)\s*(?:full-time|part-time)?\s*(?:staff|workers?)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)
        return ""
    
    def _extract_listing_urls(self, soup, business_type: str, location: str) -> List[str]:
        """Required by BaseScraper but not used in browser API mode."""
        return []
    
    def extract_owner_info(self, soup, url: str) -> List[ScrapingResult]:
        """Required by BaseScraper but not used in browser API mode."""
        return []
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get scraper health status."""
        try:
            return {
                'scraper': 'manta_browser',
                'status': 'healthy',
                'enabled': self.is_enabled(),
                'method': 'browser_api',
                'playwright_available': True
            }
        except Exception as e:
            return {
                'scraper': 'manta_browser',
                'status': 'unhealthy',
                'error': str(e),
                'enabled': self.is_enabled()
            }
