"""
Base scraper class for all data source scrapers.
"""

import re
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
import time
import random

from bs4 import BeautifulSoup
import requests

from ..core import ScrapingResult, ScrapingEngine
from ..utils.anti_bot import AntiBot


class BaseScraper(ABC):
    """Abstract base class for all scrapers."""
    
    def __init__(self, engine: ScrapingEngine, source_name: str):
        self.engine = engine
        self.source_name = source_name
        self.config = engine.config.get('sources', {}).get(source_name, {})
        self.logger = logging.getLogger(f"{__name__}.{source_name}")
        self.anti_bot = AntiBot(engine.config)
        
        # Source-specific configuration
        self.base_url = self.config.get('base_url', '')
        self.max_pages = self.config.get('max_pages', 5)
        self.enabled = self.config.get('enabled', True)
        self.cloudflare_protected = self.config.get('cloudflare_protected', False)
    
    @abstractmethod
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search for business owners by business type and location."""
        pass
    
    @abstractmethod
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Extract owner information from a parsed HTML page."""
        pass
    
    def is_enabled(self) -> bool:
        """Check if this scraper is enabled."""
        return self.enabled
    
    def make_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """Make a request using the appropriate method based on protection level."""
        if self.cloudflare_protected:
            # Use CloudScraper for Cloudflare-protected sites
            response = self.anti_bot.bypass_cloudflare(url)
            if not response:
                # Fallback to Selenium if CloudScraper fails
                html_content = self.anti_bot.selenium_cloudflare_bypass(url)
                if html_content:
                    # Create mock response
                    class MockResponse:
                        def __init__(self, content, url):
                            self.content = content.encode('utf-8')
                            self.text = content
                            self.url = url
                            self.status_code = 200
                        def raise_for_status(self):
                            pass
                    return MockResponse(html_content, url)
            return response
        else:
            # Use regular engine request
            return self.engine.make_request(url, **kwargs)
    
    def extract_text_by_patterns(self, text: str, patterns: List[str]) -> List[str]:
        """Extract text using regex patterns."""
        results = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            results.extend(matches)
        return results
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove common prefixes/suffixes
        text = re.sub(r'^(Owner:|CEO:|President:|Founder:)\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\s*(Inc\.|LLC|Corp\.|Corporation|Company)$', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def extract_contact_info(self, soup: BeautifulSoup) -> Dict[str, Optional[str]]:
        """Extract contact information from soup."""
        contact_info = {
            'phone': None,
            'email': None,
            'address': None
        }
        
        # Phone patterns
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',
            r'\b\d{3}\s\d{3}\s\d{4}\b'
        ]
        
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        text = soup.get_text()
        
        # Extract phone
        for pattern in phone_patterns:
            phone_match = re.search(pattern, text)
            if phone_match:
                contact_info['phone'] = phone_match.group()
                break
        
        # Extract email
        email_match = re.search(email_pattern, text)
        if email_match:
            contact_info['email'] = email_match.group()
        
        # Extract address (basic implementation)
        address_selectors = [
            '[class*="address"]',
            '[class*="location"]',
            '[class*="contact"]'
        ]
        
        for selector in address_selectors:
            address_elem = soup.select_one(selector)
            if address_elem:
                address_text = address_elem.get_text(strip=True)
                if len(address_text) > 10:  # Basic validation
                    contact_info['address'] = address_text
                    break
        
        return contact_info
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build search URL for the specific source."""
        # This should be implemented by each scraper
        return self.base_url
    
    def get_search_results_urls(self, search_url: str) -> List[str]:
        """Get URLs of individual business listings from search results."""
        urls = []
        
        for page in range(1, self.max_pages + 1):
            try:
                page_url = self._build_page_url(search_url, page)
                response = self.make_request(page_url)
                
                if not response:
                    continue
                
                soup = BeautifulSoup(response.text, 'lxml')
                page_urls = self._extract_listing_urls(soup)
                urls.extend(page_urls)
                
                # Random delay between pages
                time.sleep(random.uniform(1, 3))
                
                # Break if no more results
                if not page_urls:
                    break
                    
            except Exception as e:
                self.logger.error(f"Error getting page {page} results: {e}")
                continue
        
        return list(set(urls))  # Remove duplicates
    
    def _build_page_url(self, base_url: str, page: int) -> str:
        """Build URL for a specific page number."""
        # Default implementation - should be overridden by specific scrapers
        if page == 1:
            return base_url
        return f"{base_url}&page={page}"
    
    @abstractmethod
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Extract individual listing URLs from search results page."""
        pass
    
    def scrape_listing(self, url: str, business_type: str, location: str) -> List[ScrapingResult]:
        """Scrape a single business listing."""
        try:
            response = self.make_request(url)
            if not response:
                return []
            
            soup = BeautifulSoup(response.text, 'lxml')
            results = self.extract_owner_info(soup, url)
            
            # Add common fields
            for result in results:
                result.business_type = business_type
                result.location = location
                result.source = self.source_name
                result.url = url
                
                # Extract contact info if not already present
                if not any([result.phone, result.email, result.address]):
                    contact_info = self.extract_contact_info(soup)
                    result.phone = result.phone or contact_info['phone']
                    result.email = result.email or contact_info['email']
                    result.address = result.address or contact_info['address']
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error scraping listing {url}: {e}")
            return []
