"""
TruePeopleSearch.com scraper for business owner information.
This site is protected by Cloudflare.
"""

import re
import json
from typing import List
from urllib.parse import urljoin, quote_plus
from datetime import datetime
import requests

from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..core import ScrapingResult


class TruePeopleSearchScraper(BaseScraper):
    """Scraper for TruePeopleSearch.com business owner information."""
    
    def __init__(self, engine):
        super().__init__(engine, 'truepeoplesearch')
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search TruePeopleSearch for business owners."""
        if not self.is_enabled():
            return []
        
        self.logger.info(f"Searching TruePeopleSearch for '{business_type}' owners in '{location}'")
        
        results = []
        
        # Since TruePeopleSearch is primarily a people search engine,
        # we'll search for business owners by name patterns
        search_queries = self._build_search_queries(business_type, location)
        
        for query in search_queries:
            search_url = self.build_search_url_for_query(query, location)
            listing_urls = self.get_search_results_urls(search_url)
            
            for url in listing_urls:
                listing_results = self.scrape_listing(url, business_type, location)
                results.extend(listing_results)
        
        return results

    def search_person(self, query) -> List[ScrapingResult]:
        """
        Search for a specific person using TruePeopleSearch.

        Args:
            query: PersonSearchQuery object with search parameters

        Returns:
            List of ScrapingResult objects with person information
        """
        if not self.is_enabled():
            self.logger.info("TruePeopleSearch scraper is disabled")
            return []

        self.logger.info(f"Searching TruePeopleSearch for person: {query.first_name} {query.last_name}")

        try:
            # Build search URL for person
            search_url = self._build_person_search_url(query.first_name, query.last_name, query.city, query.state)

            # Get search results
            response = self.make_request(search_url)
            if not response:
                return []

            soup = BeautifulSoup(response.text, 'lxml')

            # Extract person results from search page
            results = self._extract_person_results(soup, query)

            self.logger.info(f"Found {len(results)} person results for {query.first_name} {query.last_name}")
            return results

        except Exception as e:
            self.logger.error(f"TruePeopleSearch person search failed: {e}")
            return []

    def search_by_name(self, first_name: str, last_name: str, location: str = "") -> List[ScrapingResult]:
        """
        Search for a person by name and location.

        Args:
            first_name: Person's first name
            last_name: Person's last name
            location: Location string (city, state format)

        Returns:
            List of ScrapingResult objects
        """
        # Parse location
        city, state = self._parse_location(location) if location else ("", "")

        # Create a simple query object
        class SimpleQuery:
            def __init__(self, first_name, last_name, city, state):
                self.first_name = first_name
                self.last_name = last_name
                self.city = city
                self.state = state
                self.address = location

        query = SimpleQuery(first_name, last_name, city, state)
        return self.search_person(query)

    def _build_person_search_url(self, first_name: str, last_name: str, city: str = "", state: str = "") -> str:
        """Build search URL for person search."""
        base_url = "https://www.truepeoplesearch.com/results"

        # Build name query
        name_query = f"{first_name} {last_name}".strip()

        # Build location query
        location_parts = []
        if city:
            location_parts.append(city)
        if state:
            location_parts.append(state)
        location_query = " ".join(location_parts)

        # Construct URL with parameters
        if location_query:
            search_url = f"{base_url}?name={quote_plus(name_query)}&citystatezip={quote_plus(location_query)}"
        else:
            search_url = f"{base_url}?name={quote_plus(name_query)}"

        return search_url

    def _extract_person_results(self, soup: BeautifulSoup, query) -> List[ScrapingResult]:
        """Extract person results from TruePeopleSearch results page."""
        results = []

        # TruePeopleSearch typically shows results in cards or list items
        person_cards = soup.find_all(['div', 'li'], class_=re.compile(r'(person|result|card)', re.I))

        if not person_cards:
            # Try alternative selectors
            person_cards = soup.find_all('div', attrs={'data-person': True})

        if not person_cards:
            # Try finding any div with person-like content
            person_cards = soup.find_all('div', string=re.compile(r'\b(age|phone|address)\b', re.I))

        for card in person_cards[:10]:  # Limit to first 10 results
            try:
                result = self._extract_person_from_card(card, query)
                if result:
                    results.append(result)
            except Exception as e:
                self.logger.error(f"Error extracting person from card: {e}")
                continue

        return results

    def _extract_person_from_card(self, card, query) -> ScrapingResult:
        """Extract person information from a result card."""
        # Extract name
        name_elem = card.find(['h2', 'h3', 'h4', 'a'], class_=re.compile(r'name', re.I))
        if not name_elem:
            name_elem = card.find(['h2', 'h3', 'h4', 'a'])

        full_name = ""
        if name_elem:
            full_name = name_elem.get_text(strip=True)

        if not full_name:
            return None

        # Extract age
        age_text = ""
        age_elem = card.find(string=re.compile(r'age\s*:?\s*(\d+)', re.I))
        if age_elem:
            age_match = re.search(r'age\s*:?\s*(\d+)', age_elem, re.I)
            if age_match:
                age_text = age_match.group(1)

        # Extract address
        address = ""
        address_elem = card.find(['div', 'span'], class_=re.compile(r'address', re.I))
        if address_elem:
            address = address_elem.get_text(strip=True)

        # Extract phone
        phone = ""
        phone_elem = card.find(string=re.compile(r'\(\d{3}\)\s*\d{3}-\d{4}'))
        if phone_elem:
            phone_match = re.search(r'\((\d{3})\)\s*(\d{3})-(\d{4})', phone_elem)
            if phone_match:
                phone = f"({phone_match.group(1)}) {phone_match.group(2)}-{phone_match.group(3)}"

        # Parse address components
        city, state, zip_code = self._parse_address_components(address)

        # Create result
        result = ScrapingResult(
            owner_name=full_name,
            business_name="",  # Not applicable for person search
            business_type="",  # Not applicable for person search
            location=f"{city}, {state}".strip(", ") if city or state else "",
            source="truepeoplesearch",
            address=address,
            city=city,
            state=state,
            zip_code=zip_code,
            phone=phone,
            owner_age=age_text,
            scraped_at=datetime.now(),

            # Search context
            search_type='people',
            search_query=f"{query.first_name} {query.last_name}",
            match_confidence=0.7,  # Medium confidence for web scraping
            data_quality='medium',

            raw_data={
                'source': 'truepeoplesearch',
                'search_method': 'web_scraping',
                'card_html': str(card)
            }
        )

        return result

    def _parse_location(self, location: str) -> tuple:
        """Parse location string into city and state."""
        if not location:
            return "", ""

        try:
            # Try to split by comma first
            if ',' in location:
                parts = location.split(',')
                if len(parts) >= 2:
                    city = parts[0].strip()
                    state = parts[1].strip()
                    return city, state

            # Try to split by space and assume last part is state
            parts = location.split()
            if len(parts) >= 2:
                state = parts[-1]
                city = ' '.join(parts[:-1])
                return city, state

            return location, ""
        except:
            return location, ""

    def _parse_address_components(self, address: str) -> tuple:
        """Parse address into city, state, zip components."""
        if not address:
            return "", "", ""

        try:
            # Look for zip code pattern
            zip_match = re.search(r'\b(\d{5}(?:-\d{4})?)\b', address)
            zip_code = zip_match.group(1) if zip_match else ""

            # Remove zip code from address for further parsing
            address_no_zip = re.sub(r'\b\d{5}(?:-\d{4})?\b', '', address).strip()

            # Look for state pattern (2 letter abbreviation)
            state_match = re.search(r'\b([A-Z]{2})\b', address_no_zip)
            state = state_match.group(1) if state_match else ""

            # Remove state from address
            if state:
                address_no_state = address_no_zip.replace(state, '').strip()
            else:
                address_no_state = address_no_zip

            # What's left should be city (and possibly street)
            # Take the last part as city
            parts = address_no_state.split(',')
            city = parts[-1].strip() if parts else ""

            return city, state, zip_code
        except:
            return "", "", ""

    def _build_search_queries(self, business_type: str, location: str) -> List[str]:
        """Build search queries for business owners."""
        # Common owner/executive names patterns for the business type
        queries = [
            f"{business_type} owner",
            f"{business_type} CEO",
            f"{business_type} president",
            f"{business_type} founder"
        ]
        return queries
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build TruePeopleSearch search URL."""
        query = f"{business_type} owner"
        return self.build_search_url_for_query(query, location)
    
    def build_search_url_for_query(self, query: str, location: str) -> str:
        """Build search URL for a specific query."""
        # TruePeopleSearch URL format
        encoded_query = quote_plus(query)
        encoded_location = quote_plus(location)
        
        return f"{self.base_url}/results?name={encoded_query}&citystatezip={encoded_location}"
    
    def _build_page_url(self, base_url: str, page: int) -> str:
        """Build URL for specific page."""
        if page == 1:
            return base_url
        return f"{base_url}&page={page}"
    
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Extract person profile URLs from search results."""
        urls = []
        
        # TruePeopleSearch profile selectors
        selectors = [
            'a[href*="/view/"]',  # Profile URLs
            '.search-result a[href*="/view/"]',
            '.person-result a',
            'h3 a[href*="/view/"]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        href = urljoin(self.base_url, href)
                    
                    # Filter for profile URLs
                    if '/view/' in href and 'truepeoplesearch.com' in href:
                        urls.append(href)
        
        return urls
    
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Extract owner information from TruePeopleSearch profile page."""
        results = []
        
        # Extract person name
        person_name = self._extract_person_name(soup)
        
        # Extract business associations
        business_info = self._extract_business_associations(soup)
        
        if person_name and business_info:
            for business in business_info:
                result = ScrapingResult(
                    owner_name=self.clean_text(person_name),
                    business_name=self.clean_text(business.get('name', '')),
                    source=self.source_name,
                    url=url,
                    raw_data=business
                )
                results.append(result)
        elif person_name:
            # Create result with person info even if no business found
            result = ScrapingResult(
                owner_name=self.clean_text(person_name),
                source=self.source_name,
                url=url
            )
            results.append(result)
        
        return results
    
    def _extract_person_name(self, soup: BeautifulSoup) -> str:
        """Extract person name from profile page."""
        selectors = [
            'h1.person-name',
            'h1[data-testid="person-name"]',
            '.profile-header h1',
            'h1.name',
            '.person-header h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Fallback to title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Extract name from title
            name_match = re.match(r'^([A-Z][a-z]+\s+[A-Z][a-z]+)', title_text)
            if name_match:
                return name_match.group(1)
        
        return ""
    
    def _extract_business_associations(self, soup: BeautifulSoup) -> List[dict]:
        """Extract business associations from profile page."""
        businesses = []
        
        # Look for business/work information sections
        business_sections = soup.select('.work-info, .business-info, .employment, .associated-businesses')
        
        for section in business_sections:
            # Extract business names
            business_names = section.select('.company-name, .business-name, .employer')
            for name_elem in business_names:
                business_name = name_elem.get_text(strip=True)
                if business_name:
                    businesses.append({
                        'name': business_name,
                        'type': 'employment'
                    })
        
        # Look for business ownership indicators in text
        page_text = soup.get_text()
        
        # Patterns for business ownership
        ownership_patterns = [
            r'Owner of ([A-Z][A-Za-z0-9\s&,.-]+)',
            r'CEO of ([A-Z][A-Za-z0-9\s&,.-]+)',
            r'President of ([A-Z][A-Za-z0-9\s&,.-]+)',
            r'Founder of ([A-Z][A-Za-z0-9\s&,.-]+)'
        ]
        
        for pattern in ownership_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            for match in matches:
                businesses.append({
                    'name': match.strip(),
                    'type': 'ownership'
                })
        
        # Look for structured data
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict) and 'worksFor' in data:
                    works_for = data['worksFor']
                    if isinstance(works_for, dict) and 'name' in works_for:
                        businesses.append({
                            'name': works_for['name'],
                            'type': 'employment'
                        })
                    elif isinstance(works_for, list):
                        for org in works_for:
                            if isinstance(org, dict) and 'name' in org:
                                businesses.append({
                                    'name': org['name'],
                                    'type': 'employment'
                                })
            except:
                continue
        
        # Deduplicate businesses
        unique_businesses = []
        seen_names = set()
        
        for business in businesses:
            name = business['name'].lower()
            if name not in seen_names and len(name) > 2:
                seen_names.add(name)
                unique_businesses.append(business)
        
        return unique_businesses
