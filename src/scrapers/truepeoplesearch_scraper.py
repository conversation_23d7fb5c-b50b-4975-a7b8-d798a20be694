"""
TruePeopleSearch.com scraper for business owner information.
This site is protected by Cloudflare.
"""

import re
import json
from typing import List
from urllib.parse import urljoin, quote_plus
import requests

from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..core import ScrapingResult


class TruePeopleSearchScraper(BaseScraper):
    """Scraper for TruePeopleSearch.com business owner information."""
    
    def __init__(self, engine):
        super().__init__(engine, 'truepeoplesearch')
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search TruePeopleSearch for business owners."""
        if not self.is_enabled():
            return []
        
        self.logger.info(f"Searching TruePeopleSearch for '{business_type}' owners in '{location}'")
        
        results = []
        
        # Since TruePeopleSearch is primarily a people search engine,
        # we'll search for business owners by name patterns
        search_queries = self._build_search_queries(business_type, location)
        
        for query in search_queries:
            search_url = self.build_search_url_for_query(query, location)
            listing_urls = self.get_search_results_urls(search_url)
            
            for url in listing_urls:
                listing_results = self.scrape_listing(url, business_type, location)
                results.extend(listing_results)
        
        return results
    
    def _build_search_queries(self, business_type: str, location: str) -> List[str]:
        """Build search queries for business owners."""
        # Common owner/executive names patterns for the business type
        queries = [
            f"{business_type} owner",
            f"{business_type} CEO",
            f"{business_type} president",
            f"{business_type} founder"
        ]
        return queries
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build TruePeopleSearch search URL."""
        query = f"{business_type} owner"
        return self.build_search_url_for_query(query, location)
    
    def build_search_url_for_query(self, query: str, location: str) -> str:
        """Build search URL for a specific query."""
        # TruePeopleSearch URL format
        encoded_query = quote_plus(query)
        encoded_location = quote_plus(location)
        
        return f"{self.base_url}/results?name={encoded_query}&citystatezip={encoded_location}"
    
    def _build_page_url(self, base_url: str, page: int) -> str:
        """Build URL for specific page."""
        if page == 1:
            return base_url
        return f"{base_url}&page={page}"
    
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Extract person profile URLs from search results."""
        urls = []
        
        # TruePeopleSearch profile selectors
        selectors = [
            'a[href*="/view/"]',  # Profile URLs
            '.search-result a[href*="/view/"]',
            '.person-result a',
            'h3 a[href*="/view/"]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        href = urljoin(self.base_url, href)
                    
                    # Filter for profile URLs
                    if '/view/' in href and 'truepeoplesearch.com' in href:
                        urls.append(href)
        
        return urls
    
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Extract owner information from TruePeopleSearch profile page."""
        results = []
        
        # Extract person name
        person_name = self._extract_person_name(soup)
        
        # Extract business associations
        business_info = self._extract_business_associations(soup)
        
        if person_name and business_info:
            for business in business_info:
                result = ScrapingResult(
                    owner_name=self.clean_text(person_name),
                    business_name=self.clean_text(business.get('name', '')),
                    source=self.source_name,
                    url=url,
                    raw_data=business
                )
                results.append(result)
        elif person_name:
            # Create result with person info even if no business found
            result = ScrapingResult(
                owner_name=self.clean_text(person_name),
                source=self.source_name,
                url=url
            )
            results.append(result)
        
        return results
    
    def _extract_person_name(self, soup: BeautifulSoup) -> str:
        """Extract person name from profile page."""
        selectors = [
            'h1.person-name',
            'h1[data-testid="person-name"]',
            '.profile-header h1',
            'h1.name',
            '.person-header h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Fallback to title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Extract name from title
            name_match = re.match(r'^([A-Z][a-z]+\s+[A-Z][a-z]+)', title_text)
            if name_match:
                return name_match.group(1)
        
        return ""
    
    def _extract_business_associations(self, soup: BeautifulSoup) -> List[dict]:
        """Extract business associations from profile page."""
        businesses = []
        
        # Look for business/work information sections
        business_sections = soup.select('.work-info, .business-info, .employment, .associated-businesses')
        
        for section in business_sections:
            # Extract business names
            business_names = section.select('.company-name, .business-name, .employer')
            for name_elem in business_names:
                business_name = name_elem.get_text(strip=True)
                if business_name:
                    businesses.append({
                        'name': business_name,
                        'type': 'employment'
                    })
        
        # Look for business ownership indicators in text
        page_text = soup.get_text()
        
        # Patterns for business ownership
        ownership_patterns = [
            r'Owner of ([A-Z][A-Za-z0-9\s&,.-]+)',
            r'CEO of ([A-Z][A-Za-z0-9\s&,.-]+)',
            r'President of ([A-Z][A-Za-z0-9\s&,.-]+)',
            r'Founder of ([A-Z][A-Za-z0-9\s&,.-]+)'
        ]
        
        for pattern in ownership_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            for match in matches:
                businesses.append({
                    'name': match.strip(),
                    'type': 'ownership'
                })
        
        # Look for structured data
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict) and 'worksFor' in data:
                    works_for = data['worksFor']
                    if isinstance(works_for, dict) and 'name' in works_for:
                        businesses.append({
                            'name': works_for['name'],
                            'type': 'employment'
                        })
                    elif isinstance(works_for, list):
                        for org in works_for:
                            if isinstance(org, dict) and 'name' in org:
                                businesses.append({
                                    'name': org['name'],
                                    'type': 'employment'
                                })
            except:
                continue
        
        # Deduplicate businesses
        unique_businesses = []
        seen_names = set()
        
        for business in businesses:
            name = business['name'].lower()
            if name not in seen_names and len(name) > 2:
                seen_names.add(name)
                unique_businesses.append(business)
        
        return unique_businesses
