"""
Better Business Bureau (BBB.org) scraper for business owner information.
Uses Google search with site: operator as specified by client requirements.
"""

import re
from typing import List, Dict, Any
from urllib.parse import urljoin, quote_plus
import requests

from bs4 import BeautifulSoup

from .base_scraper import BaseScraper
from ..core import ScrapingR<PERSON>ult
from ..utils.google_search import GoogleSearchEngine


class BBBScraper(BaseScraper):
    """Scraper for BBB.org business listings using Google search with site: operator."""

    def __init__(self, engine):
        super().__init__(engine, 'bbb')
        self.google_search = GoogleSearchEngine(engine.config)

        # Enhanced Cloudflare bypass for BBB
        self.cloudflare_bypass_enabled = engine.config.get('sources', {}).get('bbb', {}).get('cloudflare_bypass', True)
        if self.cloudflare_bypass_enabled:
            self.logger.info("Enhanced Cloudflare bypass enabled for BBB")
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """Search BBB for business owners using Google site: operator."""
        if not self.is_enabled():
            return []

        self.logger.info(f"Searching BBB via Google for '{business_type}' owners in '{location}'")

        results = []

        # Use Google search with site: operator as specified by client
        # Pattern: site:bbb.org "Owner" "lawn care" "Dallas"
        google_results = self.google_search.search_site_for_owners('bbb.org', business_type, location)

        self.logger.info(f"Found {len(google_results)} BBB listings from Google search")

        # Extract URLs and scrape each listing
        for google_result in google_results:
            url = google_result['url']
            listing_results = self.scrape_listing(url, business_type, location)

            # Enhance results with Google snippet information
            for result in listing_results:
                result.google_title = google_result.get('title', '')
                result.google_snippet = google_result.get('snippet', '')
                result.search_pattern = f'site:bbb.org "Owner" "{business_type}" "{location}"'

                # Add any extracted info from Google snippet
                if 'snippet_phone' in google_result and not result.phone:
                    result.phone = google_result['snippet_phone']
                if 'snippet_email' in google_result and not result.email:
                    result.email = google_result['snippet_email']
                if 'snippet_owner' in google_result and not result.owner_name:
                    result.owner_name = google_result['snippet_owner']
                if 'snippet_address' in google_result and not result.address:
                    result.address = google_result['snippet_address']
                if 'snippet_years_in_business' in google_result:
                    result.years_in_business = google_result['snippet_years_in_business']
                if 'snippet_rating' in google_result:
                    result.google_rating = google_result['snippet_rating']

            results.extend(listing_results)

        return results
    
    def build_search_url(self, business_type: str, location: str) -> str:
        """Build BBB search URL."""
        # BBB search format: https://www.bbb.org/search?find_country=USA&find_text=lawn+care&find_loc=dallas+tx
        query = f"{business_type} owner"
        encoded_query = quote_plus(query)
        encoded_location = quote_plus(location)
        
        return f"{self.base_url}/search?find_country=USA&find_text={encoded_query}&find_loc={encoded_location}"
    
    def _build_page_url(self, base_url: str, page: int) -> str:
        """Build URL for specific page."""
        if page == 1:
            return base_url
        return f"{base_url}&page={page}"
    
    def _extract_listing_urls(self, soup: BeautifulSoup) -> List[str]:
        """Extract business listing URLs from BBB search results."""
        urls = []
        
        # BBB listing selectors
        selectors = [
            'a[href*="/us/"]',  # BBB business profile URLs
            '.search-result-title a',
            '.business-name a',
            'h3 a[href*="/us/"]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    if href.startswith('/'):
                        href = urljoin(self.base_url, href)
                    
                    # Filter for business profile URLs
                    if '/us/' in href and 'bbb.org' in href:
                        urls.append(href)
        
        return urls
    
    def extract_owner_info(self, soup: BeautifulSoup, url: str) -> List[ScrapingResult]:
        """Extract comprehensive owner and business information from BBB business page."""
        results = []

        # Extract all business information
        business_info = self._extract_comprehensive_business_info(soup)
        owner_names = self._extract_owner_names(soup)

        if owner_names:
            for owner_name in owner_names:
                result = ScrapingResult(
                    owner_name=self.clean_text(owner_name),
                    source=self.source_name,
                    url=url,
                    **business_info  # Unpack all the comprehensive business info
                )
                results.append(result)
        else:
            # Create result with business info even if no owner found
            result = ScrapingResult(
                source=self.source_name,
                url=url,
                **business_info  # Unpack all the comprehensive business info
            )
            results.append(result)

        return results
    
    def _extract_business_name(self, soup: BeautifulSoup) -> str:
        """Extract business name from BBB page."""
        selectors = [
            'h1.business-name',
            'h1[data-testid="business-name"]',
            '.business-header h1',
            'h1.biz-name',
            '.business-title h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Fallback to title tag
        title = soup.find('title')
        if title:
            title_text = title.get_text()
            # Remove BBB suffix
            title_text = re.sub(r'\s*\|\s*Better Business Bureau.*$', '', title_text)
            return title_text.strip()
        
        return ""
    
    def _extract_owner_names(self, soup: BeautifulSoup) -> List[str]:
        """Extract owner names from BBB page."""
        owner_names = []
        
        # Get all text content
        page_text = soup.get_text()
        
        # Owner extraction patterns
        patterns = [
            r'Owner[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Principal[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'CEO[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'President[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Founder[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Managing Director[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'Contact Person[:\s]+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            owner_names.extend(matches)
        
        # Look for owner info in specific sections
        owner_sections = soup.select('.business-details, .contact-info, .business-info')
        for section in owner_sections:
            section_text = section.get_text()
            for pattern in patterns:
                matches = re.findall(pattern, section_text, re.IGNORECASE)
                owner_names.extend(matches)
        
        # Look for structured data
        structured_data = soup.find_all('script', type='application/ld+json')
        for script in structured_data:
            try:
                import json
                data = json.loads(script.string)
                if isinstance(data, dict):
                    # Look for person or founder information
                    if 'founder' in data:
                        founder = data['founder']
                        if isinstance(founder, dict) and 'name' in founder:
                            owner_names.append(founder['name'])
                        elif isinstance(founder, str):
                            owner_names.append(founder)
            except:
                continue
        
        # Clean and deduplicate
        cleaned_names = []
        for name in owner_names:
            cleaned_name = self.clean_text(name)
            if cleaned_name and len(cleaned_name) > 2 and cleaned_name not in cleaned_names:
                # Basic validation - should be a proper name
                if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*$', cleaned_name):
                    cleaned_names.append(cleaned_name)
        
        return cleaned_names

    def _extract_comprehensive_business_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract comprehensive business information from BBB page."""
        info = {}

        # Basic business information
        info['business_name'] = self.clean_text(self._extract_business_name(soup))

        # Contact information
        contact_info = self.extract_contact_info(soup)
        info.update(contact_info)

        # Extract additional phone numbers
        phone_numbers = self._extract_all_phone_numbers(soup)
        if phone_numbers:
            info['phone'] = phone_numbers[0] if phone_numbers else None
            info['phone_secondary'] = phone_numbers[1] if len(phone_numbers) > 1 else None

        # Extract website
        info['website'] = self._extract_website(soup)

        # Extract detailed address components
        address_info = self._extract_detailed_address(soup)
        info.update(address_info)

        # Extract business details
        info['business_description'] = self._extract_business_description(soup)
        info['business_category'] = self._extract_business_category(soup)
        info['years_in_business'] = self._extract_years_in_business(soup)
        info['employee_count'] = self._extract_employee_count(soup)

        # Extract BBB-specific information
        info['bbb_rating'] = self._extract_bbb_rating(soup)
        info['bbb_accredited'] = self._extract_bbb_accreditation(soup)

        # Extract social media links
        social_media = self._extract_social_media_links(soup)
        info.update(social_media)

        # Extract business structure information
        info['business_structure'] = self._extract_business_structure(soup)
        info['business_license'] = self._extract_business_license(soup)

        # Extract executives/management
        info['executives'] = self._extract_executives(soup)

        # Extract reviews information
        info['total_reviews'] = self._extract_total_reviews(soup)

        return info

    def _extract_all_phone_numbers(self, soup: BeautifulSoup) -> List[str]:
        """Extract all phone numbers from BBB page."""
        phone_numbers = []

        # Phone number patterns
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',
            r'\b\d{3}\s\d{3}\s\d{4}\b',
            r'1[-.]?\d{3}[-.]?\d{3}[-.]?\d{4}'
        ]

        text = soup.get_text()
        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            phone_numbers.extend(matches)

        # Look for phone numbers in specific elements
        phone_selectors = [
            '[class*="phone"]',
            '[class*="contact"]',
            '[data-phone]',
            'a[href^="tel:"]'
        ]

        for selector in phone_selectors:
            elements = soup.select(selector)
            for elem in elements:
                if elem.name == 'a' and elem.get('href', '').startswith('tel:'):
                    phone = elem.get('href').replace('tel:', '').strip()
                    phone_numbers.append(phone)
                else:
                    text = elem.get_text()
                    for pattern in phone_patterns:
                        matches = re.findall(pattern, text)
                        phone_numbers.extend(matches)

        # Clean and deduplicate
        cleaned_phones = []
        for phone in phone_numbers:
            cleaned = re.sub(r'\D', '', phone)
            if len(cleaned) >= 10 and cleaned not in [re.sub(r'\D', '', p) for p in cleaned_phones]:
                cleaned_phones.append(phone)

        return cleaned_phones[:3]  # Return up to 3 phone numbers

    def _extract_website(self, soup: BeautifulSoup) -> str:
        """Extract business website from BBB page."""
        # Look for website links
        website_selectors = [
            'a[href*="http"]:not([href*="bbb.org"])',
            '[class*="website"] a',
            '[class*="url"] a',
            'a[data-website]'
        ]

        for selector in website_selectors:
            elem = soup.select_one(selector)
            if elem:
                href = elem.get('href', '')
                if href and not any(domain in href for domain in ['bbb.org', 'facebook.com', 'twitter.com', 'linkedin.com']):
                    return href

        # Look for website in text patterns
        text = soup.get_text()
        website_pattern = r'(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\.[a-zA-Z]{2,})?'
        matches = re.findall(website_pattern, text)

        for match in matches:
            if not any(domain in match for domain in ['bbb.org', 'facebook.com', 'twitter.com']):
                return f"http://{match}" if not match.startswith('http') else match

        return None

    def _extract_detailed_address(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract detailed address components from BBB page."""
        address_info = {}

        # Look for structured address data
        address_selectors = [
            '[class*="address"]',
            '[class*="location"]',
            '[itemtype*="PostalAddress"]',
            '.contact-info'
        ]

        for selector in address_selectors:
            elem = soup.select_one(selector)
            if elem:
                # Try to extract structured data
                street = elem.select_one('[itemprop="streetAddress"], .street')
                city = elem.select_one('[itemprop="addressLocality"], .city')
                state = elem.select_one('[itemprop="addressRegion"], .state')
                zip_code = elem.select_one('[itemprop="postalCode"], .zip')

                if street:
                    address_info['street_address'] = street.get_text(strip=True)
                if city:
                    address_info['city'] = city.get_text(strip=True)
                if state:
                    address_info['state'] = state.get_text(strip=True)
                if zip_code:
                    address_info['zip_code'] = zip_code.get_text(strip=True)

                # If structured data not found, parse full address
                if not any([street, city, state, zip_code]):
                    full_address = elem.get_text(strip=True)
                    address_info['address'] = full_address

                    # Try to parse components from full address
                    parsed = self._parse_address_components(full_address)
                    address_info.update(parsed)

                break

        return address_info

    def _parse_address_components(self, address: str) -> Dict[str, str]:
        """Parse address components from full address string."""
        components = {}

        # Basic address parsing patterns
        zip_pattern = r'\b\d{5}(?:-\d{4})?\b'
        state_pattern = r'\b[A-Z]{2}\b'

        zip_match = re.search(zip_pattern, address)
        if zip_match:
            components['zip_code'] = zip_match.group()
            address = address.replace(zip_match.group(), '').strip()

        state_match = re.search(state_pattern, address)
        if state_match:
            components['state'] = state_match.group()
            address = address.replace(state_match.group(), '').strip()

        # Remaining parts
        parts = [part.strip() for part in address.split(',') if part.strip()]
        if len(parts) >= 2:
            components['street_address'] = parts[0]
            components['city'] = parts[1]
        elif len(parts) == 1:
            components['street_address'] = parts[0]

        return components

    def _extract_business_description(self, soup: BeautifulSoup) -> str:
        """Extract business description from BBB page."""
        description_selectors = [
            '[class*="description"]',
            '[class*="about"]',
            '.business-summary',
            '.company-description',
            '[itemprop="description"]'
        ]

        for selector in description_selectors:
            elem = soup.select_one(selector)
            if elem:
                desc = elem.get_text(strip=True)
                if len(desc) > 20:  # Ensure it's substantial
                    return desc

        return None

    def _extract_business_category(self, soup: BeautifulSoup) -> str:
        """Extract business category from BBB page."""
        category_selectors = [
            '[class*="category"]',
            '[class*="industry"]',
            '.business-type',
            '[itemprop="category"]'
        ]

        for selector in category_selectors:
            elem = soup.select_one(selector)
            if elem:
                return elem.get_text(strip=True)

        # Look for category in breadcrumbs
        breadcrumbs = soup.select('.breadcrumb a, .breadcrumbs a')
        if len(breadcrumbs) > 1:
            return breadcrumbs[-2].get_text(strip=True)

        return None

    def _extract_years_in_business(self, soup: BeautifulSoup) -> str:
        """Extract years in business from BBB page."""
        text = soup.get_text()

        patterns = [
            r'(\d+)\s*years?\s*in\s*business',
            r'established\s*(?:in\s*)?(\d{4})',
            r'since\s*(\d{4})',
            r'founded\s*(?:in\s*)?(\d{4})'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                year_or_years = match.group(1)
                if len(year_or_years) == 4:  # It's a year
                    from datetime import datetime
                    current_year = datetime.now().year
                    years = current_year - int(year_or_years)
                    return str(years)
                else:  # It's already years
                    return year_or_years

        return None

    def _extract_employee_count(self, soup: BeautifulSoup) -> str:
        """Extract employee count from BBB page."""
        text = soup.get_text()

        patterns = [
            r'(\d+)\s*employees?',
            r'staff\s*of\s*(\d+)',
            r'team\s*of\s*(\d+)',
            r'(\d+)\s*(?:full-time|part-time)?\s*(?:staff|workers?|people)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def _extract_bbb_rating(self, soup: BeautifulSoup) -> str:
        """Extract BBB rating from BBB page."""
        rating_selectors = [
            '.bbb-rating',
            '[class*="rating"]',
            '.grade',
            '[data-rating]'
        ]

        for selector in rating_selectors:
            elem = soup.select_one(selector)
            if elem:
                rating = elem.get_text(strip=True)
                if rating and len(rating) <= 5:  # BBB ratings are typically A+, A, B, etc.
                    return rating

        # Look for rating in text
        text = soup.get_text()
        rating_pattern = r'BBB\s*Rating:?\s*([A-F][+-]?)'
        match = re.search(rating_pattern, text, re.IGNORECASE)
        if match:
            return match.group(1)

        return None

    def _extract_bbb_accreditation(self, soup: BeautifulSoup) -> bool:
        """Extract BBB accreditation status from BBB page."""
        text = soup.get_text().lower()

        accredited_indicators = [
            'bbb accredited',
            'accredited business',
            'accredited by bbb',
            'accredited member'
        ]

        for indicator in accredited_indicators:
            if indicator in text:
                return True

        # Look for accreditation badges or icons
        accred_selectors = [
            '[class*="accredited"]',
            '[class*="badge"]',
            '.accreditation'
        ]

        for selector in accred_selectors:
            elem = soup.select_one(selector)
            if elem and 'accredited' in elem.get_text().lower():
                return True

        return False

    def _extract_social_media_links(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract social media links from BBB page."""
        social_links = {}

        # Social media patterns
        social_patterns = {
            'linkedin_url': r'linkedin\.com/(?:company/|in/)[^/\s]+',
            'facebook_url': r'facebook\.com/[^/\s]+',
            'twitter_url': r'twitter\.com/[^/\s]+',
            'instagram_url': r'instagram\.com/[^/\s]+',
            'youtube_url': r'youtube\.com/(?:channel/|user/|c/)[^/\s]+'
        }

        # Look for social media links in href attributes
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            for platform, pattern in social_patterns.items():
                if re.search(pattern, href, re.IGNORECASE):
                    social_links[platform] = href
                    break

        return social_links

    def _extract_business_structure(self, soup: BeautifulSoup) -> str:
        """Extract business structure (LLC, Corp, etc.) from BBB page."""
        text = soup.get_text()

        structure_patterns = [
            r'\b(LLC|L\.L\.C\.)\b',
            r'\b(Corporation|Corp\.?)\b',
            r'\b(Inc\.?|Incorporated)\b',
            r'\b(Partnership|LLP)\b',
            r'\b(Sole Proprietorship)\b',
            r'\b(Limited Partnership|LP)\b'
        ]

        for pattern in structure_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def _extract_business_license(self, soup: BeautifulSoup) -> str:
        """Extract business license information from BBB page."""
        text = soup.get_text()

        license_patterns = [
            r'License\s*#?:?\s*([A-Z0-9-]+)',
            r'Licensed\s*#?:?\s*([A-Z0-9-]+)',
            r'Permit\s*#?:?\s*([A-Z0-9-]+)'
        ]

        for pattern in license_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def _extract_executives(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extract executives/management information from BBB page."""
        executives = []

        # Look for management sections
        mgmt_sections = soup.select('.management, .executives, .leadership, .staff')

        for section in mgmt_sections:
            text = section.get_text()

            # Extract name and title patterns
            exec_patterns = [
                r'([A-Z][a-z]+\s+[A-Z][a-z]+),?\s*(CEO|President|Owner|Manager|Director|VP|Vice President)',
                r'(CEO|President|Owner|Manager|Director|VP|Vice President):?\s*([A-Z][a-z]+\s+[A-Z][a-z]+)'
            ]

            for pattern in exec_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    if len(match) == 2:
                        name, title = match if match[0].replace(' ', '').isalpha() else (match[1], match[0])
                        executives.append({
                            'name': name.strip(),
                            'title': title.strip()
                        })

        return executives[:5]  # Limit to 5 executives

    def _extract_total_reviews(self, soup: BeautifulSoup) -> str:
        """Extract total number of reviews from BBB page."""
        text = soup.get_text()

        review_patterns = [
            r'(\d+)\s*reviews?',
            r'(\d+)\s*customer\s*reviews?',
            r'(\d+)\s*ratings?',
            r'based\s*on\s*(\d+)\s*reviews?'
        ]

        for pattern in review_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None
