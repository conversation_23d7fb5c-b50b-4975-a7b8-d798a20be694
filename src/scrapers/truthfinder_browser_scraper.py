#!/usr/bin/env python3
"""
TruthFinder Browser Scraper - Uses Playwright to make API calls within browser context.
This is the ONLY reliable method for TruthFinder that bypasses Cloudflare protection.
"""

import asyncio
import logging
from typing import List, Dict, Any
from datetime import datetime
from playwright.async_api import async_playwright

from src.scrapers.base_scraper import BaseScraper
from src.core import ScrapingResult

logger = logging.getLogger(__name__)

class TruthFinderBrowserScraper(BaseScraper):
    """TruthFinder scraper using browser-based API calls to bypass anti-bot protection."""
    
    def __init__(self, engine):
        super().__init__(engine, "truthfinder_browser")
        self.api_key = "B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98"
        self.base_url = "https://api2.truthfinder.com"
        
        logger.info("TruthFinder Browser scraper initialized")
    
    def is_enabled(self) -> bool:
        """Check if TruthFinder browser scraper is enabled."""
        return self.engine.config.get('sources', {}).get('truthfinder_browser', {}).get('enabled', True)
    
    def search(self, business_type: str, location: str) -> List[ScrapingResult]:
        """
        Search for business owners using TruthFinder browser API.
        
        Args:
            business_type: Type of business to search for
            location: Location to search in
            
        Returns:
            List of ScrapingResult objects
        """
        if not self.is_enabled():
            logger.info("TruthFinder browser scraper is disabled")
            return []
        
        logger.info(f"Searching TruthFinder browser API for {business_type} owners in {location}")
        
        try:
            # Parse location
            city, state = self._parse_location(location)
            
            # Common business owner names to search for
            common_names = [
                ("John", "Smith"), ("Michael", "Johnson"), ("David", "Williams"),
                ("Robert", "Brown"), ("James", "Jones"), ("William", "Garcia"),
                ("Richard", "Miller"), ("Joseph", "Davis"), ("Thomas", "Rodriguez"),
                ("Christopher", "Martinez")
            ]
            
            all_results = []
            
            # Search for multiple common names (limit to avoid too many browser instances)
            for first_name, last_name in common_names[:3]:  # Limit to 3 searches
                try:
                    logger.info(f"Searching for {first_name} {last_name} in {city}, {state}")
                    
                    # Use asyncio to run the browser search
                    api_data = asyncio.run(self._search_browser_api(first_name, last_name, city, state))
                    
                    if api_data:
                        # Convert API data to ScrapingResult objects
                        results = self._convert_api_data_to_results(api_data, business_type, location)
                        all_results.extend(results)
                        
                        logger.info(f"Found {len(results)} results for {first_name} {last_name}")
                        
                        # Limit total results to avoid overwhelming
                        if len(all_results) >= 10:
                            break
                    
                except Exception as e:
                        logger.error(f"Error searching for {first_name} {last_name}: {e}")
                        continue
            
            logger.info(f"TruthFinder browser API returned {len(all_results)} total results")
            return all_results
            
        except Exception as e:
            logger.error(f"TruthFinder browser search failed: {e}")
            return []
    
    async def _search_browser_api(self, first_name: str, last_name: str, city: str = "", state: str = ""):
        """Search TruthFinder API using browser context."""
        
        async with async_playwright() as p:
            # Launch browser (headless for production)
            browser = await p.chromium.launch(
                headless=True,  # Set to False for debugging
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage'
                ]
            )
            
            # Create realistic browser context
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            # Add stealth script
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            page = await context.new_page()
            
            try:
                # Navigate to TruthFinder to establish session
                await page.goto("https://www.truthfinder.com", wait_until="networkidle")
                await page.wait_for_timeout(3000)
                
                # Handle any modals/popups
                try:
                    agree_button = await page.query_selector('button:has-text("I AGREE"), button:has-text("I Agree"), button.green')
                    if agree_button:
                        await agree_button.click()
                        await page.wait_for_timeout(2000)
                except:
                    pass
                
                # Build API URL
                api_url = f"{self.base_url}/v1/people/"
                params = {
                    'firstName': first_name,
                    'lastName': last_name,
                    'fields': 'names,locations,related_persons,phones,emails,addresses,demographics'
                }
                
                if city:
                    params['city'] = city
                if state:
                    params['state'] = state
                
                # Convert params to URL string
                param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
                full_url = f"{api_url}?{param_string}"
                
                # Make the API call using browser's fetch API
                api_response = await page.evaluate(f"""
                    async () => {{
                        try {{
                            const response = await fetch('{full_url}', {{
                                method: 'GET',
                                headers: {{
                                    'accept': '*/*',
                                    'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                                    'api-key': '{self.api_key}',
                                    'app-id': 'tf-web',
                                    'cache-control': 'no-cache',
                                    'origin': 'https://www.truthfinder.com',
                                    'referer': 'https://www.truthfinder.com/search/'
                                }}
                            }});
                            
                            if (response.ok) {{
                                const data = await response.json();
                                return {{ success: true, data: data, status: response.status }};
                            }} else {{
                                const text = await response.text();
                                return {{ success: false, error: text, status: response.status }};
                            }}
                        }} catch (error) {{
                            return {{ success: false, error: error.message, status: 0 }};
                        }}
                    }}
                """)
                
                await browser.close()
                
                if api_response['success']:
                    return api_response['data']
                else:
                    logger.warning(f"API call failed for {first_name} {last_name}: Status {api_response['status']}")
                    return None
                    
            except Exception as e:
                logger.error(f"Browser API call failed: {e}")
                await browser.close()
                return None
    
    def _convert_api_data_to_results(self, api_data: List[Dict], business_type: str, location: str) -> List[ScrapingResult]:
        """Convert TruthFinder API data to ScrapingResult objects."""
        results = []
        
        for person_data in api_data:
            try:
                # Extract primary name
                owner_name = ""
                if 'names' in person_data and person_data['names']:
                    name = person_data['names'][0]
                    owner_name = f"{name.get('first', '')} {name.get('middle', '')} {name.get('last', '')}".strip()
                
                # Extract primary location
                address = ""
                city = ""
                state = ""
                zip_code = ""
                if 'locations' in person_data and person_data['locations']:
                    location_data = person_data['locations'][0]
                    if 'address' in location_data:
                        addr = location_data['address']
                        city = addr.get('city', '')
                        state = addr.get('state_code', '')
                        zip_code = addr.get('zip_code', '')
                        street = addr.get('street', '')
                        address = f"{street} {city}, {state} {zip_code}".strip()
                
                # Extract phone numbers
                phone = ""
                if 'phones' in person_data and person_data['phones']:
                    phone = person_data['phones'][0].get('number', '')
                
                # Extract emails
                email = ""
                if 'emails' in person_data and person_data['emails']:
                    email = person_data['emails'][0].get('email', '')
                
                # Extract age from demographics or related persons
                owner_age = ""
                if 'demographics' in person_data and person_data['demographics']:
                    demo = person_data['demographics'][0]
                    if 'age' in demo:
                        owner_age = str(demo['age'])
                
                # Extract related persons (family members)
                executives = []
                previous_addresses = []
                if 'related_persons' in person_data:
                    for related in person_data['related_persons']:
                        if 'names' in related and related['names']:
                            rel_name = f"{related['names'][0].get('first', '')} {related['names'][0].get('last', '')}".strip()
                            relationship = related.get('sub_type', 'Related')
                            if rel_name:
                                executives.append(f"{rel_name} ({relationship})")
                
                # Extract previous addresses
                if 'locations' in person_data:
                    for i, location_data in enumerate(person_data['locations'][1:], 1):  # Skip first (current)
                        if 'address' in location_data:
                            addr = location_data['address']
                            prev_addr = f"{addr.get('street', '')} {addr.get('city', '')}, {addr.get('state_code', '')} {addr.get('zip_code', '')}".strip()
                            if prev_addr:
                                previous_addresses.append(prev_addr)
                
                # Create ScrapingResult
                result = ScrapingResult(
                    owner_name=owner_name,
                    business_name="",  # TruthFinder doesn't provide business names
                    business_type=business_type,
                    location=location,
                    source=self.source_name,
                    url="",
                    phone=phone,
                    email=email,
                    address=address,
                    street_address=person_data['locations'][0]['address'].get('street', '') if person_data.get('locations') and person_data['locations'][0].get('address') else "",
                    city=city,
                    state=state,
                    zip_code=zip_code,
                    owner_age=owner_age,
                    executives=executives,
                    previous_addresses=previous_addresses,
                    scraped_at=datetime.now(),
                    confidence_score=0.9,  # High confidence for API data
                    data_quality='high',
                    verification_status='api_verified',
                    raw_data=person_data
                )
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error converting API data to result: {e}")
                continue
        
        return results
    
    def _parse_location(self, location: str) -> tuple:
        """Parse location string into city and state."""
        try:
            parts = location.split(',')
            if len(parts) >= 2:
                city = parts[0].strip()
                state = parts[1].strip()
                return city, state
            else:
                # Try to split by space and assume last part is state
                parts = location.split()
                if len(parts) >= 2:
                    state = parts[-1]
                    city = ' '.join(parts[:-1])
                    return city, state
                else:
                    return location, ""
        except:
            return location, ""
    
    def _extract_listing_urls(self, soup, business_type: str, location: str) -> List[str]:
        """Required by BaseScraper but not used in browser API mode."""
        return []
    
    def extract_owner_info(self, soup, url: str) -> List[ScrapingResult]:
        """Required by BaseScraper but not used in browser API mode."""
        return []
    
    async def _get_fresh_truthfinder_cookies(self):
        """Get fresh cookies by establishing a new TruthFinder session."""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36'
            )
            page = await context.new_page()

            try:
                # Navigate to TruthFinder with tracking parameters
                search_url = "https://www.truthfinder.com/search/?utm_source=VBDA&traffic[source]=VBDA&utm_medium=affiliate&traffic[medium]=affiliate&utm_campaign=truepeoplesearch&traffic[campaign]=Banner:truepeoplesearch&utm_term=first&traffic[term]=first&utm_content=&traffic[content]=&s1=truepeoplesearch&s2=Banner&s3=first&s4=&s5=&traffic[placement]=&traffic[funnel]=bg&ck_rsid=3757101803"

                await page.goto(search_url, wait_until="networkidle")
                await page.wait_for_timeout(5000)  # Wait for cookies to be set

                # Get all cookies
                cookies = await context.cookies()

                # Filter for relevant cookies
                relevant_cookies = []
                for cookie in cookies:
                    if cookie['name'] in ['__cf_bm', 'sessionId', 'sessionCreated', 'device-id', 'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'ck_rsid']:
                        relevant_cookies.append(cookie)

                logger.info(f"Retrieved {len(relevant_cookies)} fresh TruthFinder cookies")
                return relevant_cookies

            except Exception as e:
                logger.error(f"Error getting fresh TruthFinder cookies: {e}")
                return []
            finally:
                await browser.close()

    def search_person(self, query) -> List[ScrapingResult]:
        """
        Search for a specific person using TruthFinder browser API.

        Args:
            query: PersonSearchQuery object with search parameters

        Returns:
            List of ScrapingResult objects with person information
        """
        if not self.is_enabled():
            logger.info("TruthFinder browser scraper is disabled")
            return []

        logger.info(f"Searching TruthFinder for person: {query.first_name} {query.last_name}")

        try:
            # Use asyncio to run the browser search
            api_data = asyncio.run(self._search_person_browser_api(
                query.first_name,
                query.last_name,
                query.city or "",
                query.state or ""
            ))

            if api_data:
                # Convert API data to ScrapingResult objects
                results = self._convert_person_api_data_to_results(api_data, query)
                logger.info(f"Found {len(results)} person results for {query.first_name} {query.last_name}")
                return results
            else:
                logger.info(f"No person results found for {query.first_name} {query.last_name}")
                return []

        except Exception as e:
            logger.error(f"TruthFinder person search failed: {e}")
            return []

    def search_by_name(self, first_name: str, last_name: str, location: str = "") -> List[ScrapingResult]:
        """
        Search for a person by name and location.

        Args:
            first_name: Person's first name
            last_name: Person's last name
            location: Location string (city, state format)

        Returns:
            List of ScrapingResult objects
        """
        # Parse location
        city, state = self._parse_location(location) if location else ("", "")

        # Create a simple query object
        class SimpleQuery:
            def __init__(self, first_name, last_name, city, state):
                self.first_name = first_name
                self.last_name = last_name
                self.city = city
                self.state = state
                self.address = location

        query = SimpleQuery(first_name, last_name, city, state)
        return self.search_person(query)

    async def _search_person_browser_api(self, first_name: str, last_name: str, city: str = "", state: str = ""):
        """Search TruthFinder API for a specific person using browser context."""

        async with async_playwright() as p:
            # Launch browser (headless for production)
            browser = await p.chromium.launch(
                headless=True,  # Set to False for debugging
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage'
                ]
            )

            # Create realistic browser context with exact user agent from curl
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York'
            )

            # Add stealth script
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)

            page = await context.new_page()

            try:
                # Navigate to TruthFinder to establish session
                await page.goto("https://www.truthfinder.com", wait_until="networkidle")
                await page.wait_for_timeout(3000)

                # Handle any modals/popups
                try:
                    agree_button = await page.query_selector('button:has-text("I AGREE"), button:has-text("I Agree"), button.green')
                    if agree_button:
                        await agree_button.click()
                        await page.wait_for_timeout(2000)
                except:
                    pass

                # Try to set cookies from the working curl request first
                try:
                    await context.add_cookies([
                        {
                            'name': '__cf_bm',
                            'value': 'bkdw99zciEqn6FGYmLj7rJmqYS3VQquSkftfVGJ8zLg-1754335447-1.0.1.1-wmg2Ihf9bclu1gQKZIK0sA_oubH3NvFioBx.CVND9nDns_R8QGo4myKVR06UndzAG8ZPqLhlcBUehE027zfqQiXaA7s5h0cM.fTBsP3BXZVwbehHK_45QWtyhrgwu4m5',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'sessionId',
                            'value': '0d275f9f-783d-4bec-919c-388db8466e0b',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'sessionCreated',
                            'value': '2025-08-04T19%3A24%3A08%2B00%3A00',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'device-id',
                            'value': '704be081-0db7-4eab-a3ef-211a24dec5f6',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'utm_source',
                            'value': 'VBDA',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'utm_medium',
                            'value': 'affiliate',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'utm_campaign',
                            'value': 'truepeoplesearch',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'utm_term',
                            'value': 'first',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        },
                        {
                            'name': 'ck_rsid',
                            'value': '3757101803',
                            'domain': '.truthfinder.com',
                            'path': '/'
                        }
                    ])
                    logger.info("Set hardcoded TruthFinder cookies")
                except Exception as e:
                    logger.warning(f"Failed to set hardcoded cookies: {e}")
                    # If hardcoded cookies fail, try to get fresh ones
                    fresh_cookies = await self._get_fresh_truthfinder_cookies()
                    if fresh_cookies:
                        await context.add_cookies(fresh_cookies)
                        logger.info("Set fresh TruthFinder cookies")
                    else:
                        logger.warning("Failed to get fresh cookies, proceeding without")

                # Build API URL with exact parameters from curl
                api_url = f"{self.base_url}/v1/people/"
                params = {
                    'firstName': first_name,
                    'lastName': last_name,
                    'fields': 'names,locations,related_persons'  # Using exact fields from curl
                }

                if city:
                    params['city'] = city
                if state:
                    params['state'] = state

                # Set up headers exactly as in the curl request
                headers = {
                    'accept': '*/*',
                    'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                    'api-key': 'B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98',
                    'app-id': 'tf-web',
                    'cache-control': 'no-cache',
                    'origin': 'https://www.truthfinder.com',
                    'pragma': 'no-cache',
                    'priority': 'u=1, i',
                    'referer': f'https://www.truthfinder.com/search/?utm_source=VBDA&traffic[source]=VBDA&utm_medium=affiliate&traffic[medium]=affiliate&utm_campaign=truepeoplesearch&traffic[campaign]=Banner:truepeoplesearch&utm_term=first&traffic[term]=first&utm_content=&traffic[content]=&s1=truepeoplesearch&s2=Banner&s3=first&s4=&s5=&traffic[placement]=&traffic[funnel]=bg&ck_rsid=3757101803&firstName={first_name.lower()}&lastName={last_name.lower()}',
                    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                    'sec-ch-ua-mobile': '?1',
                    'sec-ch-ua-platform': '"Android"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-site',
                    'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36'
                }

                # Make API request within browser context with exact headers
                response = await page.request.get(api_url, params=params, headers=headers)

                logger.info(f"TruthFinder person API request to: {api_url}")
                logger.info(f"TruthFinder person API returned status: {response.status}")

                if response.status == 200:
                    data = await response.json()
                    logger.info(f"TruthFinder person API returned {len(data.get('results', []))} results")
                    return data
                else:
                    # Log response details for debugging
                    response_text = await response.text()
                    logger.warning(f"TruthFinder person API returned status {response.status}")
                    logger.warning(f"Response body: {response_text[:500]}...")
                    return None

            except Exception as e:
                logger.error(f"Error in TruthFinder person browser API: {e}")
                return None
            finally:
                await browser.close()

    def _convert_person_api_data_to_results(self, api_data: Dict, query) -> List[ScrapingResult]:
        """Convert TruthFinder person API data to ScrapingResult objects."""
        results = []

        if not api_data or 'results' not in api_data:
            return results

        for person_data in api_data['results']:
            try:
                # Extract basic information
                names = person_data.get('names', [])
                primary_name = names[0] if names else {}

                full_name = f"{primary_name.get('first', '')} {primary_name.get('last', '')}".strip()
                if not full_name:
                    continue

                # Extract current address
                locations = person_data.get('locations', [])
                current_location = locations[0] if locations else {}
                current_address = ""
                city = ""
                state = ""
                zip_code = ""

                if 'address' in current_location:
                    addr = current_location['address']
                    street = addr.get('street', '')
                    city = addr.get('city', '')
                    state = addr.get('state_code', '')
                    zip_code = addr.get('zip_code', '')
                    current_address = f"{street} {city}, {state} {zip_code}".strip()

                # Extract demographic information
                demographics = person_data.get('demographics', {})
                age = demographics.get('age')
                birth_date = demographics.get('birth_date')

                # Extract contact information
                phones = person_data.get('phones', [])
                phone_numbers = []
                primary_phone = ""

                for phone_data in phones:
                    phone_info = {
                        'number': phone_data.get('number', ''),
                        'type': phone_data.get('type', 'unknown'),
                        'carrier': phone_data.get('carrier', '')
                    }
                    phone_numbers.append(phone_info)

                    if not primary_phone and phone_info['number']:
                        primary_phone = phone_info['number']

                # Extract email addresses
                emails = person_data.get('emails', [])
                email_addresses = []
                primary_email = ""

                for email_data in emails:
                    email_info = {
                        'email': email_data.get('email', ''),
                        'type': email_data.get('type', 'unknown'),
                        'verified': email_data.get('verified', False)
                    }
                    email_addresses.append(email_info)

                    if not primary_email and email_info['email']:
                        primary_email = email_info['email']

                # Extract family members
                family_members = []
                related_persons = person_data.get('related_persons', [])

                for relative in related_persons:
                    family_info = {
                        'name': f"{relative.get('first_name', '')} {relative.get('last_name', '')}".strip(),
                        'relationship': relative.get('relationship', 'unknown'),
                        'age': str(relative.get('age', '')) if relative.get('age') else ''
                    }
                    if family_info['name']:
                        family_members.append(family_info)

                # Extract aliases
                aliases = []
                for name_data in names[1:]:  # Skip primary name
                    alias = f"{name_data.get('first', '')} {name_data.get('last', '')}".strip()
                    if alias and alias != full_name:
                        aliases.append(alias)

                # Extract address history
                address_history = []
                for location_data in locations[1:]:  # Skip current address
                    if 'address' in location_data:
                        addr = location_data['address']
                        hist_addr = f"{addr.get('street', '')} {addr.get('city', '')}, {addr.get('state_code', '')} {addr.get('zip_code', '')}".strip()
                        if hist_addr:
                            address_history.append({
                                'address': hist_addr,
                                'start_date': location_data.get('start_date', ''),
                                'end_date': location_data.get('end_date', ''),
                                'type': 'previous'
                            })

                # Create ScrapingResult
                result = ScrapingResult(
                    owner_name=full_name,
                    business_name="",  # Not applicable for person search
                    business_type="",  # Not applicable for person search
                    location=f"{city}, {state}".strip(", "),
                    source="truthfinder_browser",
                    address=current_address,
                    city=city,
                    state=state,
                    zip_code=zip_code,
                    phone=primary_phone,
                    email=primary_email,
                    scraped_at=datetime.now(),

                    # People-specific fields
                    aliases=aliases,
                    middle_name=primary_name.get('middle', ''),
                    owner_age=str(age) if age else '',
                    birth_date=birth_date,
                    family_members=family_members,
                    phone_numbers=phone_numbers,
                    email_addresses=email_addresses,
                    address_history=address_history,

                    # Search context
                    search_type='people',
                    search_query=f"{query.first_name} {query.last_name}",
                    match_confidence=0.9,  # High confidence for API results
                    data_quality='high',
                    verification_status='api_verified',

                    raw_data={
                        'api_response': person_data,
                        'source': 'truthfinder_browser',
                        'search_method': 'person_api'
                    }
                )

                results.append(result)

            except Exception as e:
                logger.error(f"Error parsing TruthFinder person data: {e}")
                continue

        return results

    def get_health_status(self) -> Dict[str, Any]:
        """Get scraper health status."""
        try:
            return {
                'scraper': 'truthfinder_browser',
                'status': 'healthy',
                'enabled': self.is_enabled(),
                'method': 'browser_api',
                'playwright_available': True,
                'supports_people_search': True
            }
        except Exception as e:
            return {
                'scraper': 'truthfinder_browser',
                'status': 'unhealthy',
                'error': str(e),
                'enabled': self.is_enabled()
            }
