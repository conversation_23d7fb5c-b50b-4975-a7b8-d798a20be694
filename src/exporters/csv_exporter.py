"""
CSV exporter for scraped business owner data.
"""

import csv
import logging
import os
from typing import List, Dict, Any
from datetime import datetime
import pandas as pd

from ..core import ScrapingResult


class CSVExporter:
    """Export scraped results to CSV format."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.output_config = config.get('output', {})
        self.columns = self.output_config.get('csv_columns', [
            'owner_name', 'business_name', 'business_type', 'location',
            'source', 'url', 'phone', 'email', 'address', 'scraped_at'
        ])
    
    def export(self, results: List[ScrapingResult], filename: str = None) -> str:
        """Export results to CSV file."""
        if not results:
            self.logger.warning("No results to export")
            return ""
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"business_owners_{timestamp}.csv"
        
        # Ensure output directory exists
        output_dir = self.config.get('general', {}).get('output_directory', './results')
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        try:
            # Convert results to DataFrame
            df = self._results_to_dataframe(results)
            
            # Export to CSV
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            self.logger.info(f"Exported {len(results)} results to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting to CSV: {e}")
            return ""
    
    def _results_to_dataframe(self, results: List[ScrapingResult]) -> pd.DataFrame:
        """Convert ScrapingResult objects to pandas DataFrame."""
        data = []
        
        for result in results:
            row = {}
            
            # Map result fields to columns
            for column in self.columns:
                if hasattr(result, column):
                    value = getattr(result, column)
                    
                    # Handle datetime objects
                    if isinstance(value, datetime):
                        value = value.strftime("%Y-%m-%d %H:%M:%S")
                    
                    row[column] = value
                else:
                    row[column] = None
            
            # Add additional fields from raw_data if configured
            if hasattr(result, 'raw_data') and result.raw_data:
                for key, value in result.raw_data.items():
                    if key not in row:  # Don't override existing columns
                        row[key] = value
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    def export_summary(self, results: List[ScrapingResult], filename: str = None) -> str:
        """Export summary statistics to CSV."""
        if not results:
            return ""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"scraping_summary_{timestamp}.csv"
        
        output_dir = self.config.get('general', {}).get('output_directory', './results')
        os.makedirs(output_dir, exist_ok=True)
        filepath = os.path.join(output_dir, filename)
        
        try:
            # Generate summary statistics
            summary_data = self._generate_summary_stats(results)
            
            # Convert to DataFrame and export
            df = pd.DataFrame(summary_data)
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            self.logger.info(f"Exported summary to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting summary: {e}")
            return ""
    
    def _generate_summary_stats(self, results: List[ScrapingResult]) -> List[Dict[str, Any]]:
        """Generate summary statistics from results."""
        summary = []
        
        # Overall statistics
        total_results = len(results)
        results_with_owner = len([r for r in results if r.owner_name])
        results_with_business = len([r for r in results if r.business_name])
        results_with_contact = len([r for r in results if r.phone or r.email])
        
        summary.append({
            'metric': 'Total Results',
            'value': total_results,
            'percentage': 100.0
        })
        
        summary.append({
            'metric': 'Results with Owner Name',
            'value': results_with_owner,
            'percentage': (results_with_owner / total_results * 100) if total_results > 0 else 0
        })
        
        summary.append({
            'metric': 'Results with Business Name',
            'value': results_with_business,
            'percentage': (results_with_business / total_results * 100) if total_results > 0 else 0
        })
        
        summary.append({
            'metric': 'Results with Contact Info',
            'value': results_with_contact,
            'percentage': (results_with_contact / total_results * 100) if total_results > 0 else 0
        })
        
        # Source breakdown
        source_counts = {}
        for result in results:
            source = result.source or 'Unknown'
            source_counts[source] = source_counts.get(source, 0) + 1
        
        for source, count in source_counts.items():
            summary.append({
                'metric': f'Results from {source}',
                'value': count,
                'percentage': (count / total_results * 100) if total_results > 0 else 0
            })
        
        # Business type breakdown
        business_type_counts = {}
        for result in results:
            business_type = result.business_type or 'Unknown'
            business_type_counts[business_type] = business_type_counts.get(business_type, 0) + 1
        
        for business_type, count in business_type_counts.items():
            summary.append({
                'metric': f'Results for {business_type}',
                'value': count,
                'percentage': (count / total_results * 100) if total_results > 0 else 0
            })
        
        # Location breakdown
        location_counts = {}
        for result in results:
            location = result.location or 'Unknown'
            location_counts[location] = location_counts.get(location, 0) + 1
        
        for location, count in location_counts.items():
            summary.append({
                'metric': f'Results from {location}',
                'value': count,
                'percentage': (count / total_results * 100) if total_results > 0 else 0
            })
        
        return summary
    
    def export_detailed(self, results: List[ScrapingResult], filename: str = None) -> str:
        """Export detailed results including raw data to CSV."""
        if not results:
            return ""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"business_owners_detailed_{timestamp}.csv"
        
        output_dir = self.config.get('general', {}).get('output_directory', './results')
        os.makedirs(output_dir, exist_ok=True)
        filepath = os.path.join(output_dir, filename)
        
        try:
            data = []
            
            for result in results:
                row = {
                    'owner_name': result.owner_name,
                    'business_name': result.business_name,
                    'business_type': result.business_type,
                    'location': result.location,
                    'source': result.source,
                    'url': result.url,
                    'phone': result.phone,
                    'email': result.email,
                    'address': result.address,
                    'scraped_at': result.scraped_at.strftime("%Y-%m-%d %H:%M:%S") if result.scraped_at else None
                }
                
                # Add all raw data fields
                if result.raw_data:
                    for key, value in result.raw_data.items():
                        # Convert complex objects to strings
                        if isinstance(value, (dict, list)):
                            value = str(value)
                        row[f'raw_{key}'] = value
                
                data.append(row)
            
            df = pd.DataFrame(data)
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            self.logger.info(f"Exported detailed results to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting detailed CSV: {e}")
            return ""
