"""
Excel exporter for scraped business owner data.
"""

import logging
import os
from typing import List, Dict, Any
from datetime import datetime
import pandas as pd

from ..core import ScrapingResult


class ExcelExporter:
    """Export scraped results to Excel format."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.output_config = config.get('output', {})
        self.columns = self.output_config.get('csv_columns', [
            'owner_name', 'business_name', 'business_type', 'location',
            'source', 'url', 'phone', 'email', 'address', 'scraped_at'
        ])
    
    def export(self, results: List[ScrapingResult], filename: str = None) -> str:
        """Export results to Excel file with multiple sheets."""
        if not results:
            self.logger.warning("No results to export")
            return ""
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"business_owners_{timestamp}.xlsx"
        
        # Ensure output directory exists
        output_dir = self.config.get('general', {}).get('output_directory', './results')
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        try:
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Main results sheet
                df_main = self._results_to_dataframe(results)
                df_main.to_excel(writer, sheet_name='Business Owners', index=False)
                
                # Summary sheet
                df_summary = self._create_summary_dataframe(results)
                df_summary.to_excel(writer, sheet_name='Summary', index=False)
                
                # Source breakdown sheet
                df_sources = self._create_source_breakdown(results)
                df_sources.to_excel(writer, sheet_name='Source Breakdown', index=False)
                
                # Location breakdown sheet
                df_locations = self._create_location_breakdown(results)
                df_locations.to_excel(writer, sheet_name='Location Breakdown', index=False)
                
                # Format worksheets
                self._format_worksheets(writer)
            
            self.logger.info(f"Exported {len(results)} results to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting to Excel: {e}")
            return ""
    
    def _results_to_dataframe(self, results: List[ScrapingResult]) -> pd.DataFrame:
        """Convert ScrapingResult objects to pandas DataFrame."""
        data = []
        
        for result in results:
            row = {}
            
            # Map result fields to columns
            for column in self.columns:
                if hasattr(result, column):
                    value = getattr(result, column)
                    
                    # Handle datetime objects
                    if isinstance(value, datetime):
                        value = value.strftime("%Y-%m-%d %H:%M:%S")
                    
                    row[column] = value
                else:
                    row[column] = None
            
            # Add confidence score if available
            if hasattr(result, 'raw_data') and result.raw_data:
                row['confidence_score'] = result.raw_data.get('confidence_score', 0)
                row['data_quality'] = result.raw_data.get('data_quality', 'unknown')
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    def _create_summary_dataframe(self, results: List[ScrapingResult]) -> pd.DataFrame:
        """Create summary statistics DataFrame."""
        total_results = len(results)
        
        summary_data = [
            {'Metric': 'Total Results', 'Count': total_results, 'Percentage': 100.0},
            {'Metric': 'Results with Owner Name', 
             'Count': len([r for r in results if r.owner_name]),
             'Percentage': len([r for r in results if r.owner_name]) / total_results * 100 if total_results > 0 else 0},
            {'Metric': 'Results with Business Name', 
             'Count': len([r for r in results if r.business_name]),
             'Percentage': len([r for r in results if r.business_name]) / total_results * 100 if total_results > 0 else 0},
            {'Metric': 'Results with Phone', 
             'Count': len([r for r in results if r.phone]),
             'Percentage': len([r for r in results if r.phone]) / total_results * 100 if total_results > 0 else 0},
            {'Metric': 'Results with Email', 
             'Count': len([r for r in results if r.email]),
             'Percentage': len([r for r in results if r.email]) / total_results * 100 if total_results > 0 else 0},
            {'Metric': 'Results with Address', 
             'Count': len([r for r in results if r.address]),
             'Percentage': len([r for r in results if r.address]) / total_results * 100 if total_results > 0 else 0}
        ]
        
        return pd.DataFrame(summary_data)
    
    def _create_source_breakdown(self, results: List[ScrapingResult]) -> pd.DataFrame:
        """Create source breakdown DataFrame."""
        source_counts = {}
        total_results = len(results)
        
        for result in results:
            source = result.source or 'Unknown'
            source_counts[source] = source_counts.get(source, 0) + 1
        
        source_data = []
        for source, count in sorted(source_counts.items()):
            source_data.append({
                'Source': source,
                'Count': count,
                'Percentage': (count / total_results * 100) if total_results > 0 else 0
            })
        
        return pd.DataFrame(source_data)
    
    def _create_location_breakdown(self, results: List[ScrapingResult]) -> pd.DataFrame:
        """Create location breakdown DataFrame."""
        location_counts = {}
        total_results = len(results)
        
        for result in results:
            location = result.location or 'Unknown'
            location_counts[location] = location_counts.get(location, 0) + 1
        
        location_data = []
        for location, count in sorted(location_counts.items()):
            location_data.append({
                'Location': location,
                'Count': count,
                'Percentage': (count / total_results * 100) if total_results > 0 else 0
            })
        
        return pd.DataFrame(location_data)
    
    def _format_worksheets(self, writer):
        """Format Excel worksheets for better readability."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
            
            # Format main sheet
            ws_main = writer.sheets['Business Owners']
            
            # Header formatting
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in ws_main[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")
            
            # Auto-adjust column widths
            for column in ws_main.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                ws_main.column_dimensions[column_letter].width = adjusted_width
            
            # Format summary sheet
            ws_summary = writer.sheets['Summary']
            for cell in ws_summary[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")
            
            # Format percentage columns
            for row in ws_summary.iter_rows(min_row=2, max_col=3, max_row=ws_summary.max_row):
                if len(row) > 2:  # Percentage column
                    row[2].number_format = '0.00%'
            
        except ImportError:
            self.logger.warning("openpyxl not available for advanced formatting")
        except Exception as e:
            self.logger.warning(f"Error formatting Excel file: {e}")
    
    def export_finder_compatible(self, results: List[ScrapingResult], filename: str = None) -> str:
        """Export results in a format compatible with the finder application."""
        if not results:
            return ""
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"finder_import_{timestamp}.xlsx"
        
        output_dir = self.config.get('general', {}).get('output_directory', './results')
        os.makedirs(output_dir, exist_ok=True)
        filepath = os.path.join(output_dir, filename)
        
        try:
            # Create finder-compatible format
            finder_data = []
            
            for result in results:
                finder_row = {
                    'Owner Name': result.owner_name or '',
                    'Business Name': result.business_name or '',
                    'Business Type': result.business_type or '',
                    'Location': result.location or '',
                    'Phone': result.phone or '',
                    'Email': result.email or '',
                    'Address': result.address or '',
                    'Source': result.source or '',
                    'Source URL': result.url or '',
                    'Date Scraped': result.scraped_at.strftime("%Y-%m-%d") if result.scraped_at else '',
                    'Confidence': result.raw_data.get('confidence_score', 0) if result.raw_data else 0,
                    'Quality': result.raw_data.get('data_quality', 'unknown') if result.raw_data else 'unknown'
                }
                finder_data.append(finder_row)
            
            df = pd.DataFrame(finder_data)
            df.to_excel(filepath, index=False, engine='openpyxl')
            
            self.logger.info(f"Exported finder-compatible file to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error exporting finder-compatible file: {e}")
            return ""
