#!/usr/bin/env python3
"""
Playwright API Client - Uses real browser context to make API calls.
This bypasses Cloudflare and anti-bot protection by using a real browser.
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON>, BrowserContext
import time

logger = logging.getLogger(__name__)

class PlaywrightAPIClient:
    """Make API calls using real browser context to bypass anti-bot protection."""
    
    def __init__(self, headless: bool = True, browser_type: str = "chromium"):
        self.headless = headless
        self.browser_type = browser_type
        self.browser = None
        self.context = None
        self.page = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def start(self):
        """Start the browser and create context."""
        self.playwright = await async_playwright().start()
        
        if self.browser_type == "chromium":
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--allow-running-insecure-content'
                ]
            )
        elif self.browser_type == "firefox":
            self.browser = await self.playwright.firefox.launch(headless=self.headless)
        else:
            self.browser = await self.playwright.webkit.launch(headless=self.headless)
        
        # Create stealth context
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York'
        )
        
        # Add stealth scripts
        await self.context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        """)
        
        self.page = await self.context.new_page()
        logger.info("Playwright browser started successfully")
    
    async def close(self):
        """Close the browser."""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    async def truthfinder_search(self, first_name: str, last_name: str, city: str = "", state: str = "") -> List[Dict[str, Any]]:
        """
        Search TruthFinder using real browser interaction.

        Args:
            first_name: First name to search
            last_name: Last name to search
            city: City (optional)
            state: State (optional)

        Returns:
            List of search results
        """
        logger.info(f"Searching TruthFinder for: {first_name} {last_name} in {city}, {state}")

        try:
            # Navigate to TruthFinder
            await self.page.goto("https://www.truthfinder.com", wait_until="networkidle")
            await self.page.wait_for_timeout(3000)

            # Handle the warning modal/popup
            await self._handle_truthfinder_modals()

            # Wait a bit more for page to stabilize
            await self.page.wait_for_timeout(2000)

            # Find and fill search form with multiple selector strategies
            await self._fill_truthfinder_form(first_name, last_name, city, state)
            
            # Set up response interception to capture API calls
            api_results = []

            async def handle_response(response):
                if any(domain in response.url.lower() for domain in ['api.truthfinder.com', 'truthfinder.com/api', 'search']):
                    try:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '')
                            if 'json' in content_type:
                                data = await response.json()
                                api_results.append({
                                    'url': response.url,
                                    'data': data,
                                    'timestamp': datetime.now().isoformat(),
                                    'source': 'api_response'
                                })
                                logger.info(f"Captured API response from: {response.url}")
                    except Exception as e:
                        logger.debug(f"Error processing response from {response.url}: {e}")

            self.page.on('response', handle_response)

            # Submit search with multiple button selectors
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Search")',
                'button:has-text("SEARCH")',
                '.search-button',
                '.search-btn',
                'button[class*="search"]',
                'form button',
                '.submit-btn'
            ]

            search_submitted = False
            for selector in submit_selectors:
                try:
                    submit_button = await self.page.query_selector(selector)
                    if submit_button:
                        await submit_button.click()
                        logger.info(f"Submitted search with selector: {selector}")
                        search_submitted = True
                        break
                except:
                    continue

            if not search_submitted:
                # Try pressing Enter on the last name field
                try:
                    last_name_input = await self.page.query_selector('input[name="lastName"], input[placeholder*="Last" i]')
                    if last_name_input:
                        await last_name_input.press('Enter')
                        logger.info("Submitted search by pressing Enter")
                        search_submitted = True
                except:
                    pass

            if search_submitted:
                # Wait for results to load
                await self.page.wait_for_timeout(8000)  # Longer wait for search results

                # Try to find results on the page
                page_results = await self._extract_truthfinder_results(first_name, last_name)

                return api_results + page_results
            else:
                logger.warning("Could not submit TruthFinder search form")
                return []
            
        except Exception as e:
            logger.error(f"TruthFinder search failed: {e}")
            return []
    
    async def manta_search(self, business_type: str, location: str) -> List[Dict[str, Any]]:
        """
        Search Manta using real browser interaction.
        
        Args:
            business_type: Type of business to search
            location: Location to search
            
        Returns:
            List of business results
        """
        logger.info(f"Searching Manta for: {business_type} in {location}")
        
        try:
            # Navigate to Manta
            await self.page.goto("https://www.manta.com", wait_until="networkidle")
            await self.page.wait_for_timeout(3000)

            # Handle any popups/modals
            await self._handle_common_modals()

            # Search for businesses with robust form handling
            search_query = f"{business_type} {location}"
            await self._fill_manta_search_form(search_query)
            
            # Wait for results
            await self.page.wait_for_timeout(3000)
            
            # Extract business listings
            businesses = []
            business_elements = await self.page.query_selector_all('.business-card, .listing-item, .company-listing, [data-business-id]')
            
            for element in business_elements[:10]:  # Limit to first 10
                try:
                    business_data = await self._extract_business_element(element)
                    if business_data:
                        businesses.append(business_data)
                except Exception as e:
                    logger.debug(f"Error extracting business element: {e}")
                    continue
            
            # Try to load more results by clicking pagination or "Load More"
            try:
                load_more = await self.page.query_selector('button:has-text("Load More"), .load-more, .pagination a:last-child')
                if load_more:
                    await load_more.click()
                    await self.page.wait_for_timeout(2000)
                    
                    # Extract additional results
                    additional_elements = await self.page.query_selector_all('.business-card, .listing-item, .company-listing')
                    for element in additional_elements[len(businesses):len(businesses)+10]:
                        try:
                            business_data = await self._extract_business_element(element)
                            if business_data:
                                businesses.append(business_data)
                        except:
                            continue
            except:
                pass
            
            logger.info(f"Extracted {len(businesses)} businesses from Manta")
            return businesses
            
        except Exception as e:
            logger.error(f"Manta search failed: {e}")
            return []

    async def _handle_truthfinder_modals(self):
        """Handle TruthFinder warning modals and popups."""
        try:
            # Wait for potential modal to appear
            await self.page.wait_for_timeout(2000)

            # Handle the main warning modal
            warning_modal = await self.page.query_selector('#warning-modal, .modal, [class*="warning"], [class*="disclaimer"]')
            if warning_modal:
                logger.info("Found TruthFinder warning modal, accepting terms...")

                # Look for "I AGREE" button with multiple selectors
                agree_selectors = [
                    'button:has-text("I AGREE")',
                    'button:has-text("I Agree")',
                    'button:has-text("AGREE")',
                    'button:has-text("Agree")',
                    'button.green',
                    '.secure button',
                    '#warning-modal button',
                    '.modal button.green',
                    'button[class*="agree"]',
                    'input[type="submit"][value*="agree" i]'
                ]

                for selector in agree_selectors:
                    try:
                        agree_button = await self.page.query_selector(selector)
                        if agree_button:
                            await agree_button.click()
                            logger.info(f"Clicked agree button with selector: {selector}")
                            await self.page.wait_for_timeout(2000)
                            break
                    except:
                        continue

            # Handle cookie consent if present
            cookie_selectors = [
                'button:has-text("Accept")',
                'button:has-text("Accept All")',
                'button:has-text("OK")',
                '#cookie-consent button',
                '.cookie-banner button',
                '[class*="cookie"] button'
            ]

            for selector in cookie_selectors:
                try:
                    cookie_button = await self.page.query_selector(selector)
                    if cookie_button:
                        await cookie_button.click()
                        logger.info(f"Accepted cookies with selector: {selector}")
                        await self.page.wait_for_timeout(1000)
                        break
                except:
                    continue

            # Handle any other popups/overlays
            popup_selectors = [
                '.popup .close',
                '.overlay .close',
                '.modal .close',
                'button:has-text("Close")',
                'button:has-text("×")',
                '[class*="close"]'
            ]

            for selector in popup_selectors:
                try:
                    close_button = await self.page.query_selector(selector)
                    if close_button:
                        await close_button.click()
                        logger.info(f"Closed popup with selector: {selector}")
                        await self.page.wait_for_timeout(1000)
                except:
                    continue

        except Exception as e:
            logger.debug(f"Error handling TruthFinder modals: {e}")

    async def _fill_truthfinder_form(self, first_name: str, last_name: str, city: str = "", state: str = ""):
        """Fill TruthFinder search form with robust selector strategies."""
        try:
            # First name - try multiple selectors
            first_name_selectors = [
                'input[name="firstName"]',
                'input[placeholder*="First" i]',
                'input[placeholder*="first" i]',
                '#firstName',
                '#first-name',
                '.first-name input',
                'input[id*="first" i]',
                'input[class*="first" i]'
            ]

            for selector in first_name_selectors:
                try:
                    first_input = await self.page.query_selector(selector)
                    if first_input:
                        await first_input.fill(first_name)
                        logger.info(f"Filled first name with selector: {selector}")
                        break
                except:
                    continue

            # Last name - try multiple selectors
            last_name_selectors = [
                'input[name="lastName"]',
                'input[placeholder*="Last" i]',
                'input[placeholder*="last" i]',
                '#lastName',
                '#last-name',
                '.last-name input',
                'input[id*="last" i]',
                'input[class*="last" i]'
            ]

            for selector in last_name_selectors:
                try:
                    last_input = await self.page.query_selector(selector)
                    if last_input:
                        await last_input.fill(last_name)
                        logger.info(f"Filled last name with selector: {selector}")
                        break
                except:
                    continue

            # City (optional)
            if city:
                city_selectors = [
                    'input[name="city"]',
                    'input[placeholder*="City" i]',
                    'input[placeholder*="city" i]',
                    '#city',
                    '.city input',
                    'input[id*="city" i]'
                ]

                for selector in city_selectors:
                    try:
                        city_input = await self.page.query_selector(selector)
                        if city_input:
                            await city_input.fill(city)
                            logger.info(f"Filled city with selector: {selector}")
                            break
                    except:
                        continue

            # State (optional)
            if state:
                state_selectors = [
                    'input[name="state"]',
                    'select[name="state"]',
                    'input[placeholder*="State" i]',
                    '#state',
                    '.state input',
                    '.state select',
                    'input[id*="state" i]',
                    'select[id*="state" i]'
                ]

                for selector in state_selectors:
                    try:
                        state_input = await self.page.query_selector(selector)
                        if state_input:
                            tag_name = await state_input.evaluate('el => el.tagName.toLowerCase()')
                            if tag_name == 'select':
                                await state_input.select_option(state)
                            else:
                                await state_input.fill(state)
                            logger.info(f"Filled state with selector: {selector}")
                            break
                    except:
                        continue

        except Exception as e:
            logger.error(f"Error filling TruthFinder form: {e}")

    async def _extract_truthfinder_results(self, first_name: str = "", last_name: str = "") -> List[Dict[str, Any]]:
        """Extract TruthFinder search results from the page."""
        results = []

        try:
            # Wait for results to appear
            await self.page.wait_for_timeout(3000)

            # Multiple selectors for result elements
            result_selectors = [
                '.result',
                '.person-result',
                '.search-result',
                '[data-person-id]',
                '.person-card',
                '.result-item',
                '.person-item',
                '[class*="result"]',
                '[class*="person"]'
            ]

            result_elements = []
            for selector in result_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    result_elements = elements
                    logger.info(f"Found {len(elements)} results with selector: {selector}")
                    break

            if not result_elements:
                # Try to find any clickable person links
                result_elements = await self.page.query_selector_all('a[href*="person"], a[href*="profile"], .clickable')
                logger.info(f"Found {len(result_elements)} clickable elements as fallback")

            for element in result_elements[:10]:  # Limit to first 10 results
                try:
                    result_data = {}

                    # Extract name with multiple strategies
                    name_selectors = [
                        '.name', '.person-name', '.full-name', 'h2', 'h3', 'h4',
                        '[class*="name"]', '.title', '.person-title'
                    ]

                    for selector in name_selectors:
                        name_elem = await element.query_selector(selector)
                        if name_elem:
                            name_text = await name_elem.text_content()
                            if name_text and name_text.strip():
                                result_data['name'] = name_text.strip()
                                break

                    # Extract age
                    age_selectors = [
                        '.age', '[class*="age"]', '.years-old', '.person-age'
                    ]

                    for selector in age_selectors:
                        age_elem = await element.query_selector(selector)
                        if age_elem:
                            age_text = await age_elem.text_content()
                            if age_text and any(char.isdigit() for char in age_text):
                                result_data['age'] = age_text.strip()
                                break

                    # Extract location/address
                    location_selectors = [
                        '.location', '.address', '.city-state', '.person-location',
                        '[class*="location"]', '[class*="address"]', '.geo'
                    ]

                    for selector in location_selectors:
                        location_elem = await element.query_selector(selector)
                        if location_elem:
                            location_text = await location_elem.text_content()
                            if location_text and location_text.strip():
                                result_data['location'] = location_text.strip()
                                break

                    # Extract phone if visible
                    phone_selectors = [
                        '.phone', '.tel', '[href^="tel:"]', '.phone-number',
                        '[class*="phone"]', '.contact-phone'
                    ]

                    for selector in phone_selectors:
                        phone_elem = await element.query_selector(selector)
                        if phone_elem:
                            phone_text = await phone_elem.text_content()
                            if phone_text and any(char.isdigit() for char in phone_text):
                                result_data['phone'] = phone_text.strip()
                                break

                    # Extract email if visible
                    email_selectors = [
                        '.email', '[href^="mailto:"]', '.email-address',
                        '[class*="email"]', '.contact-email'
                    ]

                    for selector in email_selectors:
                        email_elem = await element.query_selector(selector)
                        if email_elem:
                            email_text = await email_elem.text_content()
                            if email_text and '@' in email_text:
                                result_data['email'] = email_text.strip()
                                break

                    # Extract profile URL if available
                    profile_link = await element.query_selector('a[href*="person"], a[href*="profile"]')
                    if profile_link:
                        href = await profile_link.get_attribute('href')
                        if href:
                            result_data['profile_url'] = href if href.startswith('http') else f"https://www.truthfinder.com{href}"

                    # Add metadata
                    result_data['source'] = 'truthfinder_page'
                    result_data['timestamp'] = datetime.now().isoformat()
                    result_data['search_terms'] = f"{first_name} {last_name}"

                    # Only add if we have at least a name
                    if result_data.get('name'):
                        results.append(result_data)

                except Exception as e:
                    logger.debug(f"Error extracting result element: {e}")
                    continue

            logger.info(f"Extracted {len(results)} results from TruthFinder page")

        except Exception as e:
            logger.error(f"Error extracting TruthFinder results: {e}")

        return results

    async def _handle_common_modals(self):
        """Handle common website modals (cookies, age verification, etc.)."""
        try:
            # Cookie consent
            cookie_selectors = [
                'button:has-text("Accept")',
                'button:has-text("Accept All")',
                'button:has-text("OK")',
                'button:has-text("I Agree")',
                '#cookie-consent button',
                '.cookie-banner button',
                '[class*="cookie"] button',
                '.gdpr-banner button'
            ]

            for selector in cookie_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        await button.click()
                        logger.info(f"Accepted cookies with selector: {selector}")
                        await self.page.wait_for_timeout(1000)
                        break
                except:
                    continue

            # Age verification
            age_selectors = [
                'button:has-text("Yes")',
                'button:has-text("I am 18")',
                'button:has-text("Continue")',
                '.age-verification button',
                '#age-modal button'
            ]

            for selector in age_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        await button.click()
                        logger.info(f"Passed age verification with selector: {selector}")
                        await self.page.wait_for_timeout(1000)
                        break
                except:
                    continue

            # Generic close buttons
            close_selectors = [
                '.popup .close',
                '.modal .close',
                '.overlay .close',
                'button:has-text("Close")',
                'button:has-text("×")',
                '[aria-label="Close"]'
            ]

            for selector in close_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        await button.click()
                        logger.info(f"Closed modal with selector: {selector}")
                        await self.page.wait_for_timeout(1000)
                except:
                    continue

        except Exception as e:
            logger.debug(f"Error handling common modals: {e}")

    async def _fill_manta_search_form(self, search_query: str):
        """Fill Manta search form with robust selectors."""
        try:
            # Search input selectors
            search_selectors = [
                'input[name="search"]',
                'input[placeholder*="Search" i]',
                'input[placeholder*="business" i]',
                '#search',
                '#search-input',
                '.search-input',
                'input[type="search"]',
                'input[class*="search"]'
            ]

            search_filled = False
            for selector in search_selectors:
                try:
                    search_input = await self.page.query_selector(selector)
                    if search_input:
                        await search_input.fill(search_query)
                        logger.info(f"Filled search with selector: {selector}")
                        search_filled = True
                        break
                except:
                    continue

            if search_filled:
                # Submit search
                submit_selectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:has-text("Search")',
                    '.search-button',
                    '.search-btn',
                    'form button'
                ]

                for selector in submit_selectors:
                    try:
                        submit_button = await self.page.query_selector(selector)
                        if submit_button:
                            await submit_button.click()
                            logger.info(f"Submitted search with selector: {selector}")
                            return
                    except:
                        continue

                # Fallback: press Enter
                for selector in search_selectors:
                    try:
                        search_input = await self.page.query_selector(selector)
                        if search_input:
                            await search_input.press('Enter')
                            logger.info("Submitted search by pressing Enter")
                            return
                    except:
                        continue
            else:
                logger.warning("Could not find Manta search form")

        except Exception as e:
            logger.error(f"Error filling Manta search form: {e}")

    async def _extract_page_results(self) -> List[Dict[str, Any]]:
        """Extract results directly from the page content."""
        results = []
        
        try:
            # Look for result elements
            result_elements = await self.page.query_selector_all('.result, .person-result, .search-result, [data-person-id]')
            
            for element in result_elements:
                try:
                    result_data = {}
                    
                    # Extract name
                    name_elem = await element.query_selector('.name, .person-name, h2, h3')
                    if name_elem:
                        result_data['name'] = await name_elem.text_content()
                    
                    # Extract age
                    age_elem = await element.query_selector('.age, [class*="age"]')
                    if age_elem:
                        result_data['age'] = await age_elem.text_content()
                    
                    # Extract location
                    location_elem = await element.query_selector('.location, .address, [class*="location"]')
                    if location_elem:
                        result_data['location'] = await location_elem.text_content()
                    
                    # Extract any additional data
                    result_data['source'] = 'page_extraction'
                    result_data['timestamp'] = datetime.now().isoformat()
                    
                    if result_data.get('name'):
                        results.append(result_data)
                        
                except Exception as e:
                    logger.debug(f"Error extracting result element: {e}")
                    continue
            
        except Exception as e:
            logger.debug(f"Error extracting page results: {e}")
        
        return results
    
    async def _extract_business_element(self, element) -> Optional[Dict[str, Any]]:
        """Extract business data from a business element."""
        try:
            business_data = {}
            
            # Business name
            name_elem = await element.query_selector('.business-name, .company-name, .listing-title, h2, h3')
            if name_elem:
                business_data['business_name'] = await name_elem.text_content()
            
            # Owner/contact name
            owner_elem = await element.query_selector('.owner-name, .contact-name, .manager-name')
            if owner_elem:
                business_data['owner_name'] = await owner_elem.text_content()
            
            # Phone
            phone_elem = await element.query_selector('.phone, .tel, [href^="tel:"]')
            if phone_elem:
                phone_text = await phone_elem.text_content()
                business_data['phone'] = phone_text.strip()
            
            # Email
            email_elem = await element.query_selector('.email, [href^="mailto:"]')
            if email_elem:
                email_text = await email_elem.text_content()
                business_data['email'] = email_text.strip()
            
            # Address
            address_elem = await element.query_selector('.address, .location, .business-address')
            if address_elem:
                business_data['address'] = await address_elem.text_content()
            
            # Website
            website_elem = await element.query_selector('.website, .url, a[href*="http"]')
            if website_elem:
                href = await website_elem.get_attribute('href')
                if href and 'http' in href:
                    business_data['website'] = href
            
            # Business URL
            business_link = await element.query_selector('a[href*="/c/"], .business-link')
            if business_link:
                href = await business_link.get_attribute('href')
                if href:
                    business_data['business_url'] = href if href.startswith('http') else f"https://www.manta.com{href}"
            
            business_data['source'] = 'manta_browser'
            business_data['timestamp'] = datetime.now().isoformat()
            
            return business_data if business_data.get('business_name') else None
            
        except Exception as e:
            logger.debug(f"Error extracting business element: {e}")
            return None

# Async wrapper functions for easy integration
async def search_truthfinder_async(first_name: str, last_name: str, city: str = "", state: str = "") -> List[Dict[str, Any]]:
    """Async wrapper for TruthFinder search."""
    async with PlaywrightAPIClient(headless=True) as client:
        return await client.truthfinder_search(first_name, last_name, city, state)

async def search_manta_async(business_type: str, location: str) -> List[Dict[str, Any]]:
    """Async wrapper for Manta search."""
    async with PlaywrightAPIClient(headless=True) as client:
        return await client.manta_search(business_type, location)

# Sync wrapper functions for integration with existing code
def search_truthfinder(first_name: str, last_name: str, city: str = "", state: str = "") -> List[Dict[str, Any]]:
    """Sync wrapper for TruthFinder search."""
    return asyncio.run(search_truthfinder_async(first_name, last_name, city, state))

def search_manta(business_type: str, location: str) -> List[Dict[str, Any]]:
    """Sync wrapper for Manta search."""
    return asyncio.run(search_manta_async(business_type, location))
