"""
Fallback system for handling scraper failures and providing alternative data sources.
"""

import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import time


@dataclass
class FallbackRule:
    """Defines a fallback rule for a scraper."""
    primary_scraper: str
    fallback_scrapers: List[str]
    trigger_conditions: Dict[str, Any]
    cooldown_minutes: int = 30
    max_attempts: int = 3


class FallbackSystem:
    """Comprehensive fallback system for scraper failures."""
    
    def __init__(self, config: Dict):
        """Initialize the fallback system."""
        self.config = config.get('fallback_system', {})
        self.enabled = self.config.get('enabled', True)
        
        self.logger = logging.getLogger(__name__)
        
        # Fallback rules
        self.fallback_rules: Dict[str, FallbackRule] = {}
        self._initialize_fallback_rules()
        
        # Tracking
        self.fallback_attempts: Dict[str, List[datetime]] = {}
        self.scraper_cooldowns: Dict[str, datetime] = {}
        
        if self.enabled:
            self.logger.info("Fallback system initialized")
        else:
            self.logger.info("Fallback system disabled")
    
    def _initialize_fallback_rules(self):
        """Initialize default fallback rules."""
        # Default fallback chains
        default_rules = [
            FallbackRule(
                primary_scraper="bbb",
                fallback_scrapers=["manta", "truthfinder_api"],
                trigger_conditions={
                    "error_rate": 0.7,
                    "consecutive_failures": 3,
                    "response_time": 30.0
                },
                cooldown_minutes=15,
                max_attempts=2
            ),
            FallbackRule(
                primary_scraper="manta", 
                fallback_scrapers=["bbb", "truthfinder_api"],
                trigger_conditions={
                    "error_rate": 0.8,
                    "consecutive_failures": 5,
                    "response_time": 45.0
                },
                cooldown_minutes=20,
                max_attempts=2
            ),
            FallbackRule(
                primary_scraper="linkedin",
                fallback_scrapers=["truthfinder_api", "bbb"],
                trigger_conditions={
                    "error_rate": 0.5,  # LinkedIn fails frequently
                    "consecutive_failures": 2,
                    "response_time": 20.0
                },
                cooldown_minutes=60,  # Longer cooldown for LinkedIn
                max_attempts=1
            ),
            FallbackRule(
                primary_scraper="truepeoplesearch",
                fallback_scrapers=["truthfinder_api", "cyberbackgroundchecks"],
                trigger_conditions={
                    "error_rate": 0.6,
                    "consecutive_failures": 3,
                    "response_time": 25.0
                },
                cooldown_minutes=30,
                max_attempts=2
            )
        ]
        
        # Load custom rules from config
        custom_rules = self.config.get('rules', [])
        
        for rule_data in default_rules + custom_rules:
            if isinstance(rule_data, dict):
                rule = FallbackRule(**rule_data)
            else:
                rule = rule_data
            
            self.fallback_rules[rule.primary_scraper] = rule
            self.logger.debug(f"Loaded fallback rule for {rule.primary_scraper}")
    
    def should_use_fallback(self, scraper_name: str, metrics: Dict[str, Any]) -> bool:
        """Determine if fallback should be triggered for a scraper."""
        if not self.enabled:
            return False
        
        if scraper_name not in self.fallback_rules:
            return False
        
        rule = self.fallback_rules[scraper_name]
        conditions = rule.trigger_conditions
        
        # Check if scraper is in cooldown
        if self._is_in_cooldown(scraper_name):
            return False
        
        # Check error rate
        if metrics.get('error_rate', 0) >= conditions.get('error_rate', 1.0):
            self.logger.info(f"Fallback triggered for {scraper_name}: high error rate")
            return True
        
        # Check consecutive failures
        consecutive_failures = metrics.get('consecutive_failures', 0)
        if consecutive_failures >= conditions.get('consecutive_failures', 999):
            self.logger.info(f"Fallback triggered for {scraper_name}: consecutive failures")
            return True
        
        # Check response time
        response_time = metrics.get('average_response_time', 0)
        if response_time >= conditions.get('response_time', 999):
            self.logger.info(f"Fallback triggered for {scraper_name}: slow response time")
            return True
        
        return False
    
    def get_fallback_scrapers(self, primary_scraper: str, available_scrapers: List[str]) -> List[str]:
        """Get ordered list of fallback scrapers for a primary scraper."""
        if not self.enabled or primary_scraper not in self.fallback_rules:
            return []
        
        rule = self.fallback_rules[primary_scraper]
        
        # Filter fallback scrapers to only include available ones
        fallback_scrapers = [
            scraper for scraper in rule.fallback_scrapers 
            if scraper in available_scrapers and not self._is_in_cooldown(scraper)
        ]
        
        # Check attempt limits
        attempts_today = len([
            attempt for attempt in self.fallback_attempts.get(primary_scraper, [])
            if attempt > datetime.now() - timedelta(days=1)
        ])
        
        if attempts_today >= rule.max_attempts:
            self.logger.warning(f"Max fallback attempts reached for {primary_scraper} today")
            return []
        
        return fallback_scrapers
    
    def record_fallback_attempt(self, primary_scraper: str, fallback_scraper: str, success: bool):
        """Record a fallback attempt."""
        if not self.enabled:
            return
        
        now = datetime.now()
        
        # Record attempt
        if primary_scraper not in self.fallback_attempts:
            self.fallback_attempts[primary_scraper] = []
        
        self.fallback_attempts[primary_scraper].append(now)
        
        # Set cooldown if fallback failed
        if not success and primary_scraper in self.fallback_rules:
            rule = self.fallback_rules[primary_scraper]
            cooldown_until = now + timedelta(minutes=rule.cooldown_minutes)
            self.scraper_cooldowns[fallback_scraper] = cooldown_until
            
            self.logger.info(f"Fallback failed: {fallback_scraper} in cooldown until {cooldown_until}")
        else:
            self.logger.info(f"Fallback successful: {primary_scraper} -> {fallback_scraper}")
    
    def _is_in_cooldown(self, scraper_name: str) -> bool:
        """Check if a scraper is in cooldown period."""
        if scraper_name not in self.scraper_cooldowns:
            return False
        
        cooldown_until = self.scraper_cooldowns[scraper_name]
        if datetime.now() > cooldown_until:
            # Cooldown expired, remove it
            del self.scraper_cooldowns[scraper_name]
            return False
        
        return True
    
    def get_fallback_status(self) -> Dict[str, Any]:
        """Get current fallback system status."""
        status = {
            'enabled': self.enabled,
            'rules_count': len(self.fallback_rules),
            'active_cooldowns': {},
            'recent_attempts': {},
            'rules': {}
        }
        
        # Active cooldowns
        now = datetime.now()
        for scraper, cooldown_until in self.scraper_cooldowns.items():
            if cooldown_until > now:
                status['active_cooldowns'][scraper] = {
                    'cooldown_until': cooldown_until.isoformat(),
                    'minutes_remaining': (cooldown_until - now).total_seconds() / 60
                }
        
        # Recent attempts (last 24 hours)
        cutoff = now - timedelta(days=1)
        for scraper, attempts in self.fallback_attempts.items():
            recent_attempts = [a for a in attempts if a > cutoff]
            if recent_attempts:
                status['recent_attempts'][scraper] = {
                    'count': len(recent_attempts),
                    'last_attempt': max(recent_attempts).isoformat()
                }
        
        # Rules summary
        for scraper, rule in self.fallback_rules.items():
            status['rules'][scraper] = {
                'fallback_scrapers': rule.fallback_scrapers,
                'trigger_conditions': rule.trigger_conditions,
                'cooldown_minutes': rule.cooldown_minutes,
                'max_attempts': rule.max_attempts
            }
        
        return status


class HealthChecker:
    """Performs health checks on scrapers and external services."""
    
    def __init__(self, config: Dict):
        """Initialize the health checker."""
        self.config = config.get('health_checker', {})
        self.enabled = self.config.get('enabled', True)
        self.check_interval = self.config.get('check_interval', 300)  # 5 minutes
        
        self.logger = logging.getLogger(__name__)
        
        # Health check functions
        self.health_checks: Dict[str, Callable] = {}
        
        if self.enabled:
            self.logger.info("Health checker initialized")
    
    def register_health_check(self, name: str, check_function: Callable) -> None:
        """Register a health check function."""
        self.health_checks[name] = check_function
        self.logger.debug(f"Registered health check: {name}")
    
    def run_health_checks(self) -> Dict[str, Any]:
        """Run all registered health checks."""
        if not self.enabled:
            return {}
        
        results = {}
        
        for name, check_function in self.health_checks.items():
            try:
                start_time = time.time()
                result = check_function()
                duration = time.time() - start_time
                
                results[name] = {
                    'status': 'healthy' if result else 'unhealthy',
                    'duration': duration,
                    'timestamp': datetime.now().isoformat(),
                    'details': result if isinstance(result, dict) else {}
                }
                
            except Exception as e:
                results[name] = {
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                self.logger.error(f"Health check failed for {name}: {e}")
        
        return results
    
    def check_scraper_health(self, scraper) -> bool:
        """Check if a scraper is healthy."""
        try:
            # Basic checks
            if not scraper.is_enabled():
                return False
            
            # Check if scraper can make a simple request
            if hasattr(scraper, 'test_connection'):
                return scraper.test_connection()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Scraper health check failed: {e}")
            return False
    
    def check_api_health(self, api_client) -> Dict[str, Any]:
        """Check API health (for TruthFinder, Google Custom Search, etc.)."""
        try:
            if hasattr(api_client, 'test_api_connection'):
                success = api_client.test_api_connection()
                
                status = api_client.get_api_status() if hasattr(api_client, 'get_api_status') else {}
                
                return {
                    'connection': success,
                    'status': status
                }
            
            return {'connection': True, 'status': 'unknown'}
            
        except Exception as e:
            self.logger.error(f"API health check failed: {e}")
            return {'connection': False, 'error': str(e)}


class AlertSystem:
    """Simple alerting system for critical issues."""
    
    def __init__(self, config: Dict):
        """Initialize the alert system."""
        self.config = config.get('alerts', {})
        self.enabled = self.config.get('enabled', True)
        self.alert_channels = self.config.get('channels', ['log'])
        
        self.logger = logging.getLogger(__name__)
        
        if self.enabled:
            self.logger.info("Alert system initialized")
    
    def send_alert(self, alert_type: str, message: str, severity: str = 'warning'):
        """Send an alert through configured channels."""
        if not self.enabled:
            return
        
        alert_data = {
            'type': alert_type,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        }
        
        for channel in self.alert_channels:
            try:
                if channel == 'log':
                    self._send_log_alert(alert_data)
                elif channel == 'file':
                    self._send_file_alert(alert_data)
                # Add more channels as needed (email, webhook, etc.)
                
            except Exception as e:
                self.logger.error(f"Failed to send alert via {channel}: {e}")
    
    def _send_log_alert(self, alert_data: Dict[str, Any]):
        """Send alert to log."""
        severity = alert_data['severity']
        message = f"ALERT [{severity.upper()}] {alert_data['type']}: {alert_data['message']}"
        
        if severity == 'critical':
            self.logger.critical(message)
        elif severity == 'warning':
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def _send_file_alert(self, alert_data: Dict[str, Any]):
        """Send alert to file."""
        alert_file = self.config.get('alert_file', 'alerts.log')
        
        try:
            with open(alert_file, 'a') as f:
                f.write(f"{alert_data['timestamp']} - {alert_data['severity'].upper()} - "
                       f"{alert_data['type']}: {alert_data['message']}\n")
        except Exception as e:
            self.logger.error(f"Failed to write alert to file: {e}")
