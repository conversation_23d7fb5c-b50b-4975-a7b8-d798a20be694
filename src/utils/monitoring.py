"""
Monitoring and health check utilities for the Business Owner Scraper.
Provides real-time monitoring, performance tracking, and alerting.
"""

import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import threading
from collections import defaultdict, deque


@dataclass
class ScraperMetrics:
    """Metrics for a single scraper."""
    name: str
    enabled: bool
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    blocked_requests: int = 0
    average_response_time: float = 0.0
    last_success: Optional[datetime] = None
    last_failure: Optional[datetime] = None
    error_rate: float = 0.0
    success_rate: float = 0.0
    status: str = "unknown"  # healthy, degraded, failed, disabled


@dataclass
class SystemMetrics:
    """Overall system metrics."""
    total_scrapers: int = 0
    active_scrapers: int = 0
    healthy_scrapers: int = 0
    degraded_scrapers: int = 0
    failed_scrapers: int = 0
    overall_success_rate: float = 0.0
    total_results_collected: int = 0
    uptime: float = 0.0
    last_updated: Optional[datetime] = None


class ScraperMonitor:
    """Real-time monitoring system for scrapers."""
    
    def __init__(self, config: Dict):
        """Initialize the monitoring system."""
        self.config = config.get('monitoring', {})
        self.enabled = self.config.get('enabled', True)
        self.metrics_retention_hours = self.config.get('metrics_retention_hours', 24)
        self.health_check_interval = self.config.get('health_check_interval', 300)  # 5 minutes
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'error_rate': 0.5,  # 50% error rate triggers alert
            'success_rate': 0.3,  # Below 30% success rate triggers alert
            'response_time': 30.0  # Above 30s response time triggers alert
        })
        
        self.logger = logging.getLogger(__name__)
        self.start_time = datetime.now()
        
        # Metrics storage
        self.scraper_metrics: Dict[str, ScraperMetrics] = {}
        self.system_metrics = SystemMetrics()
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.lock = threading.Lock()
        
        if self.enabled:
            self.logger.info("Scraper monitoring system initialized")
        else:
            self.logger.info("Scraper monitoring system disabled")
    
    def start_monitoring(self):
        """Start the monitoring system."""
        if not self.enabled:
            return
        
        if self.monitoring_active:
            self.logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Scraper monitoring started")
    
    def stop_monitoring(self):
        """Stop the monitoring system."""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Scraper monitoring stopped")
    
    def register_scraper(self, scraper_name: str, enabled: bool = True):
        """Register a scraper for monitoring."""
        with self.lock:
            if scraper_name not in self.scraper_metrics:
                self.scraper_metrics[scraper_name] = ScraperMetrics(
                    name=scraper_name,
                    enabled=enabled,
                    status="healthy" if enabled else "disabled"
                )
                self.logger.info(f"Registered scraper for monitoring: {scraper_name}")
    
    def record_request(self, scraper_name: str, success: bool, response_time: float, 
                      blocked: bool = False, error: Optional[str] = None):
        """Record a scraper request result."""
        if not self.enabled:
            return
        
        with self.lock:
            if scraper_name not in self.scraper_metrics:
                self.register_scraper(scraper_name)
            
            metrics = self.scraper_metrics[scraper_name]
            metrics.total_requests += 1
            
            if success:
                metrics.successful_requests += 1
                metrics.last_success = datetime.now()
            else:
                metrics.failed_requests += 1
                metrics.last_failure = datetime.now()
            
            if blocked:
                metrics.blocked_requests += 1
            
            # Update response time (moving average)
            if metrics.total_requests == 1:
                metrics.average_response_time = response_time
            else:
                # Exponential moving average
                alpha = 0.1
                metrics.average_response_time = (
                    alpha * response_time + 
                    (1 - alpha) * metrics.average_response_time
                )
            
            # Calculate rates
            if metrics.total_requests > 0:
                metrics.success_rate = metrics.successful_requests / metrics.total_requests
                metrics.error_rate = metrics.failed_requests / metrics.total_requests
            
            # Update status
            metrics.status = self._calculate_scraper_status(metrics)
            
            # Store in history
            self.metrics_history[scraper_name].append({
                'timestamp': datetime.now(),
                'success': success,
                'response_time': response_time,
                'blocked': blocked,
                'error': error
            })
    
    def record_results(self, scraper_name: str, result_count: int):
        """Record the number of results collected."""
        if not self.enabled:
            return
        
        with self.lock:
            self.system_metrics.total_results_collected += result_count
    
    def get_scraper_status(self, scraper_name: str) -> Optional[ScraperMetrics]:
        """Get current status of a specific scraper."""
        with self.lock:
            return self.scraper_metrics.get(scraper_name)
    
    def get_system_status(self) -> SystemMetrics:
        """Get overall system status."""
        with self.lock:
            # Update system metrics
            self.system_metrics.total_scrapers = len(self.scraper_metrics)
            self.system_metrics.active_scrapers = sum(
                1 for m in self.scraper_metrics.values() if m.enabled
            )
            
            # Count by status
            status_counts = defaultdict(int)
            for metrics in self.scraper_metrics.values():
                if metrics.enabled:
                    status_counts[metrics.status] += 1
            
            self.system_metrics.healthy_scrapers = status_counts['healthy']
            self.system_metrics.degraded_scrapers = status_counts['degraded']
            self.system_metrics.failed_scrapers = status_counts['failed']
            
            # Calculate overall success rate
            total_requests = sum(m.total_requests for m in self.scraper_metrics.values())
            total_successful = sum(m.successful_requests for m in self.scraper_metrics.values())
            
            if total_requests > 0:
                self.system_metrics.overall_success_rate = total_successful / total_requests
            
            # Calculate uptime
            self.system_metrics.uptime = (datetime.now() - self.start_time).total_seconds()
            self.system_metrics.last_updated = datetime.now()
            
            return self.system_metrics
    
    def get_health_report(self) -> Dict[str, Any]:
        """Generate a comprehensive health report."""
        with self.lock:
            system_status = self.get_system_status()
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'system': asdict(system_status),
                'scrapers': {},
                'alerts': self._check_alerts(),
                'recommendations': self._generate_recommendations()
            }
            
            for name, metrics in self.scraper_metrics.items():
                report['scrapers'][name] = asdict(metrics)
            
            return report
    
    def _calculate_scraper_status(self, metrics: ScraperMetrics) -> str:
        """Calculate the health status of a scraper."""
        if not metrics.enabled:
            return "disabled"
        
        if metrics.total_requests == 0:
            return "unknown"
        
        # Check for critical failures
        if metrics.error_rate >= self.alert_thresholds['error_rate']:
            return "failed"
        
        # Check for degraded performance
        if (metrics.success_rate < self.alert_thresholds['success_rate'] or
            metrics.average_response_time > self.alert_thresholds['response_time']):
            return "degraded"
        
        return "healthy"
    
    def _check_alerts(self) -> List[Dict[str, Any]]:
        """Check for alert conditions."""
        alerts = []
        
        for name, metrics in self.scraper_metrics.items():
            if not metrics.enabled:
                continue
            
            # High error rate alert
            if metrics.error_rate >= self.alert_thresholds['error_rate']:
                alerts.append({
                    'type': 'high_error_rate',
                    'scraper': name,
                    'severity': 'critical',
                    'message': f"High error rate: {metrics.error_rate:.1%}",
                    'threshold': self.alert_thresholds['error_rate']
                })
            
            # Low success rate alert
            if metrics.success_rate < self.alert_thresholds['success_rate']:
                alerts.append({
                    'type': 'low_success_rate',
                    'scraper': name,
                    'severity': 'warning',
                    'message': f"Low success rate: {metrics.success_rate:.1%}",
                    'threshold': self.alert_thresholds['success_rate']
                })
            
            # High response time alert
            if metrics.average_response_time > self.alert_thresholds['response_time']:
                alerts.append({
                    'type': 'high_response_time',
                    'scraper': name,
                    'severity': 'warning',
                    'message': f"High response time: {metrics.average_response_time:.1f}s",
                    'threshold': self.alert_thresholds['response_time']
                })
        
        return alerts
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on current metrics."""
        recommendations = []
        
        system_status = self.get_system_status()
        
        # System-level recommendations
        if system_status.overall_success_rate < 0.5:
            recommendations.append("Overall success rate is low - consider reviewing scraper configurations")
        
        if system_status.failed_scrapers > 0:
            recommendations.append(f"{system_status.failed_scrapers} scrapers are failing - check logs for errors")
        
        # Scraper-specific recommendations
        for name, metrics in self.scraper_metrics.items():
            if not metrics.enabled:
                continue
            
            if metrics.blocked_requests > metrics.successful_requests:
                recommendations.append(f"{name}: High blocking rate - consider using proxies or reducing request rate")
            
            if metrics.average_response_time > 20:
                recommendations.append(f"{name}: Slow response times - check network connectivity or server load")
        
        return recommendations
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                # Perform health checks
                self._perform_health_checks()
                
                # Clean old metrics
                self._cleanup_old_metrics()
                
                # Sleep until next check
                time.sleep(self.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait a minute before retrying
    
    def _perform_health_checks(self):
        """Perform periodic health checks."""
        # This could include pinging scrapers, checking API quotas, etc.
        pass
    
    def _cleanup_old_metrics(self):
        """Clean up old metrics data."""
        cutoff_time = datetime.now() - timedelta(hours=self.metrics_retention_hours)
        
        for scraper_name, history in self.metrics_history.items():
            # Remove old entries
            while history and history[0]['timestamp'] < cutoff_time:
                history.popleft()
    
    def export_metrics(self, filepath: str):
        """Export metrics to a JSON file."""
        try:
            report = self.get_health_report()
            
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Metrics exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Failed to export metrics: {e}")


class FallbackManager:
    """Manages fallback mechanisms for failed scrapers."""
    
    def __init__(self, config: Dict):
        """Initialize the fallback manager."""
        self.config = config.get('fallback', {})
        self.enabled = self.config.get('enabled', True)
        self.fallback_chains = self.config.get('chains', {})
        
        self.logger = logging.getLogger(__name__)
        
        if self.enabled:
            self.logger.info("Fallback manager initialized")
    
    def get_fallback_scrapers(self, failed_scraper: str) -> List[str]:
        """Get fallback scrapers for a failed scraper."""
        if not self.enabled:
            return []
        
        return self.fallback_chains.get(failed_scraper, [])
    
    def should_use_fallback(self, scraper_name: str, error_rate: float) -> bool:
        """Determine if fallback should be used."""
        if not self.enabled:
            return False
        
        threshold = self.config.get('error_rate_threshold', 0.7)
        return error_rate >= threshold
