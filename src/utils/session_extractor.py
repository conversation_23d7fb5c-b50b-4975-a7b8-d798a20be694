#!/usr/bin/env python3
"""
Session Extractor - Uses <PERSON>wright to extract real cookies and headers from websites.
This tool helps capture fresh session data for internal APIs.
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON>, BrowserContext
import logging

logger = logging.getLogger(__name__)

class SessionExtractor:
    """Extract real browser sessions using <PERSON>wright for API authentication."""
    
    def __init__(self, headless: bool = False, browser_type: str = "chromium"):
        self.headless = headless
        self.browser_type = browser_type
        self.captured_requests = []
        self.captured_cookies = {}
        self.captured_headers = {}
        
    async def extract_truthfinder_session(self, search_name: str = "<PERSON>") -> Dict[str, Any]:
        """
        Extract TruthFinder session data by performing a real search.
        
        Args:
            search_name: Name to search for to trigger API calls
            
        Returns:
            Dictionary with cookies, headers, and API details
        """
        logger.info(f"Extracting TruthFinder session data for search: {search_name}")
        
        async with async_playwright() as p:
            # Launch browser with anti-detection settings
            browser = await self._launch_browser(p)
            context = await self._create_stealth_context(browser)
            page = await context.new_page()
            
            # Set up request/response interception
            await self._setup_network_interception(page, "truthfinder.com")
            
            try:
                # Navigate to TruthFinder
                logger.info("Navigating to TruthFinder...")
                await page.goto("https://www.truthfinder.com", wait_until="networkidle")
                await page.wait_for_timeout(3000)
                
                # Fill in search form
                logger.info(f"Performing search for: {search_name}")
                name_parts = search_name.split()
                first_name = name_parts[0] if name_parts else "John"
                last_name = name_parts[1] if len(name_parts) > 1 else "Smith"
                
                # Look for search form fields
                await page.fill('input[name="firstName"], input[placeholder*="First"], #firstName', first_name)
                await page.fill('input[name="lastName"], input[placeholder*="Last"], #lastName', last_name)
                
                # Submit search
                await page.click('button[type="submit"], .search-button, input[type="submit"]')
                
                # Wait for API calls to complete
                logger.info("Waiting for API calls...")
                await page.wait_for_timeout(5000)
                
                # Extract session data
                session_data = await self._extract_session_data(page, "truthfinder")
                
                logger.info(f"Captured {len(self.captured_requests)} API requests")
                return session_data
                
            except Exception as e:
                logger.error(f"Error extracting TruthFinder session: {e}")
                return {}
            finally:
                await browser.close()
    
    async def extract_manta_session(self, business_type: str = "restaurant", location: str = "houston") -> Dict[str, Any]:
        """
        Extract Manta session data by browsing business listings.
        
        Args:
            business_type: Type of business to search for
            location: Location to search in
            
        Returns:
            Dictionary with cookies, headers, and API details
        """
        logger.info(f"Extracting Manta session data for: {business_type} in {location}")
        
        async with async_playwright() as p:
            browser = await self._launch_browser(p)
            context = await self._create_stealth_context(browser)
            page = await context.new_page()
            
            await self._setup_network_interception(page, "manta.com")
            
            try:
                # Navigate to Manta
                logger.info("Navigating to Manta...")
                await page.goto("https://www.manta.com", wait_until="networkidle")
                await page.wait_for_timeout(3000)
                
                # Search for businesses
                logger.info(f"Searching for {business_type} in {location}")
                search_query = f"{business_type} {location}"
                
                # Fill search form
                await page.fill('input[name="search"], input[placeholder*="Search"], #search', search_query)
                await page.press('input[name="search"], input[placeholder*="Search"], #search', 'Enter')
                
                # Wait for results and browse
                await page.wait_for_timeout(5000)
                
                # Click on a few business listings to trigger more API calls
                business_links = await page.query_selector_all('a[href*="/c/"]')
                for i, link in enumerate(business_links[:3]):  # Click first 3 businesses
                    try:
                        await link.click()
                        await page.wait_for_timeout(2000)
                        await page.go_back()
                        await page.wait_for_timeout(1000)
                    except:
                        continue
                
                session_data = await self._extract_session_data(page, "manta")
                
                logger.info(f"Captured {len(self.captured_requests)} API requests")
                return session_data
                
            except Exception as e:
                logger.error(f"Error extracting Manta session: {e}")
                return {}
            finally:
                await browser.close()
    
    async def _launch_browser(self, playwright):
        """Launch browser with anti-detection settings."""
        if self.browser_type == "chromium":
            return await playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--disable-extensions',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--allow-running-insecure-content',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-default-apps',
                    '--disable-popup-blocking'
                ]
            )
        elif self.browser_type == "firefox":
            return await playwright.firefox.launch(headless=self.headless)
        else:
            return await playwright.webkit.launch(headless=self.headless)
    
    async def _create_stealth_context(self, browser) -> BrowserContext:
        """Create browser context with stealth settings."""
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York',
            permissions=['geolocation'],
            geolocation={'latitude': 40.7128, 'longitude': -74.0060},  # New York
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            }
        )
        
        # Add stealth scripts
        await context.add_init_script("""
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // Mock plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // Mock languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            // Mock permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)
        
        return context
    
    async def _setup_network_interception(self, page: Page, domain: str):
        """Set up network request/response interception."""
        
        async def handle_request(request):
            if domain in request.url:
                # Capture request details
                self.captured_requests.append({
                    'url': request.url,
                    'method': request.method,
                    'headers': dict(request.headers),
                    'post_data': request.post_data,
                    'timestamp': datetime.now().isoformat()
                })
        
        async def handle_response(response):
            if domain in response.url and 'api' in response.url.lower():
                # This is likely an API call
                try:
                    response_text = await response.text()
                    logger.info(f"Captured API response from: {response.url}")
                    logger.debug(f"Response status: {response.status}")
                    logger.debug(f"Response headers: {dict(response.headers)}")
                except:
                    pass
        
        page.on('request', handle_request)
        page.on('response', handle_response)
    
    async def _extract_session_data(self, page: Page, site_name: str) -> Dict[str, Any]:
        """Extract comprehensive session data from the page."""
        
        # Get cookies
        cookies = await page.context.cookies()
        cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        
        # Get local storage
        local_storage = await page.evaluate('() => Object.assign({}, window.localStorage)')
        
        # Get session storage
        session_storage = await page.evaluate('() => Object.assign({}, window.sessionStorage)')
        
        # Find API requests
        api_requests = [req for req in self.captured_requests if 'api' in req['url'].lower()]
        
        session_data = {
            'site': site_name,
            'timestamp': datetime.now().isoformat(),
            'cookies': cookie_dict,
            'local_storage': local_storage,
            'session_storage': session_storage,
            'api_requests': api_requests,
            'user_agent': await page.evaluate('() => navigator.userAgent'),
            'page_url': page.url
        }
        
        return session_data
    
    def save_session_data(self, session_data: Dict[str, Any], filename: str = None):
        """Save session data to JSON file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            site = session_data.get('site', 'unknown')
            filename = f"session_{site}_{timestamp}.json"
        
        filepath = f"sessions/{filename}"
        
        # Create sessions directory if it doesn't exist
        import os
        os.makedirs('sessions', exist_ok=True)
        
        with open(filepath, 'w') as f:
            json.dump(session_data, f, indent=2)
        
        logger.info(f"Session data saved to: {filepath}")
        return filepath
    
    def generate_api_config(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate API configuration from session data."""
        site = session_data.get('site', 'unknown')
        cookies = session_data.get('cookies', {})
        api_requests = session_data.get('api_requests', [])
        
        if not api_requests:
            logger.warning("No API requests found in session data")
            return {}
        
        # Get the most recent API request
        latest_api_request = api_requests[-1]
        
        config = {
            'site': site,
            'base_url': self._extract_base_url(latest_api_request['url']),
            'headers': latest_api_request['headers'],
            'cookies': cookies,
            'user_agent': session_data.get('user_agent'),
            'sample_request': {
                'url': latest_api_request['url'],
                'method': latest_api_request['method'],
                'post_data': latest_api_request['post_data']
            },
            'generated_at': datetime.now().isoformat()
        }
        
        return config
    
    def _extract_base_url(self, url: str) -> str:
        """Extract base URL from full URL."""
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"

# CLI interface for easy usage
async def main():
    """CLI interface for session extraction."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract browser sessions for API authentication")
    parser.add_argument('--site', choices=['truthfinder', 'manta'], required=True, help='Site to extract session from')
    parser.add_argument('--headless', action='store_true', help='Run browser in headless mode')
    parser.add_argument('--browser', choices=['chromium', 'firefox', 'webkit'], default='chromium', help='Browser to use')
    parser.add_argument('--search', default='John Smith', help='Search term for TruthFinder')
    parser.add_argument('--business', default='restaurant', help='Business type for Manta')
    parser.add_argument('--location', default='houston', help='Location for Manta')
    
    args = parser.parse_args()
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    extractor = SessionExtractor(headless=args.headless, browser_type=args.browser)
    
    if args.site == 'truthfinder':
        session_data = await extractor.extract_truthfinder_session(args.search)
    else:
        session_data = await extractor.extract_manta_session(args.business, args.location)
    
    if session_data:
        # Save session data
        filepath = extractor.save_session_data(session_data)
        
        # Generate API config
        api_config = extractor.generate_api_config(session_data)
        if api_config:
            config_filepath = filepath.replace('.json', '_config.json')
            with open(config_filepath, 'w') as f:
                json.dump(api_config, f, indent=2)
            print(f"✅ API config saved to: {config_filepath}")
        
        print(f"✅ Session extraction completed!")
        print(f"📁 Session data: {filepath}")
        print(f"🍪 Cookies captured: {len(session_data.get('cookies', {}))}")
        print(f"📡 API requests captured: {len(session_data.get('api_requests', []))}")
    else:
        print("❌ Session extraction failed")

if __name__ == '__main__':
    asyncio.run(main())
