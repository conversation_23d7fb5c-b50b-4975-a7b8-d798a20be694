"""
Google Custom Search API integration for reliable search functionality.
Replaces direct Google search scraping with official API.
"""

import requests
import logging
import time
from typing import List, Dict, Optional
from urllib.parse import quote_plus
import json


class GoogleCustomSearchAPI:
    """Google Custom Search API client for business owner searches."""
    
    def __init__(self, config: Dict):
        """Initialize Google Custom Search API client."""
        self.api_key = config.get('api_key', '')
        self.search_engine_id = config.get('search_engine_id', '')
        self.base_url = "https://www.googleapis.com/customsearch/v1"
        self.enabled = config.get('enabled', False) and bool(self.api_key) and bool(self.search_engine_id)
        self.rate_limit = config.get('rate_limit', 100)  # requests per day (Google's free limit)
        self.timeout = config.get('timeout', 30)
        
        self.logger = logging.getLogger(__name__)
        self.request_count = 0
        self.last_request_time = 0
        
        if not self.api_key:
            self.logger.warning("Google Custom Search API key not provided")
            self.enabled = False
        
        if not self.search_engine_id:
            self.logger.warning("Google Custom Search Engine ID not provided")
            self.enabled = False
        
        if self.enabled:
            self.logger.info("Google Custom Search API initialized successfully")
        else:
            self.logger.warning("Google Custom Search API is disabled")
    
    def _check_rate_limit(self):
        """Enforce rate limiting for API requests."""
        current_time = time.time()
        
        # Ensure minimum delay between requests (to avoid hitting rate limits)
        min_delay = 1.0  # 1 second between requests
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.1f} seconds")
            time.sleep(sleep_time)
        
        self.request_count += 1
        self.last_request_time = time.time()
        
        # Log usage
        if self.request_count % 10 == 0:
            self.logger.info(f"Google Custom Search API usage: {self.request_count} requests")
    
    def search_site_for_owners(self, site: str, business_type: str, location: str, max_results: int = 10) -> List[Dict]:
        """
        Search a specific site for business owners using Google Custom Search API.
        
        Args:
            site: Site to search (e.g., 'bbb.org', 'manta.com')
            business_type: Type of business (e.g., 'restaurant', 'construction')
            location: Location to search (e.g., 'houston tx')
            max_results: Maximum number of results to return
            
        Returns:
            List of search result dictionaries
        """
        if not self.enabled:
            self.logger.warning("Google Custom Search API is disabled")
            return []
        
        try:
            self._check_rate_limit()
            
            # Build search query with site: operator
            query = f'site:{site} "Owner" "{business_type}" "{location}"'
            
            self.logger.info(f"Searching Google Custom Search API: {query}")
            
            # Prepare API request parameters
            params = {
                'key': self.api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': min(max_results, 10),  # Google API max is 10 per request
                'safe': 'off',
                'fields': 'items(title,link,snippet,displayLink)'
            }
            
            # Make API request
            response = requests.get(
                self.base_url,
                params=params,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                items = data.get('items', [])
                
                # Convert to our standard format
                results = []
                for item in items:
                    result = {
                        'url': item.get('link', ''),
                        'title': item.get('title', ''),
                        'snippet': item.get('snippet', ''),
                        'source_site': site,
                        'search_query': query,
                        'api_source': 'google_custom_search'
                    }
                    results.append(result)
                
                self.logger.info(f"Google Custom Search API returned {len(results)} results")
                return results
                
            elif response.status_code == 429:
                self.logger.error("Google Custom Search API rate limit exceeded")
                return []
            elif response.status_code == 403:
                self.logger.error("Google Custom Search API access forbidden - check API key and quotas")
                return []
            else:
                self.logger.error(f"Google Custom Search API request failed: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            self.logger.error(f"Google Custom Search API error: {e}")
            return []
    
    def search_multiple_sites(self, sites: List[str], business_type: str, location: str, max_results_per_site: int = 5) -> Dict[str, List[Dict]]:
        """
        Search multiple sites for business owners.
        
        Args:
            sites: List of sites to search
            business_type: Type of business
            location: Location to search
            max_results_per_site: Maximum results per site
            
        Returns:
            Dictionary mapping site names to search results
        """
        if not self.enabled:
            return {}
        
        results = {}
        
        for site in sites:
            try:
                site_results = self.search_site_for_owners(
                    site, business_type, location, max_results_per_site
                )
                results[site] = site_results
                
                # Small delay between site searches
                time.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Error searching site {site}: {e}")
                results[site] = []
        
        return results
    
    def test_api_connection(self) -> bool:
        """Test if the API connection is working."""
        if not self.enabled:
            return False
        
        try:
            # Test with a simple search
            params = {
                'key': self.api_key,
                'cx': self.search_engine_id,
                'q': 'test',
                'num': 1
            }
            
            response = requests.get(
                self.base_url,
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                self.logger.info("Google Custom Search API connection test successful")
                return True
            else:
                self.logger.error(f"API connection test failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"API connection test error: {e}")
            return False
    
    def get_api_status(self) -> Dict:
        """Get current API status and usage information."""
        return {
            'enabled': self.enabled,
            'api_key_configured': bool(self.api_key),
            'search_engine_id_configured': bool(self.search_engine_id),
            'base_url': self.base_url,
            'rate_limit': self.rate_limit,
            'requests_made': self.request_count,
            'last_request': self.last_request_time
        }
    
    def get_quota_info(self) -> Dict:
        """Get API quota information (if available)."""
        # Note: Google Custom Search API doesn't provide quota info via API
        # This is for informational purposes
        return {
            'free_tier_limit': 100,  # requests per day
            'requests_used_today': self.request_count,
            'estimated_remaining': max(0, 100 - self.request_count),
            'note': 'Quota tracking is approximate - check Google Cloud Console for exact usage'
        }


class GoogleSearchFallback:
    """Fallback search functionality when Custom Search API is not available."""
    
    def __init__(self, config: Dict):
        """Initialize fallback search."""
        self.logger = logging.getLogger(__name__)
        self.enabled = config.get('fallback_enabled', True)
        
        if self.enabled:
            self.logger.info("Google search fallback enabled")
        else:
            self.logger.info("Google search fallback disabled")
    
    def search_site_for_owners(self, site: str, business_type: str, location: str, max_results: int = 10) -> List[Dict]:
        """
        Fallback search method (returns empty results with warning).
        
        This method exists to maintain compatibility but doesn't perform actual searches
        since direct Google scraping is unreliable and may violate ToS.
        """
        if not self.enabled:
            return []
        
        self.logger.warning(f"Using fallback search for {site} - results may be limited")
        self.logger.warning("Consider setting up Google Custom Search API for better results")
        
        # Return empty results - fallback doesn't perform actual searches
        # This prevents the scraper from failing but encourages API setup
        return []
    
    def get_setup_instructions(self) -> str:
        """Get instructions for setting up Google Custom Search API."""
        return """
        To set up Google Custom Search API:
        
        1. Go to Google Cloud Console (console.cloud.google.com)
        2. Create a new project or select existing project
        3. Enable the Custom Search API
        4. Create credentials (API key)
        5. Go to Google Custom Search Engine (cse.google.com)
        6. Create a new search engine
        7. Configure it to search the entire web
        8. Get the Search Engine ID
        9. Add both API key and Search Engine ID to config.yaml
        
        Configuration example:
        google_custom_search:
          enabled: true
          api_key: "YOUR_API_KEY"
          search_engine_id: "YOUR_SEARCH_ENGINE_ID"
          rate_limit: 100
        """
