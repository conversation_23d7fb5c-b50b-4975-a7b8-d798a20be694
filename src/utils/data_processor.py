"""
Data processing and deduplication utilities for scraped business owner information.
"""

import re
import logging
from typing import List, Dict, Set, Tuple, Optional
from difflib import SequenceMatcher
from collections import defaultdict
import pandas as pd
from datetime import datetime

from ..core import ScrapingResult


class DataProcessor:
    """Data processing and deduplication for scraped results."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.dedup_config = config.get('output', {}).get('deduplication', {})
        self.similarity_threshold = self.dedup_config.get('similarity_threshold', 0.8)
        self.key_fields = self.dedup_config.get('key_fields', ['owner_name', 'business_name'])
    
    def process_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Process and clean scraped results."""
        self.logger.info(f"Processing {len(results)} scraped results")
        
        # Step 1: Clean and standardize data
        cleaned_results = self._clean_results(results)
        
        # Step 2: Validate results
        valid_results = self._validate_results(cleaned_results)
        
        # Step 3: Deduplicate results
        if self.dedup_config.get('enabled', True):
            deduplicated_results = self._deduplicate_results(valid_results)
        else:
            deduplicated_results = valid_results
        
        # Step 4: Cross-source data enrichment (Manta + TruthFinder + others)
        cross_enriched_results = self._cross_source_enrichment(deduplicated_results)

        # Step 5: Final enrichment
        enriched_results = self._enrich_results(cross_enriched_results)

        self.logger.info(f"Processing complete: {len(enriched_results)} final results")

        return enriched_results

    def _cross_source_enrichment(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """
        Perform cross-source data enrichment by merging data from different sources.

        Strategy:
        1. Group results by business/owner similarity
        2. Merge Manta business data with TruthFinder personal data
        3. Enrich missing fields from multiple sources
        4. Cross-validate information across sources
        """
        self.logger.info(f"Performing cross-source enrichment on {len(results)} results")

        # Group results by source
        source_groups = defaultdict(list)
        for result in results:
            source_groups[result.source].append(result)

        self.logger.info(f"Source distribution: {dict((k, len(v)) for k, v in source_groups.items())}")

        # If we have both Manta and TruthFinder data, merge them intelligently
        manta_results = source_groups.get('manta_browser', [])
        truthfinder_results = source_groups.get('truthfinder_browser', [])
        other_results = []

        for source, source_results in source_groups.items():
            if source not in ['manta_browser', 'truthfinder_browser']:
                other_results.extend(source_results)

        enriched_results = []

        # Strategy 1: Merge Manta business data with TruthFinder personal data
        if manta_results and truthfinder_results:
            self.logger.info(f"Merging {len(manta_results)} Manta results with {len(truthfinder_results)} TruthFinder results")
            merged_results = self._merge_manta_truthfinder(manta_results, truthfinder_results)
            enriched_results.extend(merged_results)
        else:
            # Add results separately if no cross-source merging possible
            enriched_results.extend(manta_results)
            enriched_results.extend(truthfinder_results)

        # Strategy 2: Enrich with data from other sources (BBB, etc.)
        if other_results:
            self.logger.info(f"Enriching with {len(other_results)} results from other sources")
            enriched_results = self._enrich_with_other_sources(enriched_results, other_results)

        self.logger.info(f"Cross-source enrichment complete: {len(enriched_results)} enriched results")
        return enriched_results

    def _merge_manta_truthfinder(self, manta_results: List[ScrapingResult], truthfinder_results: List[ScrapingResult]) -> List[ScrapingResult]:
        """
        Intelligently merge Manta business data with TruthFinder personal data.

        Approach:
        1. Use Manta results as the base (comprehensive business data)
        2. Enrich each Manta business with matching TruthFinder personal data
        3. Match by location, business type, and name similarity
        """
        merged_results = []

        for manta_result in manta_results:
            # Start with Manta business data as base
            enriched_result = manta_result

            # Find matching TruthFinder data
            best_match = self._find_best_truthfinder_match(manta_result, truthfinder_results)

            if best_match:
                # Merge TruthFinder personal data into Manta business data
                enriched_result = self._merge_business_personal_data(manta_result, best_match)
                self.logger.debug(f"Merged Manta business '{manta_result.business_name}' with TruthFinder person '{best_match.owner_name}'")

            merged_results.append(enriched_result)

        # Add any unmatched TruthFinder results
        matched_tf_results = set()
        for manta_result in manta_results:
            best_match = self._find_best_truthfinder_match(manta_result, truthfinder_results)
            if best_match:
                matched_tf_results.add(id(best_match))

        for tf_result in truthfinder_results:
            if id(tf_result) not in matched_tf_results:
                merged_results.append(tf_result)

        return merged_results

    def _find_best_truthfinder_match(self, manta_result: ScrapingResult, truthfinder_results: List[ScrapingResult]) -> Optional[ScrapingResult]:
        """Find the best matching TruthFinder result for a Manta business."""
        best_match = None
        best_score = 0.0

        for tf_result in truthfinder_results:
            score = self._calculate_match_score(manta_result, tf_result)
            if score > best_score and score > 0.3:  # Minimum threshold
                best_score = score
                best_match = tf_result

        return best_match

    def _calculate_match_score(self, manta_result: ScrapingResult, tf_result: ScrapingResult) -> float:
        """Calculate similarity score between Manta business and TruthFinder person."""
        score = 0.0
        factors = 0

        # Location matching (city, state)
        if manta_result.city and tf_result.city:
            city_similarity = SequenceMatcher(None, manta_result.city.lower(), tf_result.city.lower()).ratio()
            score += city_similarity * 0.4
            factors += 1

        if manta_result.state and tf_result.state:
            state_similarity = SequenceMatcher(None, manta_result.state.lower(), tf_result.state.lower()).ratio()
            score += state_similarity * 0.3
            factors += 1

        # Business type relevance (restaurant owners, etc.)
        if manta_result.business_type and tf_result.business_type:
            type_similarity = SequenceMatcher(None, manta_result.business_type.lower(), tf_result.business_type.lower()).ratio()
            score += type_similarity * 0.2
            factors += 1

        # Name similarity (if available)
        if manta_result.owner_name and tf_result.owner_name:
            name_similarity = SequenceMatcher(None, manta_result.owner_name.lower(), tf_result.owner_name.lower()).ratio()
            score += name_similarity * 0.1
            factors += 1

        return score / max(factors, 1)

    def _merge_business_personal_data(self, manta_result: ScrapingResult, tf_result: ScrapingResult) -> ScrapingResult:
        """Merge Manta business data with TruthFinder personal data."""
        # Start with Manta business data
        merged = ScrapingResult(
            # Business data from Manta (primary)
            business_name=manta_result.business_name or tf_result.business_name,
            business_type=manta_result.business_type or tf_result.business_type,
            business_description=manta_result.business_description or tf_result.business_description,
            website=manta_result.website or tf_result.website,

            # Personal data from TruthFinder (primary)
            owner_name=tf_result.owner_name or manta_result.owner_name,
            owner_age=tf_result.owner_age or manta_result.owner_age,

            # Contact data - merge from both sources
            phone=manta_result.phone or tf_result.phone,
            email=manta_result.email or tf_result.email,

            # Address data - prefer Manta for business, TruthFinder for personal
            address=manta_result.address or tf_result.address,
            street_address=manta_result.street_address or tf_result.street_address,
            city=manta_result.city or tf_result.city,
            state=manta_result.state or tf_result.state,
            zip_code=manta_result.zip_code or tf_result.zip_code,

            # Family and personal data from TruthFinder
            executives=tf_result.executives or manta_result.executives,
            previous_addresses=tf_result.previous_addresses or manta_result.previous_addresses,

            # Business data from Manta
            years_in_business=manta_result.years_in_business or tf_result.years_in_business,
            employee_count=manta_result.employee_count or tf_result.employee_count,

            # Metadata
            location=manta_result.location or tf_result.location,
            source=f"{manta_result.source}+{tf_result.source}",  # Indicate merged sources
            url=manta_result.url or tf_result.url,
            scraped_at=max(manta_result.scraped_at, tf_result.scraped_at),
            confidence_score=min(manta_result.confidence_score + tf_result.confidence_score, 1.0),
            data_quality='enriched',  # Mark as enriched data
            verification_status='cross_verified'  # Cross-verified across sources
        )

        return merged

    def _enrich_with_other_sources(self, base_results: List[ScrapingResult], other_results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Enrich base results with data from other sources (BBB, etc.)."""
        enriched = base_results.copy()

        # Add unique results from other sources that don't match existing ones
        for other_result in other_results:
            if not self._has_similar_result(other_result, enriched):
                enriched.append(other_result)

        return enriched

    def _has_similar_result(self, result: ScrapingResult, result_list: List[ScrapingResult]) -> bool:
        """Check if a similar result already exists in the list."""
        for existing in result_list:
            if self._are_similar_results(result, existing):
                return True
        return False

    def _are_similar_results(self, result1: ScrapingResult, result2: ScrapingResult) -> bool:
        """Check if two results are similar enough to be considered duplicates."""
        # Use existing similarity logic
        similarity = self._calculate_similarity(result1, result2)
        return similarity > self.similarity_threshold

    def _clean_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Clean and standardize result data."""
        cleaned_results = []
        
        for result in results:
            # Clean owner name
            if result.owner_name:
                result.owner_name = self._clean_name(result.owner_name)
            
            # Clean business name
            if result.business_name:
                result.business_name = self._clean_business_name(result.business_name)
            
            # Clean location
            if result.location:
                result.location = self._clean_location(result.location)
            
            # Clean contact information
            if result.phone:
                result.phone = self._clean_phone(result.phone)
            
            if result.email:
                result.email = self._clean_email(result.email)
            
            if result.address:
                result.address = self._clean_address(result.address)
            
            cleaned_results.append(result)
        
        return cleaned_results
    
    def _clean_name(self, name: str) -> str:
        """Clean and standardize person names."""
        if not name:
            return ""
        
        # Remove extra whitespace
        name = re.sub(r'\s+', ' ', name.strip())
        
        # Remove common prefixes/suffixes
        name = re.sub(r'^(Mr\.|Mrs\.|Ms\.|Dr\.|Prof\.)\s*', '', name, flags=re.IGNORECASE)
        name = re.sub(r'\s*(Jr\.|Sr\.|III|IV|PhD|MD)$', '', name, flags=re.IGNORECASE)
        
        # Remove business titles
        name = re.sub(r'\s*(Owner|CEO|President|Founder|Manager)$', '', name, flags=re.IGNORECASE)
        
        # Title case
        name = name.title()
        
        # Fix common issues
        name = re.sub(r'\bMc([a-z])', r'Mc\1', name)  # McDonald -> McDonald
        name = re.sub(r'\bO\'([a-z])', r"O'\1", name)  # O'connor -> O'Connor
        
        return name.strip()
    
    def _clean_business_name(self, business_name: str) -> str:
        """Clean and standardize business names."""
        if not business_name:
            return ""
        
        # Remove extra whitespace
        business_name = re.sub(r'\s+', ' ', business_name.strip())
        
        # Remove common prefixes
        business_name = re.sub(r'^(The\s+)', '', business_name, flags=re.IGNORECASE)
        
        # Standardize business entity suffixes
        suffixes = {
            r'\b(inc|incorporated)\b': 'Inc.',
            r'\b(llc|l\.l\.c\.)\b': 'LLC',
            r'\b(corp|corporation)\b': 'Corp.',
            r'\b(ltd|limited)\b': 'Ltd.',
            r'\b(co|company)\b': 'Company'
        }
        
        for pattern, replacement in suffixes.items():
            business_name = re.sub(pattern, replacement, business_name, flags=re.IGNORECASE)
        
        return business_name.strip()
    
    def _clean_location(self, location: str) -> str:
        """Clean and standardize location information."""
        if not location:
            return ""
        
        # Remove extra whitespace
        location = re.sub(r'\s+', ' ', location.strip())
        
        # Title case
        location = location.title()
        
        # Standardize state abbreviations
        state_abbrevs = {
            'Texas': 'TX', 'California': 'CA', 'Florida': 'FL',
            'New York': 'NY', 'Illinois': 'IL', 'Pennsylvania': 'PA'
            # Add more as needed
        }
        
        for state, abbrev in state_abbrevs.items():
            location = re.sub(rf'\b{state}\b', abbrev, location, flags=re.IGNORECASE)
        
        return location.strip()
    
    def _clean_phone(self, phone: str) -> str:
        """Clean and standardize phone numbers."""
        if not phone:
            return ""
        
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', phone)
        
        # Format as (XXX) XXX-XXXX if 10 digits
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        
        return phone  # Return original if can't format
    
    def _clean_email(self, email: str) -> str:
        """Clean and validate email addresses."""
        if not email:
            return ""
        
        email = email.strip().lower()
        
        # Basic email validation
        if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            return email
        
        return ""  # Return empty if invalid
    
    def _clean_address(self, address: str) -> str:
        """Clean and standardize addresses."""
        if not address:
            return ""
        
        # Remove extra whitespace
        address = re.sub(r'\s+', ' ', address.strip())
        
        # Title case
        address = address.title()
        
        # Standardize common abbreviations
        abbrevs = {
            r'\bStreet\b': 'St',
            r'\bAvenue\b': 'Ave',
            r'\bBoulevard\b': 'Blvd',
            r'\bRoad\b': 'Rd',
            r'\bDrive\b': 'Dr',
            r'\bLane\b': 'Ln'
        }
        
        for pattern, replacement in abbrevs.items():
            address = re.sub(pattern, replacement, address, flags=re.IGNORECASE)
        
        return address.strip()
    
    def _validate_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Validate and filter results."""
        valid_results = []
        
        for result in results:
            # Must have either owner name or business name
            if not result.owner_name and not result.business_name:
                continue
            
            # Owner name validation
            if result.owner_name:
                # Must be at least 2 characters and look like a name
                if len(result.owner_name) < 2:
                    continue
                if not re.match(r'^[A-Za-z\s\'-\.]+$', result.owner_name):
                    continue
            
            # Business name validation
            if result.business_name:
                # Must be at least 2 characters
                if len(result.business_name) < 2:
                    continue
            
            valid_results.append(result)
        
        self.logger.info(f"Validation: {len(valid_results)}/{len(results)} results passed")
        return valid_results
    
    def _deduplicate_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """
        Remove duplicate results based on similarity with intelligent cross-source merging.

        Enhanced deduplication strategy:
        1. Group similar results together
        2. For each group, merge data from multiple sources if available
        3. Select the most comprehensive result from each group
        """
        if not results:
            return results

        self.logger.info(f"Deduplicating {len(results)} results with cross-source merging")

        # Group results by similarity
        groups = self._group_similar_results(results)

        # Process each group with intelligent merging
        deduplicated = []
        for group in groups:
            if len(group) == 1:
                # Single result, no merging needed
                deduplicated.append(group[0])
            else:
                # Multiple similar results - merge them intelligently
                merged_result = self._merge_similar_results(group)
                deduplicated.append(merged_result)

        self.logger.info(f"Deduplication: {len(deduplicated)} unique results from {len(results)} total")
        return deduplicated

    def _merge_similar_results(self, similar_results: List[ScrapingResult]) -> ScrapingResult:
        """
        Merge multiple similar results into one comprehensive result.

        Strategy:
        1. Prioritize data from more reliable sources
        2. Combine non-conflicting information
        3. Use the most complete data available
        """
        if len(similar_results) == 1:
            return similar_results[0]

        # Source priority for data selection
        source_priority = {
            'manta_browser': 1,      # Best for business data
            'truthfinder_browser': 2, # Best for personal data
            'bbb': 3,                # Good for business verification
            'manta': 4,              # Fallback business data
            'truepeoplesearch': 5,   # Fallback personal data
            'cyberbackgroundchecks': 6
        }

        # Sort by source priority and data quality
        sorted_results = sorted(similar_results, key=lambda r: (
            source_priority.get(r.source, 999),
            -r.confidence_score,
            -(len(r.business_name or '') + len(r.owner_name or ''))
        ))

        # Use the highest priority result as base
        base_result = sorted_results[0]

        # Merge additional data from other sources
        merged = ScrapingResult(
            # Business information - prioritize Manta
            business_name=self._select_best_field([r.business_name for r in sorted_results]),
            business_type=self._select_best_field([r.business_type for r in sorted_results]),
            business_description=self._select_best_field([r.business_description for r in sorted_results]),
            website=self._select_best_field([r.website for r in sorted_results]),
            years_in_business=self._select_best_field([r.years_in_business for r in sorted_results]),
            employee_count=self._select_best_field([r.employee_count for r in sorted_results]),

            # Personal information - prioritize TruthFinder
            owner_name=self._select_best_field([r.owner_name for r in sorted_results]),
            owner_age=self._select_best_field([r.owner_age for r in sorted_results]),

            # Contact information - merge from all sources
            phone=self._select_best_field([r.phone for r in sorted_results]),
            email=self._select_best_field([r.email for r in sorted_results]),

            # Address information - prefer most complete
            address=self._select_best_field([r.address for r in sorted_results]),
            street_address=self._select_best_field([r.street_address for r in sorted_results]),
            city=self._select_best_field([r.city for r in sorted_results]),
            state=self._select_best_field([r.state for r in sorted_results]),
            zip_code=self._select_best_field([r.zip_code for r in sorted_results]),

            # Family and relationship data - combine from all sources
            executives=self._merge_list_fields([r.executives for r in sorted_results]),
            previous_addresses=self._merge_list_fields([r.previous_addresses for r in sorted_results]),

            # Metadata
            location=base_result.location,
            source='+'.join(set(r.source for r in sorted_results)),  # Indicate merged sources
            url=self._select_best_field([r.url for r in sorted_results]),
            scraped_at=max(r.scraped_at for r in sorted_results),
            confidence_score=min(sum(r.confidence_score for r in sorted_results) / len(sorted_results), 1.0),
            data_quality='merged',
            verification_status='multi_source_verified'
        )

        self.logger.debug(f"Merged {len(similar_results)} similar results from sources: {[r.source for r in sorted_results]}")
        return merged

    def _select_best_field(self, field_values: List[str]) -> str:
        """Select the best non-empty field value from a list."""
        for value in field_values:
            if value and value.strip():
                return value.strip()
        return ""

    def _merge_list_fields(self, list_fields: List[List[str]]) -> List[str]:
        """Merge list fields from multiple sources, removing duplicates."""
        merged = []
        seen = set()

        for field_list in list_fields:
            if field_list:
                for item in field_list:
                    if item and item not in seen:
                        merged.append(item)
                        seen.add(item)

        return merged
    
    def _group_similar_results(self, results: List[ScrapingResult]) -> List[List[ScrapingResult]]:
        """Group similar results together."""
        groups = []
        used_indices = set()
        
        for i, result1 in enumerate(results):
            if i in used_indices:
                continue
            
            group = [result1]
            used_indices.add(i)
            
            for j, result2 in enumerate(results[i+1:], i+1):
                if j in used_indices:
                    continue
                
                if self._are_similar(result1, result2):
                    group.append(result2)
                    used_indices.add(j)
            
            groups.append(group)
        
        return groups
    
    def _are_similar(self, result1: ScrapingResult, result2: ScrapingResult) -> bool:
        """Check if two results are similar enough to be considered duplicates."""
        similarities = []
        
        # Compare owner names
        if result1.owner_name and result2.owner_name:
            name_sim = self._string_similarity(result1.owner_name, result2.owner_name)
            similarities.append(name_sim)
        
        # Compare business names
        if result1.business_name and result2.business_name:
            business_sim = self._string_similarity(result1.business_name, result2.business_name)
            similarities.append(business_sim)
        
        # If no comparable fields, not similar
        if not similarities:
            return False
        
        # Average similarity must exceed threshold
        avg_similarity = sum(similarities) / len(similarities)
        return avg_similarity >= self.similarity_threshold
    
    def _string_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings."""
        if not str1 or not str2:
            return 0.0
        
        # Normalize strings
        str1 = str1.lower().strip()
        str2 = str2.lower().strip()
        
        # Exact match
        if str1 == str2:
            return 1.0
        
        # Use SequenceMatcher for similarity
        return SequenceMatcher(None, str1, str2).ratio()
    
    def _select_best_result(self, group: List[ScrapingResult]) -> ScrapingResult:
        """Select the best result from a group of similar results."""
        if len(group) == 1:
            return group[0]
        
        # Score results based on completeness
        scored_results = []
        for result in group:
            score = self._calculate_completeness_score(result)
            scored_results.append((score, result))
        
        # Sort by score (highest first)
        scored_results.sort(key=lambda x: x[0], reverse=True)
        
        # Merge information from all results into the best one
        best_result = scored_results[0][1]
        merged_result = self._merge_results(group, best_result)
        
        return merged_result
    
    def _calculate_completeness_score(self, result: ScrapingResult) -> int:
        """Calculate completeness score for a result."""
        score = 0
        
        if result.owner_name:
            score += 3
        if result.business_name:
            score += 3
        if result.phone:
            score += 2
        if result.email:
            score += 2
        if result.address:
            score += 1
        if result.business_type:
            score += 1
        if result.location:
            score += 1
        
        return score
    
    def _merge_results(self, group: List[ScrapingResult], base_result: ScrapingResult) -> ScrapingResult:
        """Merge information from multiple results into one."""
        merged = base_result
        
        # Collect all non-empty values for each field
        for result in group:
            if not merged.owner_name and result.owner_name:
                merged.owner_name = result.owner_name
            if not merged.business_name and result.business_name:
                merged.business_name = result.business_name
            if not merged.phone and result.phone:
                merged.phone = result.phone
            if not merged.email and result.email:
                merged.email = result.email
            if not merged.address and result.address:
                merged.address = result.address
            if not merged.business_type and result.business_type:
                merged.business_type = result.business_type
            if not merged.location and result.location:
                merged.location = result.location
        
        # Merge raw data
        merged_raw_data = {}
        for result in group:
            if result.raw_data:
                merged_raw_data.update(result.raw_data)
        merged.raw_data = merged_raw_data
        
        return merged
    
    def _enrich_results(self, results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Enrich results with additional information."""
        for result in results:
            # Add confidence score
            result.raw_data['confidence_score'] = self._calculate_confidence_score(result)
            
            # Add data quality indicators
            result.raw_data['data_quality'] = self._assess_data_quality(result)
        
        return results
    
    def _calculate_confidence_score(self, result: ScrapingResult) -> float:
        """Calculate confidence score for a result."""
        score = 0.0
        max_score = 0.0
        
        # Owner name confidence
        if result.owner_name:
            max_score += 0.4
            if len(result.owner_name.split()) >= 2:  # First and last name
                score += 0.4
            else:
                score += 0.2
        
        # Business name confidence
        if result.business_name:
            max_score += 0.3
            score += 0.3
        
        # Contact info confidence
        if result.phone:
            max_score += 0.15
            score += 0.15
        
        if result.email:
            max_score += 0.15
            score += 0.15
        
        return score / max_score if max_score > 0 else 0.0
    
    def _assess_data_quality(self, result: ScrapingResult) -> str:
        """Assess overall data quality."""
        completeness = self._calculate_completeness_score(result)
        
        if completeness >= 8:
            return "high"
        elif completeness >= 5:
            return "medium"
        else:
            return "low"
