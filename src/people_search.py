#!/usr/bin/env python3
"""
People Search Engine - Comprehensive people search functionality.

This module provides people search capabilities that complement the existing
business owner search functionality. It allows searching for individuals by
name and address/location, returning comprehensive personal information.
"""

import logging
import re
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

from .core import ScrapingEngine, ScrapingResult
from .utils.data_processor import DataProcessor


@dataclass
class PersonSearchQuery:
    """Data class for person search queries."""
    first_name: str
    last_name: str
    middle_name: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    age_range: Optional[Tuple[int, int]] = None
    phone: Optional[str] = None
    email: Optional[str] = None


class PersonSearchEngine:
    """
    Main engine for people search functionality.
    
    This class orchestrates people searches across multiple data sources
    using the same anti-bot protection and error handling as business searches.
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the person search engine."""
        self.engine = ScrapingEngine(config_path)
        self.data_processor = DataProcessor(self.engine.config)
        self.logger = logging.getLogger(__name__)
        
        # Initialize people search specific scrapers
        self.people_scrapers = {}
        self._init_people_scrapers()
        
        # Search configuration
        self.search_config = self.engine.config.get('people_search', {})
        self.max_results_per_source = self.search_config.get('max_results_per_source', 50)
        self.enable_cross_reference = self.search_config.get('enable_cross_reference', True)
        
        self.logger.info("PersonSearchEngine initialized")
    
    def _init_people_scrapers(self):
        """Initialize scrapers that support people search."""
        from .scrapers.truthfinder_browser_scraper import TruthFinderBrowserScraper
        from .scrapers.truepeoplesearch_scraper import TruePeopleSearchScraper
        from .scrapers.cyberbackgroundchecks_scraper import CyberBackgroundChecksScraper
        
        # Initialize people-capable scrapers
        people_sources = self.engine.config.get('sources', {})
        
        if people_sources.get('truthfinder_browser', {}).get('enabled', False):
            self.people_scrapers['truthfinder_browser'] = TruthFinderBrowserScraper(self.engine)
            
        if people_sources.get('truepeoplesearch', {}).get('enabled', False):
            self.people_scrapers['truepeoplesearch'] = TruePeopleSearchScraper(self.engine)
            
        if people_sources.get('cyberbackgroundchecks', {}).get('enabled', False):
            self.people_scrapers['cyberbackgroundchecks'] = CyberBackgroundChecksScraper(self.engine)
        
        self.logger.info(f"Initialized {len(self.people_scrapers)} people search scrapers")
    
    def search_person(self, query: PersonSearchQuery, 
                     sources: Optional[List[str]] = None) -> List[ScrapingResult]:
        """
        Search for a person across multiple data sources.
        
        Args:
            query: PersonSearchQuery object with search parameters
            sources: Optional list of specific sources to search
            
        Returns:
            List of ScrapingResult objects with person information
        """
        self.logger.info(f"Starting people search for {query.first_name} {query.last_name}")
        
        # Use all enabled scrapers if none specified
        if not sources:
            sources = list(self.people_scrapers.keys())
        
        # Filter to only enabled scrapers
        enabled_sources = [s for s in sources if s in self.people_scrapers 
                          and self.people_scrapers[s].is_enabled()]
        
        if not enabled_sources:
            self.logger.warning("No enabled people search sources available")
            return []
        
        all_results = []
        
        # Search each source
        for source_name in enabled_sources:
            try:
                self.logger.info(f"Searching {source_name} for person data")
                scraper = self.people_scrapers[source_name]
                
                # Convert query to format expected by scraper
                results = self._search_source(scraper, query, source_name)
                
                if results:
                    # Add search context to results
                    for result in results:
                        result.search_type = 'people'
                        result.search_query = f"{query.first_name} {query.last_name}"
                        if query.address:
                            result.search_query += f" {query.address}"
                    
                    all_results.extend(results)
                    self.logger.info(f"Found {len(results)} results from {source_name}")
                else:
                    self.logger.info(f"No results from {source_name}")
                    
            except Exception as e:
                self.logger.error(f"Error searching {source_name}: {e}")
                continue
        
        self.logger.info(f"Raw people search completed: {len(all_results)} total results")
        
        # Process and enrich results
        if all_results:
            processed_results = self.data_processor.process_results(all_results)
            self.logger.info(f"Processed people search results: {len(processed_results)} final results")
            return processed_results
        
        return []
    
    def _search_source(self, scraper, query: PersonSearchQuery, source_name: str) -> List[ScrapingResult]:
        """Search a specific source for person data."""
        try:
            # Different scrapers may have different search methods
            if hasattr(scraper, 'search_person'):
                # Use dedicated person search method if available
                return scraper.search_person(query)
            elif hasattr(scraper, 'search_by_name'):
                # Use name-based search method
                location = self._build_location_string(query)
                return scraper.search_by_name(query.first_name, query.last_name, location)
            else:
                # Fall back to general search method
                # Convert person query to business-style search
                search_term = f"{query.first_name} {query.last_name}"
                location = self._build_location_string(query)
                return scraper.search(search_term, location)
                
        except Exception as e:
            self.logger.error(f"Error in {source_name} person search: {e}")
            return []
    
    def _build_location_string(self, query: PersonSearchQuery) -> str:
        """Build location string from query components."""
        location_parts = []
        
        if query.city:
            location_parts.append(query.city)
        if query.state:
            location_parts.append(query.state)
        if query.zip_code:
            location_parts.append(query.zip_code)
        elif query.address:
            location_parts.append(query.address)
            
        return " ".join(location_parts) if location_parts else ""
    
    def cross_reference_with_business_data(self, person_results: List[ScrapingResult], 
                                         business_results: List[ScrapingResult]) -> List[ScrapingResult]:
        """
        Cross-reference person search results with business data.
        
        This method identifies connections between people and businesses,
        enriching the data with business ownership information.
        """
        if not self.enable_cross_reference:
            return person_results
            
        self.logger.info("Cross-referencing person data with business information")
        
        enriched_results = []
        
        for person_result in person_results:
            # Create a copy to avoid modifying original
            enriched_person = person_result
            
            # Look for matching business records
            matching_businesses = self._find_matching_businesses(person_result, business_results)
            
            if matching_businesses:
                # Add business affiliations
                for business in matching_businesses:
                    business_info = {
                        'business_name': business.business_name,
                        'business_type': business.business_type,
                        'role': 'Owner',  # Assume ownership for now
                        'address': business.address,
                        'phone': business.phone,
                        'source': business.source
                    }
                    enriched_person.related_businesses.append(business_info)
                
                self.logger.info(f"Found {len(matching_businesses)} business connections for {person_result.owner_name}")
            
            enriched_results.append(enriched_person)
        
        return enriched_results
    
    def _find_matching_businesses(self, person_result: ScrapingResult, 
                                business_results: List[ScrapingResult]) -> List[ScrapingResult]:
        """Find business records that match a person."""
        matches = []
        
        if not person_result.owner_name:
            return matches
        
        person_name = person_result.owner_name.lower()
        
        for business in business_results:
            if not business.owner_name:
                continue
                
            business_owner = business.owner_name.lower()
            
            # Simple name matching (can be enhanced with fuzzy matching)
            if self._names_match(person_name, business_owner):
                # Additional validation based on location/phone if available
                if self._validate_person_business_match(person_result, business):
                    matches.append(business)
        
        return matches
    
    def _names_match(self, name1: str, name2: str) -> bool:
        """Check if two names match (basic implementation)."""
        # Remove common prefixes/suffixes
        name1_clean = re.sub(r'\b(mr|mrs|ms|dr|prof)\b\.?', '', name1, flags=re.IGNORECASE).strip()
        name2_clean = re.sub(r'\b(mr|mrs|ms|dr|prof)\b\.?', '', name2, flags=re.IGNORECASE).strip()
        
        # Simple exact match for now (can be enhanced with fuzzy matching)
        return name1_clean == name2_clean
    
    def _validate_person_business_match(self, person: ScrapingResult, 
                                      business: ScrapingResult) -> bool:
        """Validate that a person and business record refer to the same entity."""
        # Check phone number match
        if person.phone and business.phone:
            person_phone = re.sub(r'[^\d]', '', person.phone)
            business_phone = re.sub(r'[^\d]', '', business.phone)
            if person_phone == business_phone:
                return True
        
        # Check address similarity (basic implementation)
        if person.address and business.address:
            person_addr = person.address.lower()
            business_addr = business.address.lower()
            # Check if they share common address components
            person_parts = set(person_addr.split())
            business_parts = set(business_addr.split())
            common_parts = person_parts.intersection(business_parts)
            if len(common_parts) >= 2:  # At least 2 common address components
                return True
        
        # If no additional validation possible, assume match based on name
        return True
    
    def cleanup(self):
        """Clean up resources."""
        if self.engine:
            self.engine.cleanup()
        
        for scraper in self.people_scrapers.values():
            if hasattr(scraper, 'cleanup'):
                scraper.cleanup()
