#!/usr/bin/env python3
"""
Manta Internal API Integration
Uses Manta's internal API endpoints for direct business data access.
"""

import requests
import json
import time
import random
from typing import List, Dict, Optional, Any
from datetime import datetime
from urllib.parse import quote_plus, urljoin
from bs4 import BeautifulSoup
import logging

from src.core import ScrapingResult

logger = logging.getLogger(__name__)

class MantaAPI:
    """Manta internal API client for business data extraction."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('manta_api', {})
        self.base_url = "https://www.manta.com"
        self.session = requests.Session()
        self.rate_limit_delay = self.config.get('rate_limit_delay', 2)
        self.max_retries = self.config.get('max_retries', 3)
        
        # Set up session with headers from the captured request
        self.session.headers.update({
            'Accept': '*/*',
            'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-arch': '""',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-full-version': '138.0.7204.184',
            'sec-ch-ua-mobile': '?1',
            'sec-ch-ua-platform': '"Android"',
            'x-request-id': self._generate_request_id()
        })
        
        # Set cookies from captured session
        self._set_session_cookies()
        
        logger.info("Manta API client initialized")
    
    def _generate_request_id(self) -> str:
        """Generate a random request ID for API calls."""
        import uuid
        return str(uuid.uuid4()).replace('-', '')[:32]
    
    def _set_session_cookies(self):
        """Set session cookies from captured request."""
        # These are the cookies from the captured request
        # In production, you'd want to refresh these periodically
        cookies = {
            'refer_id': '0000',
            'sess_refer': '1',
            'cust_id': 'a2b03c09-72f5-4d7a-b6c8-dc32a8a6abb6',
            '_gcl_au': '1.1.629188657.1754337795',
            '_fbp': 'fb.1.1754337795481.554633048990336608',
            '_ga': 'GA1.1.441186518.1754337796',
            'cf_clearance': '67lzCpUWG7xbBEFllgeswp1CMxszKGtS2Nbdw128.Zs-1754337837-1.2.1.1-lWhdW2m.CnlJ3plKWu_..wXwivAOHoIA35vil9Om_Xp.bBKwCYWQnerm.UZstA0Ns_rh5KN3eIZatCc_3R4h1Rx8QvfKiMzqd6thVyc77x8CSGRHL0GyL8hDwc3by2J5XKvLihFwS5vQzezhhRyp3BFm7eVYSF3GjyNL36EKwQXjq6Lx9GOFcPL6HorpArF9AakGzUHLg1T02bYMpT37XjCnKI0BnTwvoVIWPk.7MrM',
            'pwBotScore': '99',
            'usprivacy': '1---',
            'ad_clicker': 'false'
        }
        
        for name, value in cookies.items():
            self.session.cookies.set(name, value, domain='.manta.com')
    
    def search_businesses(self, business_type: str, location: str, max_pages: int = 5) -> List[ScrapingResult]:
        """
        Search for businesses using Manta's internal API.
        
        Args:
            business_type: Type of business to search for
            location: Location to search in
            max_pages: Maximum number of pages to scrape
            
        Returns:
            List of ScrapingResult objects
        """
        logger.info(f"Searching Manta API for {business_type} in {location}")
        
        results = []
        
        # First, find the category ID for the business type
        category_id = self._find_category_id(business_type, location)
        if not category_id:
            logger.warning(f"Could not find category ID for {business_type}")
            return results
        
        # Search through multiple pages
        for page in range(1, max_pages + 1):
            try:
                page_results = self._search_page(category_id, business_type, location, page)
                results.extend(page_results)
                
                if not page_results:
                    logger.info(f"No more results found at page {page}")
                    break
                
                # Rate limiting
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"Error searching page {page}: {e}")
                continue
        
        logger.info(f"Manta API returned {len(results)} results")
        return results
    
    def _find_category_id(self, business_type: str, location: str) -> Optional[str]:
        """Find the Manta category ID for a business type."""
        # This would typically involve searching Manta's category system
        # For now, we'll use some common mappings
        category_mappings = {
            'restaurant': '54_C4_000',
            'construction': '54_B1_000', 
            'lawn care': '54_B3_KCQ',
            'plumbing': '54_B1_KCQ',
            'electrical': '54_B1_KCQ',
            'automotive': '54_A1_000',
            'dental': '54_D0_KCQ',
            'medical': '54_D0_000',
            'legal': '54_A6_KCQ',
            'accounting': '54_A6_000',
            'real estate': '54_A6_KCQ',
            'insurance': '54_A6_000'
        }
        
        # Try exact match first
        business_lower = business_type.lower()
        if business_lower in category_mappings:
            return category_mappings[business_lower]
        
        # Try partial matches
        for key, category_id in category_mappings.items():
            if key in business_lower or business_lower in key:
                return category_id
        
        # Default to general business services
        return '54_A6_000'
    
    def _search_page(self, category_id: str, business_type: str, location: str, page: int) -> List[ScrapingResult]:
        """Search a specific page of results."""
        # Construct the API URL
        # Format: /more-results/{category_id}/{business_type}?pg={page}
        business_slug = business_type.lower().replace(' ', '_')
        location_slug = location.lower().replace(' ', '_').replace(',', '')
        
        url = f"{self.base_url}/more-results/{category_id}/{business_slug}"
        params = {'pg': page}
        
        # Add location if needed
        if location:
            url = f"{self.base_url}/mb_{category_id}/{business_slug}/{location_slug}"
        
        logger.debug(f"Requesting Manta API: {url} with params: {params}")
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 404:
                logger.warning(f"Manta API endpoint not found: {url}")
                return []
            elif response.status_code != 200:
                logger.warning(f"Manta API returned status {response.status_code}")
                return []
            
            # Parse the response
            return self._parse_api_response(response.text, business_type, location)
            
        except requests.RequestException as e:
            logger.error(f"Manta API request failed: {e}")
            return []
    
    def _parse_api_response(self, html_content: str, business_type: str, location: str) -> List[ScrapingResult]:
        """Parse HTML response from Manta API."""
        results = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Look for business listings in the HTML
            # Manta typically uses specific CSS classes for business listings
            business_selectors = [
                '.business-card',
                '.listing-item',
                '.company-listing',
                '[data-business-id]',
                '.search-result'
            ]
            
            businesses = []
            for selector in business_selectors:
                businesses = soup.select(selector)
                if businesses:
                    break
            
            if not businesses:
                logger.debug("No business listings found in Manta API response")
                return results
            
            for business in businesses:
                try:
                    result = self._extract_business_data(business, business_type, location)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"Error extracting business data: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"Error parsing Manta API response: {e}")
        
        return results
    
    def _extract_business_data(self, business_element, business_type: str, location: str) -> Optional[ScrapingResult]:
        """Extract comprehensive business data from a business listing element."""
        try:
            # Extract basic information
            business_name = self._extract_text(business_element, [
                '.business-name', '.company-name', '.listing-title', 'h2', 'h3'
            ])
            
            owner_name = self._extract_text(business_element, [
                '.owner-name', '.contact-name', '.manager-name'
            ])
            
            # Extract contact information
            phone = self._extract_text(business_element, [
                '.phone', '.tel', '[href^="tel:"]', '.contact-phone'
            ])
            
            email = self._extract_text(business_element, [
                '.email', '[href^="mailto:"]', '.contact-email'
            ])
            
            website = self._extract_link(business_element, [
                '.website', '.url', '.company-website'
            ])
            
            # Extract address
            address = self._extract_text(business_element, [
                '.address', '.location', '.business-address'
            ])
            
            # Extract business URL
            business_url = self._extract_link(business_element, [
                'a[href*="/c/"]', '.business-link', '.company-link'
            ])
            
            if business_url and not business_url.startswith('http'):
                business_url = urljoin(self.base_url, business_url)
            
            # Create result with comprehensive data
            result = ScrapingResult(
                owner_name=owner_name,
                business_name=business_name,
                business_type=business_type,
                location=location,
                source="manta_api",
                url=business_url,
                phone=phone,
                email=email,
                address=address,
                scraped_at=datetime.now(),
                confidence_score=0.9,  # High confidence for API data
                data_quality='high',
                verification_status='api_verified'
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting business data: {e}")
            return None
    
    def _extract_text(self, element, selectors: List[str]) -> Optional[str]:
        """Extract text using multiple selector options."""
        for selector in selectors:
            found = element.select_one(selector)
            if found:
                text = found.get_text(strip=True)
                if text:
                    return text
        return None
    
    def _extract_link(self, element, selectors: List[str]) -> Optional[str]:
        """Extract link URL using multiple selector options."""
        for selector in selectors:
            found = element.select_one(selector)
            if found:
                href = found.get('href')
                if href:
                    return href
        return None
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get API health status."""
        try:
            # Test with a simple request
            response = self.session.get(f"{self.base_url}/", timeout=10)
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'response_time': response.elapsed.total_seconds(),
                'status_code': response.status_code,
                'cookies_set': len(self.session.cookies) > 0
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'cookies_set': len(self.session.cookies) > 0
            }
