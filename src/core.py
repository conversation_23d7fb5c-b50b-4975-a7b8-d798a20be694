"""
Core scraping engine for business owner information extraction.
"""

import asyncio
import logging
import random
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import yaml
import os
from pathlib import Path

import requests
import cloudscraper
from fake_useragent import UserAgent
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import undetected_chromedriver as uc
from bs4 import BeautifulSoup
import pandas as pd


@dataclass
class ScrapingResult:
    """Enhanced data class for comprehensive scraping results."""
    # Basic Information
    owner_name: Optional[str] = None
    business_name: Optional[str] = None
    business_type: Optional[str] = None
    location: Optional[str] = None
    source: Optional[str] = None
    url: Optional[str] = None
    scraped_at: datetime = field(default_factory=datetime.now)

    # Contact Information
    phone: Optional[str] = None
    phone_secondary: Optional[str] = None
    email: Optional[str] = None
    email_secondary: Optional[str] = None
    website: Optional[str] = None
    fax: Optional[str] = None

    # Address Information
    address: Optional[str] = None
    street_address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    county: Optional[str] = None
    country: Optional[str] = None

    # Business Details
    business_description: Optional[str] = None
    business_category: Optional[str] = None
    business_subcategory: Optional[str] = None
    years_in_business: Optional[str] = None
    employee_count: Optional[str] = None
    annual_revenue: Optional[str] = None
    business_license: Optional[str] = None

    # Owner/Executive Information
    owner_title: Optional[str] = None
    owner_age: Optional[str] = None
    owner_education: Optional[str] = None
    executives: List[Dict[str, str]] = field(default_factory=list)  # [{name, title, contact}]

    # Business Ratings & Reviews
    bbb_rating: Optional[str] = None
    bbb_accredited: Optional[bool] = None
    google_rating: Optional[str] = None
    yelp_rating: Optional[str] = None
    total_reviews: Optional[str] = None

    # Social Media & Online Presence
    linkedin_url: Optional[str] = None
    facebook_url: Optional[str] = None
    twitter_url: Optional[str] = None
    instagram_url: Optional[str] = None
    youtube_url: Optional[str] = None

    # Financial & Legal Information
    business_structure: Optional[str] = None  # LLC, Corp, Partnership, etc.
    tax_id: Optional[str] = None
    duns_number: Optional[str] = None
    incorporation_date: Optional[str] = None
    incorporation_state: Optional[str] = None

    # Related Entities
    parent_company: Optional[str] = None
    subsidiaries: List[str] = field(default_factory=list)
    related_businesses: List[Dict[str, str]] = field(default_factory=list)

    # Historical Data
    previous_addresses: List[Dict[str, str]] = field(default_factory=list)
    previous_names: List[str] = field(default_factory=list)
    business_history: List[Dict[str, str]] = field(default_factory=list)

    # Additional Metadata
    confidence_score: Optional[float] = None
    data_quality: Optional[str] = None
    last_updated: Optional[str] = None
    verification_status: Optional[str] = None

    # Raw data and source-specific information
    raw_data: Dict[str, Any] = field(default_factory=dict)
    google_snippet: Optional[str] = None
    google_title: Optional[str] = None
    search_pattern: Optional[str] = None


class ScrapingEngine:
    """Main scraping engine with anti-bot protection and session management."""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the scraping engine."""
        self.config = self._load_config(config_path)
        self.logger = self._setup_logging()
        self.user_agent = UserAgent()
        self.session = None
        self.cloudscraper_session = None
        self.driver = None
        self.results: List[ScrapingResult] = []
        
        # Initialize sessions
        self._init_sessions()
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            self.logger.error(f"Configuration file {config_path} not found")
            return {}
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler("logs/scraper.log")
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _init_sessions(self):
        """Initialize HTTP sessions with anti-bot protection."""
        # Regular requests session
        self.session = requests.Session()
        
        # CloudScraper session for Cloudflare protection
        self.cloudscraper_session = cloudscraper.create_scraper()
        
        # Set up proxy if configured
        proxies = self._get_proxy()
        if proxies:
            self.session.proxies.update(proxies)
            self.cloudscraper_session.proxies.update(proxies)
    
    def _get_proxy(self) -> Optional[Dict[str, str]]:
        """Get a random proxy from configuration."""
        http_proxies = self.config.get('proxies', {}).get('http_proxies', [])
        socks_proxies = self.config.get('proxies', {}).get('socks_proxies', [])
        
        all_proxies = http_proxies + socks_proxies
        
        if not all_proxies:
            return None
        
        proxy = random.choice(all_proxies)
        
        if proxy.startswith('http'):
            return {'http': proxy, 'https': proxy}
        elif proxy.startswith('socks'):
            return {'http': proxy, 'https': proxy}
        
        return None
    
    def _get_user_agent(self) -> str:
        """Get a random user agent."""
        if self.config.get('anti_bot', {}).get('rotate_user_agents', True):
            return self.user_agent.random
        return self.user_agent.chrome
    
    def _init_selenium_driver(self) -> webdriver.Chrome:
        """Initialize Selenium WebDriver with anti-detection."""
        options = Options()
        
        if self.config.get('anti_bot', {}).get('headless', True):
            options.add_argument('--headless')
        
        # Anti-detection options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # Set user agent
        options.add_argument(f'--user-agent={self._get_user_agent()}')
        
        # Proxy support
        proxy = self._get_proxy()
        if proxy and 'http' in proxy:
            proxy_url = proxy['http'].replace('http://', '')
            options.add_argument(f'--proxy-server={proxy_url}')
        
        try:
            if self.config.get('anti_bot', {}).get('use_undetected_chrome', True):
                driver = uc.Chrome(options=options)
            else:
                driver = webdriver.Chrome(options=options)
            
            # Execute script to remove webdriver property
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
        except Exception as e:
            self.logger.error(f"Failed to initialize WebDriver: {e}")
            return None
    
    def make_request(self, url: str, method: str = 'GET', 
                    use_cloudscraper: bool = False, 
                    use_selenium: bool = False, **kwargs) -> Optional[requests.Response]:
        """Make HTTP request with anti-bot protection."""
        headers = kwargs.get('headers', {})
        headers.update({
            'User-Agent': self._get_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        kwargs['headers'] = headers
        
        # Add delay between requests
        delay = self.config.get('general', {}).get('request_delay', 2)
        time.sleep(random.uniform(delay * 0.5, delay * 1.5))
        
        try:
            if use_selenium:
                return self._selenium_request(url)
            elif use_cloudscraper:
                response = self.cloudscraper_session.request(method, url, **kwargs)
            else:
                response = self.session.request(method, url, **kwargs)
            
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed for {url}: {e}")
            return None
    
    def _selenium_request(self, url: str) -> Optional[requests.Response]:
        """Make request using Selenium WebDriver."""
        if not self.driver:
            self.driver = self._init_selenium_driver()
        
        if not self.driver:
            return None
        
        try:
            self.driver.get(url)
            time.sleep(random.uniform(2, 5))  # Random wait
            
            # Create a mock response object
            class MockResponse:
                def __init__(self, content, url):
                    self.content = content.encode('utf-8')
                    self.text = content
                    self.url = url
                    self.status_code = 200
                
                def raise_for_status(self):
                    pass
            
            return MockResponse(self.driver.page_source, url)
            
        except Exception as e:
            self.logger.error(f"Selenium request failed for {url}: {e}")
            return None
    
    def parse_html(self, html_content: str, url: str = "") -> BeautifulSoup:
        """Parse HTML content using BeautifulSoup."""
        return BeautifulSoup(html_content, 'lxml')
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                self.logger.error(f"Error closing WebDriver: {e}")
        
        if self.session:
            self.session.close()
        
        if self.cloudscraper_session:
            self.cloudscraper_session.close()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
