#!/usr/bin/env python3
"""
Test ScraperAPI with basic Manta URLs to verify it works
"""

import requests
import time

def test_scraperapi_basic():
    """Test ScraperAPI with basic Manta pages first."""
    print("🧪 Testing ScraperAPI with Basic Manta URLs")
    print("=" * 50)
    
    api_key = "89a467f0dd31dee55c2aaf9fa1fc0645"
    scraper_api_url = "https://api.scraperapi.com/"
    
    # Test URLs - start with main pages, then try search pages
    test_urls = [
        "https://www.manta.com/",
        "https://www.manta.com/c/restaurants",
        "https://www.manta.com/c/restaurants/houston-tx",
        "https://www.manta.com/search?q=restaurant&location=houston+tx",
        "https://www.manta.com/mb_54_C4_000/restaurant/houston_tx"
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🔍 Test {i}/{len(test_urls)}: {url}")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            # Basic ScraperAPI request
            payload = {
                'api_key': api_key,
                'url': url,
                'render': 'false',
                'country_code': 'US'
            }
            
            print(f"   📡 Making ScraperAPI request...")
            response = requests.get(scraper_api_url, params=payload, timeout=30)
            duration = time.time() - start_time
            
            print(f"   📊 Status Code: {response.status_code}")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
            print(f"   📄 Response Length: {len(response.text)} characters")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS!")
                
                # Check content
                content = response.text.lower()
                if 'cloudflare' in content or 'just a moment' in content:
                    print(f"   🛡️  Cloudflare challenge detected")
                elif 'manta' in content:
                    print(f"   🎯 Manta content detected - ScraperAPI working!")
                    
                    # Look for business listings
                    if 'business' in content or 'company' in content:
                        print(f"   🏢 Business listings found in content")
                    
                    # Save sample for analysis
                    filename = f"scraperapi_sample_{i}.html"
                    with open(filename, 'w') as f:
                        f.write(response.text)
                    print(f"   📁 Sample saved to: {filename}")
                    
                else:
                    print(f"   ⚠️  Unexpected content")
                    print(f"   📄 Preview: {response.text[:200]}...")
                    
            elif response.status_code == 404:
                print(f"   ❌ URL not found - may need different URL format")
            elif response.status_code == 403:
                print(f"   🛡️  Forbidden - still blocked despite ScraperAPI")
            else:
                print(f"   ❌ Failed with status {response.status_code}")
                print(f"   📄 Response: {response.text[:200]}...")
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"   ❌ ERROR: {e}")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
        
        # Small delay between requests
        if i < len(test_urls):
            time.sleep(2)
    
    print(f"\n🎯 BASIC SCRAPERAPI TEST COMPLETE")
    print("=" * 50)
    
    # Now test with different ScraperAPI parameters
    print(f"\n🔧 Testing Different ScraperAPI Parameters")
    print("-" * 50)
    
    # Use the most promising URL from above tests
    test_url = "https://www.manta.com/c/restaurants/houston-tx"
    
    parameter_tests = [
        {
            'name': 'Basic Request',
            'params': {
                'api_key': api_key,
                'url': test_url,
                'render': 'false'
            }
        },
        {
            'name': 'With JavaScript Rendering',
            'params': {
                'api_key': api_key,
                'url': test_url,
                'render': 'true',
                'wait_for': '3000'
            }
        },
        {
            'name': 'Premium Residential IP',
            'params': {
                'api_key': api_key,
                'url': test_url,
                'render': 'false',
                'premium': 'true',
                'country_code': 'US'
            }
        },
        {
            'name': 'With Session',
            'params': {
                'api_key': api_key,
                'url': test_url,
                'render': 'false',
                'session_number': '123',
                'keep_headers': 'true'
            }
        }
    ]
    
    for i, test in enumerate(parameter_tests, 1):
        print(f"\n🧪 Parameter Test {i}/{len(parameter_tests)}: {test['name']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            response = requests.get(scraper_api_url, params=test['params'], timeout=45)
            duration = time.time() - start_time
            
            print(f"   📊 Status: {response.status_code}")
            print(f"   ⏱️  Duration: {duration:.2f}s")
            print(f"   📄 Length: {len(response.text)} chars")
            
            if response.status_code == 200:
                content = response.text.lower()
                if 'cloudflare' in content:
                    print(f"   🛡️  Still getting Cloudflare challenge")
                elif 'manta' in content and 'business' in content:
                    print(f"   ✅ SUCCESS - Got Manta business content!")
                elif 'manta' in content:
                    print(f"   🎯 Got Manta content (may need parsing)")
                else:
                    print(f"   ⚠️  Unexpected content")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            duration = time.time() - start_time
            print(f"   ❌ ERROR: {e}")
            print(f"   ⏱️  Duration: {duration:.2f}s")
        
        time.sleep(3)  # Longer delay for parameter tests
    
    print(f"\n🎉 SCRAPERAPI PARAMETER TEST COMPLETE")

def main():
    """Main test function."""
    print("🌐 ScraperAPI Basic Integration Test")
    print("Testing if ScraperAPI can access Manta at all")
    print()
    
    test_scraperapi_basic()
    
    print(f"\n💡 NEXT STEPS:")
    print("1. If any tests succeeded, use those URL patterns")
    print("2. If all failed, check ScraperAPI account status")
    print("3. Try different Manta URL formats")
    print("4. Consider using ScraperAPI for main pages, then extract business URLs")

if __name__ == '__main__':
    main()
