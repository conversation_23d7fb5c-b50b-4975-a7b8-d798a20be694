#!/usr/bin/env python3
"""
Test Manta Browser API - Verify the browser-based approach works with your curl command.
"""

import asyncio
import sys
import os
from datetime import datetime
from playwright.async_api import async_playwright

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_manta_browser_api():
    """Test Manta API using browser context with your working curl command."""
    print("🏢 Testing Manta Browser API")
    print("=" * 50)
    print("Using the same approach as your working curl command")
    print("URL: https://www.manta.com/more-results/54_B83AE_KCQ/dolls_and_stuffed_toys?pg=2")
    print()
    
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(
            headless=False,  # Set to False to see what's happening
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage'
            ]
        )
        
        # Create realistic browser context
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York'
        )
        
        # Add stealth script
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        page = await context.new_page()
        
        try:
            # Step 1: Navigate to Manta to establish session
            print("🌐 Navigating to Manta...")
            await page.goto("https://www.manta.com", wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # Step 2: Handle any modals/popups
            print("✅ Handling popups...")
            try:
                accept_button = await page.query_selector('button:has-text("Accept"), button:has-text("OK"), .cookie-accept')
                if accept_button:
                    await accept_button.click()
                    print("✅ Accepted cookies/terms")
                    await page.wait_for_timeout(2000)
            except:
                pass
            
            # Step 3: Make the API call using your exact URL pattern
            print("📡 Making Manta API call...")
            
            # Your exact API URL from the curl command
            api_url = "https://www.manta.com/more-results/54_B83AE_KCQ/dolls_and_stuffed_toys?pg=2"
            
            # Make the API call using browser's fetch API with your headers
            api_response = await page.evaluate(f"""
                async () => {{
                    try {{
                        const response = await fetch('{api_url}', {{
                            method: 'GET',
                            headers: {{
                                'Accept': '*/*',
                                'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                                'Cache-Control': 'no-cache',
                                'Connection': 'keep-alive',
                                'Pragma': 'no-cache',
                                'Referer': 'https://www.manta.com/mb_54_B83AE_KCQ/dolls_and_stuffed_toys/dallas_tx',
                                'Sec-Fetch-Dest': 'empty',
                                'Sec-Fetch-Mode': 'cors',
                                'Sec-Fetch-Site': 'same-origin',
                                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                                'sec-ch-ua-mobile': '?1',
                                'sec-ch-ua-platform': '"Android"',
                                'x-request-id': Math.random().toString(36).substring(2, 15)
                            }}
                        }});
                        
                        if (response.ok) {{
                            const text = await response.text();
                            return {{ success: true, data: text, status: response.status, length: text.length }};
                        }} else {{
                            const text = await response.text();
                            return {{ success: false, error: text, status: response.status }};
                        }}
                    }} catch (error) {{
                        return {{ success: false, error: error.message, status: 0 }};
                    }}
                }}
            """)
            
            await browser.close()
            
            if api_response['success']:
                print(f"✅ Manta API call successful!")
                print(f"📊 Status: {api_response['status']}")
                print(f"📄 Response length: {api_response['length']} characters")
                
                # Parse the HTML response
                html_data = api_response['data']
                businesses = parse_manta_response(html_data)
                
                print(f"🏢 Found {len(businesses)} businesses")
                
                if businesses:
                    print(f"\n📋 Sample businesses:")
                    for i, business in enumerate(businesses[:3], 1):
                        print(f"   {i}. {business.get('name', 'N/A')}")
                        if business.get('phone'):
                            print(f"      Phone: {business['phone']}")
                        if business.get('address'):
                            print(f"      Address: {business['address']}")
                        print()
                
                # Save response for analysis
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"manta_api_response_{timestamp}.html"
                
                with open(filename, 'w') as f:
                    f.write(html_data)
                
                print(f"📁 Raw response saved to: {filename}")
                return businesses
                
            else:
                print(f"❌ Manta API call failed!")
                print(f"📊 Status: {api_response['status']}")
                print(f"❌ Error: {api_response['error'][:200]}...")
                return []
                
        except Exception as e:
            print(f"❌ Browser API call failed: {e}")
            await browser.close()
            return []

def parse_manta_response(html_data: str):
    """Parse Manta HTML response to extract business data."""
    businesses = []
    
    try:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_data, 'html.parser')
        
        # Look for business listings
        selectors = [
            '.business-card',
            '.listing-item',
            '.company-listing', 
            '[data-business-id]',
            '.search-result',
            '.result-item',
            'div[class*="business"]',
            'div[class*="company"]',
            'div[class*="listing"]'
        ]
        
        business_elements = []
        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                business_elements = elements
                print(f"   Found elements with selector: {selector}")
                break
        
        if not business_elements:
            # Try to find any structured data
            business_elements = soup.find_all('div', class_=True)
            print(f"   Using fallback: found {len(business_elements)} div elements")
        
        for element in business_elements[:10]:  # Limit to first 10
            try:
                business = {}
                
                # Extract business name
                name_selectors = ['.business-name', '.company-name', '.listing-title', 'h2', 'h3', 'h4']
                for sel in name_selectors:
                    name_elem = element.select_one(sel)
                    if name_elem:
                        business['name'] = name_elem.get_text(strip=True)
                        break
                
                # Extract phone
                phone_selectors = ['.phone', '.tel', '[href^="tel:"]']
                for sel in phone_selectors:
                    phone_elem = element.select_one(sel)
                    if phone_elem:
                        business['phone'] = phone_elem.get_text(strip=True)
                        break
                
                # Extract address
                addr_selectors = ['.address', '.location', '.business-address']
                for sel in addr_selectors:
                    addr_elem = element.select_one(sel)
                    if addr_elem:
                        business['address'] = addr_elem.get_text(strip=True)
                        break
                
                # Only add if we have at least a name
                if business.get('name'):
                    businesses.append(business)
                    
            except Exception as e:
                print(f"   Error parsing business element: {e}")
                continue
        
    except Exception as e:
        print(f"Error parsing Manta response: {e}")
    
    return businesses

async def main():
    """Main test function."""
    print("🧪 Testing Manta Browser API with your working curl command")
    print("This will verify that the browser approach works with Manta's API")
    print()
    
    businesses = await test_manta_browser_api()
    
    if businesses:
        print(f"\n🎉 Manta browser API test successful!")
        print(f"🏢 Found {len(businesses)} businesses")
        print(f"💡 The browser approach bypasses Cloudflare protection!")
    else:
        print(f"\n⚠️  No businesses found")
        print(f"💡 This could be due to:")
        print(f"   - Different HTML structure than expected")
        print(f"   - API endpoint changes")
        print(f"   - Cloudflare still blocking requests")

if __name__ == '__main__':
    asyncio.run(main())
