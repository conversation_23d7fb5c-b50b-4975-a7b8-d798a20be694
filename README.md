# Business Owner Scraper

A comprehensive web scraping bot that extracts business owner information from multiple sources without using APIs. Designed to bypass anti-bot protections and efficiently gather owner/CEO information from various business directories and people search engines.

## Features

- **Multi-Source Scraping**: Extracts data from BBB.org, Manta.com, LinkedIn.com, TruePeopleSearch.com, and CyberBackgroundChecks.com
- **Anti-Bot Protection**: Bypasses Cloudflare protection and other anti-scraping measures
- **Proxy Support**: Configurable HTTP and SOCKS proxy rotation
- **Data Deduplication**: Intelligent deduplication based on similarity matching
- **Multiple Export Formats**: CSV, Excel, and finder-compatible formats
- **CLI Interface**: Easy-to-use command-line interface with interactive mode
- **Configurable**: Highly configurable through YAML configuration files

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd business-owner-scraper
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your settings (optional)
   ```

## Quick Start

### Basic Usage

```bash
# Scrape lawn care businesses in Dallas
python main.py --business-type "lawn care" --location "dallas tx"

# Scrape multiple business types and locations
python main.py -b "lawn care" -b "restaurant" -l "dallas tx" -l "houston tx"

# Use specific sources only
python main.py -b "lawn care" -l "dallas tx" -s bbb -s manta

# Export to Excel format
python main.py -b "lawn care" -l "dallas tx" --format xlsx

# Export in finder-compatible format
python main.py -b "lawn care" -l "dallas tx" --format finder
```

### Interactive Mode

```bash
python main.py --interactive
```

This will guide you through the scraping process step by step.

## Configuration

The scraper is configured through `config.yaml`. Key sections include:

### General Settings
```yaml
general:
  max_workers: 5
  request_delay: 2
  timeout: 30
  max_retries: 3
  output_format: "csv"
  output_directory: "./results"
```

### Anti-Bot Protection
```yaml
anti_bot:
  use_proxies: true
  rotate_user_agents: true
  use_cloudscraper: true
  use_undetected_chrome: true
  headless: true
```

### Proxy Configuration
```yaml
proxies:
  http_proxies:
    - "http://username:<EMAIL>:8080"
    - "http://username:<EMAIL>:8080"
  socks_proxies:
    - "socks5://username:<EMAIL>:1080"
```

### Data Sources
```yaml
sources:
  bbb:
    enabled: true
    max_pages: 5
  manta:
    enabled: true
    max_pages: 5
  # ... other sources
```

## Data Sources

### 1. Better Business Bureau (BBB.org)
- **Search Pattern**: `site:bbb.org "Owner" {business_type} {location}`
- **Data Extracted**: Business name, owner name, contact information
- **Protection Level**: Low

### 2. Manta.com
- **Search Pattern**: `site:manta.com "Owner" {business_type} {location}`
- **Data Extracted**: Business name, owner name, contact information
- **Protection Level**: Medium

### 3. LinkedIn.com
- **Search Pattern**: `site:linkedin.com ("Owner" OR "CEO") {business_type} {location}`
- **Data Extracted**: Owner name, business name, job title
- **Protection Level**: High (uses search engine results)

### 4. TruePeopleSearch.com
- **Search Pattern**: `site:truepeoplesearch.com "Owner" {business_type} {location}`
- **Data Extracted**: Person name, business associations
- **Protection Level**: High (Cloudflare protected)

### 5. CyberBackgroundChecks.com
- **Search Pattern**: `site:cyberbackgroundchecks.com "Owner" {business_type} {location}`
- **Data Extracted**: Person name, business associations
- **Protection Level**: High (Cloudflare protected)

## Output Formats

### CSV Format
Standard comma-separated values with columns:
- owner_name
- business_name
- business_type
- location
- source
- url
- phone
- email
- address
- scraped_at

### Excel Format
Multi-sheet Excel file with:
- **Business Owners**: Main results
- **Summary**: Statistics and metrics
- **Source Breakdown**: Results by source
- **Location Breakdown**: Results by location

### Finder-Compatible Format
Specially formatted Excel file compatible with the "finder" application with standardized column names and data formatting.

## Anti-Bot Protection Features

### Cloudflare Bypass
- **CloudScraper**: Primary method for bypassing Cloudflare
- **Selenium Fallback**: Undetected Chrome driver for complex challenges
- **Challenge Detection**: Automatic detection of protection pages

### Request Rotation
- **User Agent Rotation**: Random user agents from multiple browsers
- **Proxy Rotation**: Automatic proxy switching
- **Request Delays**: Configurable delays between requests
- **Session Management**: Persistent sessions with cookie handling

### Stealth Features
- **Browser Fingerprinting**: Mimics real browser behavior
- **JavaScript Execution**: Handles dynamic content
- **Header Randomization**: Realistic HTTP headers
- **Window Size Randomization**: Random browser window sizes

## Data Processing

### Cleaning and Standardization
- **Name Normalization**: Proper capitalization and formatting
- **Business Name Standardization**: Consistent entity suffixes (Inc., LLC, etc.)
- **Contact Information**: Phone number and email formatting
- **Address Standardization**: Consistent address formatting

### Deduplication
- **Similarity Matching**: Configurable similarity threshold (default: 80%)
- **Key Field Comparison**: Based on owner name and business name
- **Data Merging**: Combines information from duplicate records
- **Confidence Scoring**: Quality assessment for each result

## Usage Examples

### Example 1: Basic Scraping
```bash
python main.py --business-type "lawn care" --location "dallas tx" --format xlsx
```

### Example 2: Multiple Sources
```bash
python main.py \
  --business-type "restaurant" \
  --location "houston tx" \
  --sources bbb \
  --sources manta \
  --sources linkedin \
  --output restaurant_owners.csv
```

### Example 3: Batch Processing
```bash
python main.py \
  -b "lawn care" -b "plumbing" -b "electrical" \
  -l "dallas tx" -l "austin tx" -l "houston tx" \
  --format finder \
  --output texas_business_owners.xlsx
```

### Example 4: Custom Configuration
```bash
python main.py \
  --config custom_config.yaml \
  --business-type "construction" \
  --location "san antonio tx" \
  --verbose
```

## Troubleshooting

### Common Issues

1. **Cloudflare Blocking**
   - Enable proxy rotation in config
   - Increase request delays
   - Use undetected Chrome driver

2. **No Results Found**
   - Check if sources are enabled in config
   - Verify business type and location spelling
   - Try different search terms

3. **Rate Limiting**
   - Increase request delays
   - Enable proxy rotation
   - Reduce concurrent workers

4. **Export Errors**
   - Check output directory permissions
   - Ensure sufficient disk space
   - Verify file is not open in another application

### Logging

Enable verbose logging for debugging:
```bash
python main.py --verbose --business-type "lawn care" --location "dallas tx"
```

Logs are saved to `logs/scraper.log`.

## Legal and Ethical Considerations

- **Respect robots.txt**: Check website policies before scraping
- **Rate Limiting**: Use appropriate delays between requests
- **Data Usage**: Use scraped data responsibly and in compliance with applicable laws
- **Terms of Service**: Review and comply with website terms of service

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error messages
3. Open an issue on GitHub with detailed information

## Disclaimer

This tool is for educational and research purposes. Users are responsible for complying with applicable laws and website terms of service when using this software.
