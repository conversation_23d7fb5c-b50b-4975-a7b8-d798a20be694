#!/usr/bin/env python3
"""
Test Internal APIs - Demonstrates both TruthFinder and Manta internal API usage.
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__)) if '__file__' in globals() else os.getcwd()
sys.path.insert(0, os.path.join(current_dir, 'src'))

def test_internal_apis():
    """Test both TruthFinder and Manta internal APIs."""
    print("🚀 Internal APIs Test - TruthFinder & Manta")
    print("=" * 60)
    print("🎯 Testing internal API integrations for maximum data extraction")
    print("=" * 60)
    
    try:
        # Import modules
        from src.core import ScrapingEngine, ScrapingResult
        from src.scrapers.manta_api_scraper import MantaAPIScraper
        from src.scrapers.truthfinder_scraper import TruthFinderScraper
        from src.utils.data_processor import DataProcessor
        from src.exporters.excel_exporter import ExcelExporter
        
        print("✅ All internal API modules imported successfully")
        
        # Initialize engine
        print("\n🔧 Initializing scraping engine with internal APIs...")
        engine = ScrapingEngine("config.yaml")
        processor = DataProcessor(engine.config)
        excel_exporter = ExcelExporter(engine.config)
        
        # Test scenarios
        test_scenarios = [
            {
                'business_type': 'restaurant',
                'location': 'houston tx',
                'description': 'Restaurant owners via internal APIs'
            },
            {
                'business_type': 'construction',
                'location': 'dallas tx', 
                'description': 'Construction companies via internal APIs'
            }
        ]
        
        all_results = []
        
        # Test internal API scrapers
        api_scrapers = {
            'TruthFinder Internal API': TruthFinderScraper(engine),
            'Manta Internal API': MantaAPIScraper(engine)
        }
        
        for scenario_idx, scenario in enumerate(test_scenarios, 1):
            business_type = scenario['business_type']
            location = scenario['location']
            description = scenario['description']
            
            print(f"\n🧪 API TEST {scenario_idx}/2: {description}")
            print(f"   Business Type: {business_type}")
            print(f"   Location: {location}")
            print("-" * 50)
            
            scenario_results = []
            
            for api_name, scraper in api_scrapers.items():
                if not scraper.is_enabled():
                    print(f"   ⚠️  {api_name} is disabled, skipping...")
                    continue
                
                print(f"\n📡 Testing {api_name}...")
                
                try:
                    # Test the internal API
                    results = scraper.search(business_type, location)
                    
                    if results:
                        print(f"   ✅ {api_name}: {len(results)} results")
                        
                        # Display comprehensive data from first result
                        sample_result = results[0]
                        print(f"   📋 Sample API Data:")
                        print(f"      Owner: {sample_result.owner_name}")
                        print(f"      Business: {sample_result.business_name}")
                        print(f"      Phone: {sample_result.phone}")
                        print(f"      Email: {sample_result.email}")
                        print(f"      Address: {sample_result.address}")
                        print(f"      Street: {sample_result.street_address}")
                        print(f"      City: {sample_result.city}")
                        print(f"      State: {sample_result.state}")
                        print(f"      Zip: {sample_result.zip_code}")
                        print(f"      Website: {sample_result.website}")
                        print(f"      Description: {sample_result.business_description}")
                        print(f"      Years in Business: {sample_result.years_in_business}")
                        print(f"      Employee Count: {sample_result.employee_count}")
                        print(f"      Annual Revenue: {sample_result.annual_revenue}")
                        print(f"      Business Category: {sample_result.business_category}")
                        print(f"      LinkedIn: {sample_result.linkedin_url}")
                        print(f"      Facebook: {sample_result.facebook_url}")
                        print(f"      Business Structure: {sample_result.business_structure}")
                        print(f"      Owner Age: {sample_result.owner_age}")
                        print(f"      Executives: {len(sample_result.executives)} found")
                        print(f"      Previous Addresses: {len(sample_result.previous_addresses)} found")
                        print(f"      Previous Names: {len(sample_result.previous_names)} found")
                        print(f"      Confidence Score: {sample_result.confidence_score}")
                        print(f"      Data Quality: {sample_result.data_quality}")
                        print(f"      Verification: {sample_result.verification_status}")
                        
                        # Show raw data sample for APIs
                        if hasattr(sample_result, 'raw_data') and sample_result.raw_data:
                            raw_keys = list(sample_result.raw_data.keys())[:5]
                            print(f"      Raw Data Keys: {raw_keys}")
                        
                        scenario_results.extend(results)
                    else:
                        print(f"   ⚠️  {api_name}: No results found")
                        
                except Exception as e:
                    print(f"   ❌ {api_name} error: {e}")
                    continue
            
            all_results.extend(scenario_results)
            print(f"\n📊 Scenario {scenario_idx} Total: {len(scenario_results)} API results")
        
        print(f"\n🎯 INTERNAL APIs SUMMARY")
        print("=" * 40)
        print(f"📊 Total API Results: {len(all_results)}")
        
        if all_results:
            # Process results
            print("\n🔄 Processing API results...")
            processed_results = processor.process_results(all_results)
            
            print(f"✅ Processed {len(processed_results)} API results")
            
            # Export results
            print("\n📁 Exporting API results...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Export comprehensive Excel file
            excel_file = excel_exporter.export(processed_results, f"internal_apis_{timestamp}.xlsx")
            if excel_file:
                print(f"✅ Internal APIs Excel export: {excel_file}")
            
            # Export finder-compatible format
            finder_file = excel_exporter.export_finder_compatible(processed_results, f"internal_apis_finder_{timestamp}.xlsx")
            if finder_file:
                print(f"✅ Internal APIs Finder export: {finder_file}")
            
            # Display API-specific statistics
            print(f"\n📈 INTERNAL APIs DATA STATISTICS")
            print("=" * 45)
            
            # Source breakdown
            source_counts = {}
            for result in processed_results:
                source = result.source
                source_counts[source] = source_counts.get(source, 0) + 1
            
            print("📡 Results by API Source:")
            for source, count in source_counts.items():
                print(f"   {source}: {count} results")
            
            # Data richness stats
            stats = {
                'results_with_phone': len([r for r in processed_results if r.phone]),
                'results_with_email': len([r for r in processed_results if r.email]),
                'results_with_website': len([r for r in processed_results if r.website]),
                'results_with_detailed_address': len([r for r in processed_results if r.street_address and r.city]),
                'results_with_business_description': len([r for r in processed_results if r.business_description]),
                'results_with_years_in_business': len([r for r in processed_results if r.years_in_business]),
                'results_with_employee_count': len([r for r in processed_results if r.employee_count]),
                'results_with_annual_revenue': len([r for r in processed_results if r.annual_revenue]),
                'results_with_social_media': len([r for r in processed_results if r.linkedin_url or r.facebook_url]),
                'results_with_executives': len([r for r in processed_results if r.executives]),
                'results_with_previous_addresses': len([r for r in processed_results if r.previous_addresses]),
                'results_with_owner_age': len([r for r in processed_results if r.owner_age]),
                'api_verified_results': len([r for r in processed_results if r.verification_status == 'api_verified']),
                'high_confidence_results': len([r for r in processed_results if r.confidence_score and r.confidence_score > 0.8])
            }
            
            print("\n📊 Data Richness Metrics:")
            for stat_name, count in stats.items():
                percentage = (count / len(processed_results)) * 100 if processed_results else 0
                print(f"   {stat_name.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
            
            # API-specific insights
            truthfinder_results = [r for r in processed_results if r.source == 'truthfinder_api']
            manta_api_results = [r for r in processed_results if r.source == 'manta_api']
            
            print(f"\n🔍 API-Specific Insights:")
            if truthfinder_results:
                avg_confidence = sum(r.confidence_score or 0 for r in truthfinder_results) / len(truthfinder_results)
                print(f"   TruthFinder API: {len(truthfinder_results)} results, avg confidence: {avg_confidence:.2f}")
            
            if manta_api_results:
                avg_confidence = sum(r.confidence_score or 0 for r in manta_api_results) / len(manta_api_results)
                print(f"   Manta API: {len(manta_api_results)} results, avg confidence: {avg_confidence:.2f}")
            
            print(f"\n🎉 Internal APIs test completed successfully!")
            print(f"📡 Both TruthFinder and Manta internal APIs provide rich, comprehensive data!")
            
        else:
            print("⚠️  No API results to process")
            print("💡 This could be due to:")
            print("   - APIs being blocked by anti-bot protection")
            print("   - API keys/sessions needing refresh")
            print("   - Network connectivity issues")
            print("   - API endpoints being changed")
        
    except Exception as e:
        print(f"❌ Internal APIs test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_internal_apis()
