#!/usr/bin/env python3
"""
Example usage of the Business Owner Scraper.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.core import ScrapingEngine
from src.scrapers.bbb_scraper import BBBScraper
from src.scrapers.manta_scraper import MantaScraper
from src.utils.data_processor import DataProcessor
from src.exporters.csv_exporter import CSVExporter
from src.exporters.excel_exporter import ExcelExporter


def example_basic_scraping():
    """Example of basic scraping workflow."""
    print("🔄 Example: Basic Scraping Workflow")
    print("=" * 50)
    
    # Initialize engine
    engine = ScrapingEngine("config.yaml")
    
    try:
        # Initialize scrapers
        bbb_scraper = BBBScraper(engine)
        manta_scraper = MantaScraper(engine)
        
        # Scrape from BBB
        print("📋 Scraping BBB for lawn care businesses in Dallas...")
        bbb_results = bbb_scraper.search("lawn care", "dallas tx")
        print(f"   Found {len(bbb_results)} results from BBB")
        
        # Scrape from Manta
        print("📋 Scraping Manta for lawn care businesses in Dallas...")
        manta_results = manta_scraper.search("lawn care", "dallas tx")
        print(f"   Found {len(manta_results)} results from Manta")
        
        # Combine results
        all_results = bbb_results + manta_results
        print(f"📊 Total raw results: {len(all_results)}")
        
        # Process results
        processor = DataProcessor(engine.config)
        processed_results = processor.process_results(all_results)
        print(f"📊 Processed results: {len(processed_results)}")
        
        # Export to CSV
        csv_exporter = CSVExporter(engine.config)
        csv_file = csv_exporter.export(processed_results, "example_results.csv")
        print(f"📁 CSV exported to: {csv_file}")
        
        # Export to Excel
        excel_exporter = ExcelExporter(engine.config)
        excel_file = excel_exporter.export(processed_results, "example_results.xlsx")
        print(f"📁 Excel exported to: {excel_file}")
        
        print("✅ Example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        engine.cleanup()


def example_custom_configuration():
    """Example of using custom configuration."""
    print("\n🔄 Example: Custom Configuration")
    print("=" * 50)
    
    # Custom configuration
    custom_config = {
        'general': {
            'max_workers': 3,
            'request_delay': 1,
            'timeout': 20,
            'output_directory': './custom_results'
        },
        'sources': {
            'bbb': {
                'enabled': True,
                'base_url': 'https://www.bbb.org',
                'max_pages': 2
            },
            'manta': {
                'enabled': False  # Disable Manta for this example
            }
        },
        'output': {
            'deduplication': {
                'enabled': True,
                'similarity_threshold': 0.9  # Higher threshold
            }
        }
    }
    
    # Create custom config file
    import yaml
    with open('custom_config.yaml', 'w') as f:
        yaml.dump(custom_config, f)
    
    # Use custom config
    engine = ScrapingEngine("custom_config.yaml")
    
    try:
        bbb_scraper = BBBScraper(engine)
        
        if bbb_scraper.is_enabled():
            print("📋 Scraping with custom configuration...")
            results = bbb_scraper.search("restaurant", "houston tx")
            print(f"   Found {len(results)} results")
            
            # Process and export
            processor = DataProcessor(engine.config)
            processed_results = processor.process_results(results)
            
            csv_exporter = CSVExporter(engine.config)
            csv_file = csv_exporter.export(processed_results, "custom_results.csv")
            print(f"📁 Results exported to: {csv_file}")
        else:
            print("⚠️  BBB scraper is disabled in custom config")
        
        print("✅ Custom configuration example completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        engine.cleanup()
        # Clean up custom config file
        if os.path.exists('custom_config.yaml'):
            os.remove('custom_config.yaml')


def example_data_processing():
    """Example of data processing and deduplication."""
    print("\n🔄 Example: Data Processing and Deduplication")
    print("=" * 50)
    
    from src.core import ScrapingResult
    from datetime import datetime
    
    # Create sample results with duplicates
    sample_results = [
        ScrapingResult(
            owner_name="John Smith",
            business_name="Smith Lawn Care",
            business_type="lawn care",
            location="dallas tx",
            source="bbb",
            phone="(*************"
        ),
        ScrapingResult(
            owner_name="john smith",  # Duplicate with different case
            business_name="Smith Lawn Care Inc",
            business_type="lawn care",
            location="dallas tx",
            source="manta",
            email="<EMAIL>"
        ),
        ScrapingResult(
            owner_name="Jane Doe",
            business_name="Doe's Restaurant",
            business_type="restaurant",
            location="houston tx",
            source="bbb"
        ),
        ScrapingResult(
            owner_name="Bob Johnson",
            business_name="Johnson Construction",
            business_type="construction",
            location="austin tx",
            source="manta",
            phone="(*************"
        )
    ]
    
    print(f"📊 Sample data: {len(sample_results)} raw results")
    
    # Process results
    config = {
        'output': {
            'deduplication': {
                'enabled': True,
                'similarity_threshold': 0.8,
                'key_fields': ['owner_name', 'business_name']
            }
        }
    }
    
    processor = DataProcessor(config)
    processed_results = processor.process_results(sample_results)
    
    print(f"📊 After processing: {len(processed_results)} unique results")
    
    # Display results
    for i, result in enumerate(processed_results, 1):
        print(f"\n{i}. {result.owner_name or 'N/A'}")
        print(f"   Business: {result.business_name or 'N/A'}")
        print(f"   Type: {result.business_type or 'N/A'}")
        print(f"   Location: {result.location or 'N/A'}")
        print(f"   Phone: {result.phone or 'N/A'}")
        print(f"   Email: {result.email or 'N/A'}")
        print(f"   Source: {result.source or 'N/A'}")
        if result.raw_data:
            print(f"   Confidence: {result.raw_data.get('confidence_score', 'N/A')}")
            print(f"   Quality: {result.raw_data.get('data_quality', 'N/A')}")
    
    print("\n✅ Data processing example completed!")


def example_export_formats():
    """Example of different export formats."""
    print("\n🔄 Example: Export Formats")
    print("=" * 50)
    
    from src.core import ScrapingResult
    
    # Create sample results
    sample_results = [
        ScrapingResult(
            owner_name="Alice Johnson",
            business_name="Johnson's Bakery",
            business_type="bakery",
            location="dallas tx",
            source="bbb",
            phone="(*************",
            email="<EMAIL>",
            address="123 Main St, Dallas, TX 75201"
        ),
        ScrapingResult(
            owner_name="Mike Wilson",
            business_name="Wilson Auto Repair",
            business_type="auto repair",
            location="houston tx",
            source="manta",
            phone="(*************"
        )
    ]
    
    config = {
        'general': {
            'output_directory': './example_exports'
        },
        'output': {
            'csv_columns': [
                'owner_name', 'business_name', 'business_type', 'location',
                'source', 'phone', 'email', 'address', 'scraped_at'
            ]
        }
    }
    
    # CSV Export
    csv_exporter = CSVExporter(config)
    csv_file = csv_exporter.export(sample_results, "format_example.csv")
    print(f"📁 CSV file: {csv_file}")
    
    # Excel Export
    excel_exporter = ExcelExporter(config)
    excel_file = excel_exporter.export(sample_results, "format_example.xlsx")
    print(f"📁 Excel file: {excel_file}")
    
    # Finder-compatible Export
    finder_file = excel_exporter.export_finder_compatible(sample_results, "finder_example.xlsx")
    print(f"📁 Finder-compatible file: {finder_file}")
    
    # Summary Export
    summary_file = csv_exporter.export_summary(sample_results, "summary_example.csv")
    print(f"📁 Summary file: {summary_file}")
    
    print("✅ Export formats example completed!")


if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    os.makedirs('example_exports', exist_ok=True)
    
    print("🤖 Business Owner Scraper - Usage Examples")
    print("=" * 60)
    
    try:
        # Run examples
        example_basic_scraping()
        example_custom_configuration()
        example_data_processing()
        example_export_formats()
        
        print("\n🎉 All examples completed successfully!")
        print("\nNext steps:")
        print("1. Review the generated files in ./results and ./example_exports")
        print("2. Modify config.yaml for your specific needs")
        print("3. Run: python main.py --interactive")
        
    except KeyboardInterrupt:
        print("\n🛑 Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()
