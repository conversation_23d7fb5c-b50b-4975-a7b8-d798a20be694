#!/usr/bin/env python3
"""
Test script for the updated TruthFinder integration with existing cookie system.

This script tests that the TruthFinder scraper properly uses the existing
cookie extraction and session management system.
"""

import sys
import os
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_session_data_loading():
    """Test loading session data from existing files."""
    print("🧪 Testing Session Data Loading")
    print("=" * 40)
    
    try:
        from src.scrapers.truthfinder_browser_scraper import TruthFinderBrowserScraper
        from src.core import ScrapingEngine
        
        # Initialize the scraper
        engine = ScrapingEngine("config.yaml")
        scraper = TruthFinderBrowserScraper(engine)
        
        print("✅ TruthFinder scraper initialized")
        
        # Test loading session data
        session_data = scraper._load_fresh_session_data()
        
        if session_data:
            print(f"✅ Loaded session data with {len(session_data)} keys")
            
            # Check for important keys
            important_keys = ['cookies', 'user_agent', 'headers', 'generated_at']
            for key in important_keys:
                if key in session_data:
                    if key == 'cookies':
                        print(f"   📋 {key}: {len(session_data[key])} cookies")
                        # Show some cookie names
                        cookie_names = list(session_data[key].keys())[:5]
                        print(f"      Sample cookies: {', '.join(cookie_names)}")
                    elif key == 'headers':
                        print(f"   📋 {key}: {len(session_data[key])} headers")
                    else:
                        value = str(session_data[key])
                        print(f"   📋 {key}: {value[:50]}{'...' if len(value) > 50 else ''}")
                else:
                    print(f"   ⚠️  {key}: Not found")
        else:
            print("⚠️  No session data found")
            print("💡 Run: python get_fresh_cookies.py truthfinder --automated")
            print("   Or: python extract_sessions.py truthfinder")
        
        return session_data is not None
        
    except Exception as e:
        print(f"❌ Session data loading test failed: {e}")
        return False

def test_cookie_refresh_system():
    """Test the cookie refresh system."""
    print("\n🔄 Testing Cookie Refresh System")
    print("=" * 40)
    
    try:
        from src.scrapers.truthfinder_browser_scraper import TruthFinderBrowserScraper
        from src.core import ScrapingEngine
        
        # Initialize the scraper
        engine = ScrapingEngine("config.yaml")
        scraper = TruthFinderBrowserScraper(engine)
        
        print("✅ TruthFinder scraper initialized")
        
        # Check if get_fresh_cookies.py exists
        script_path = os.path.join(os.getcwd(), "get_fresh_cookies.py")
        if os.path.exists(script_path):
            print("✅ get_fresh_cookies.py script found")
            
            # Test the refresh method (but don't actually run it to avoid delays)
            print("📋 Cookie refresh system is available")
            print("💡 To test refresh: scraper._refresh_truthfinder_cookies()")
            
            return True
        else:
            print("❌ get_fresh_cookies.py script not found")
            return False
        
    except Exception as e:
        print(f"❌ Cookie refresh test failed: {e}")
        return False

def test_session_directory_structure():
    """Test the session directory structure."""
    print("\n📁 Testing Session Directory Structure")
    print("=" * 40)
    
    try:
        sessions_dir = os.path.join(os.getcwd(), "sessions")
        
        if os.path.exists(sessions_dir):
            print("✅ Sessions directory exists")
            
            # List session files
            files = os.listdir(sessions_dir)
            truthfinder_files = [f for f in files if 'truthfinder' in f.lower()]
            
            print(f"📋 Total files in sessions/: {len(files)}")
            print(f"📋 TruthFinder-related files: {len(truthfinder_files)}")
            
            if truthfinder_files:
                print("   TruthFinder session files:")
                for file in truthfinder_files[:5]:  # Show first 5
                    file_path = os.path.join(sessions_dir, file)
                    file_size = os.path.getsize(file_path)
                    mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    print(f"   - {file} ({file_size} bytes, {mod_time.strftime('%Y-%m-%d %H:%M')})")
            
            return True
        else:
            print("⚠️  Sessions directory not found")
            print("💡 Create it with: mkdir sessions")
            return False
        
    except Exception as e:
        print(f"❌ Session directory test failed: {e}")
        return False

def test_people_search_with_session_system():
    """Test people search with the session system."""
    print("\n👤 Testing People Search with Session System")
    print("=" * 40)
    
    try:
        from src.people_search import PersonSearchEngine, PersonSearchQuery
        
        # Initialize the engine
        engine = PersonSearchEngine("config.yaml")
        
        print("✅ People search engine initialized")
        
        # Check if TruthFinder scraper is available
        if 'truthfinder_browser' in engine.people_scrapers:
            scraper = engine.people_scrapers['truthfinder_browser']
            print("✅ TruthFinder browser scraper available in people search")
            
            # Test session data loading
            session_data = scraper._load_fresh_session_data()
            if session_data:
                print("✅ Session data loaded successfully")
                print(f"   📋 Cookies available: {len(session_data.get('cookies', {}))}")
                print(f"   📋 Headers available: {len(session_data.get('headers', {}))}")
            else:
                print("⚠️  No session data available")
            
            return True
        else:
            print("❌ TruthFinder browser scraper not available")
            return False
        
    except Exception as e:
        print(f"❌ People search test failed: {e}")
        return False

def show_usage_instructions():
    """Show instructions for using the cookie system."""
    print("\n💡 Usage Instructions")
    print("=" * 40)
    
    print("To use the TruthFinder people search with fresh cookies:")
    print("")
    print("1. 🍪 Extract fresh cookies:")
    print("   python get_fresh_cookies.py truthfinder --automated")
    print("   (or without --automated for manual browsing)")
    print("")
    print("2. 🔍 Or extract full session data:")
    print("   python extract_sessions.py truthfinder")
    print("")
    print("3. 👤 Run people search:")
    print("   python example_people_search.py")
    print("   (or use the Streamlit interface)")
    print("")
    print("4. 🔄 The system will automatically:")
    print("   - Load fresh cookies from sessions/ directory")
    print("   - Use proper headers from session data")
    print("   - Fall back to hardcoded cookies if needed")
    print("   - Refresh cookies automatically when they expire")
    print("")
    print("📁 Session files are stored in: sessions/")
    print("🔧 Cookie refresh happens automatically before each API call batch")

def main():
    """Run all cookie integration tests."""
    print("🚀 TruthFinder Cookie Integration Test Suite")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    tests = [
        test_session_directory_structure,
        test_session_data_loading,
        test_cookie_refresh_system,
        test_people_search_with_session_system
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"   ❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if passed == len(tests):
        print("🎉 All cookie integration tests passed!")
        print("")
        print("✅ The TruthFinder scraper is properly integrated with:")
        print("- Existing cookie extraction system (get_fresh_cookies.py)")
        print("- Session management system (extract_sessions.py)")
        print("- Automatic cookie refresh mechanism")
        print("- Fallback to hardcoded cookies when needed")
        print("")
        print("🚀 Ready to use! The people search will automatically:")
        print("1. Load fresh cookies from sessions/ directory")
        print("2. Use proper headers and user agents")
        print("3. Refresh cookies when they expire")
        print("4. Make successful API calls to TruthFinder")
    else:
        print("⚠️  Some integration tests failed.")
        print("")
        print("🔧 Next steps:")
        if failed > 0:
            print("1. Check that sessions/ directory exists")
            print("2. Run cookie extraction: python get_fresh_cookies.py truthfinder")
            print("3. Verify the session files are created properly")
    
    # Always show usage instructions
    show_usage_instructions()
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
