<!DOCTYPE html>
<html lang="en">
<head><script>(function(w,i,g){w[g]=w[g]||[];if(typeof w[g].push=='function')w[g].push(i)})
(window,'GTM-R7B4','google_tags_first_party');</script><script>(function(w,d,s,l){w[l]=w[l]||[];(function(){w[l].push(arguments);})('set', 'developer_id.dY2E1Nz', true);
		var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s);j.async=true;j.src='/pxhh/';
		f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer');</script>
  <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="For over 20 years, <PERSON><PERSON> has been America’s leading small business search directory. Find a local business in your neighborhood to support!">
<title>America’s Business Directory for Over 20 Years | Manta</title>
<link rel="icon" href="https://cc3.manta-r3.com/assets-gz/2e2a97f56/img/favicon.png" type="image/png">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com">
<link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/content/css/app.css">
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/content/css/fa.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/content/css/fa.css"></noscript>
<style>
  iframe[name=d_ifrm] {
  display: none;
  }
</style>
        <script rel="gtm-render">
      var screenWidth = screen.width;
      var sSz = 'lg';
      var wsSz = 'lg';
      var wSz = 'lg';
      if (screenWidth < 768) {
        sSz = wsSz = wSz = 'xs';
      } else if (screenWidth < 992) {
        sSz = wsSz = wSz = 'sm';
      } else if (screenWidth < 1200) {
        sSz = wsSz = wSz = 'md';
      }

      var gtmData = {
        ua_property: "UA-10299948-11",
        googleExperimentId: "",
        googleExperimentVariation: "-1",
        pageTitle: "America’s Business Directory for Over 20 Years | Manta",
        page_type: "company-content", // TODO: make this work when we do search/browse
        is_pagespeed: Boolean('false'),

        
        visitor_id: "fc94ac57-131b-4a7d-a33a-b12963335ad5",
        customer_segment: "consumer",
        page_depth: "1",
        scr_win_width: sSz + '-' + wSz,

        
        treatment: "no-test",

                  altTreatment1: "LM $49 Price Test CONTROL",
                  altTreatment2: "",
                  altTreatment3: "",
        
        ip: "**************",

                  // Older cookies might not have stateAbbrv and countryAbbrv,
          // so fall back to state and country
          user_state: "VA",
          user_country: "US",
        
        url_hash: window.location.hash,
        timestamp: new Date().toString(),
        sbi: "false",
        statusCode: "200",  // TODO: actual status
      };

              var dataLayer = [gtmData];
      
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-R7B4');
    </script>
    <script>
      var gaTrack = function (category, action, label, value, interactive) {
        if (arguments.length === 1) {
          // Non-event tracking stuff
          dataLayer.push(category);
          return;
        } else if (arguments.length === 3) {
          value = 1;
          interactive = true;
        }
        if (arguments.length === 4) {
          if (typeof value === 'boolean') {
            interactive = value;
            value = 1;
          } else {
            interactive = true;
          }
        }

        // if label is an object, serialize it into name=value pairs
        if (typeof label === 'object' && !Array.isArray(label)) {
          var pairs = [];
          for (var key in label) {
            pairs.push(key + '=' + label[key]);
          }
          label = pairs.join(',');
        }

        dataLayer.push({
          eventData: {
            category: category,
            action: action,
            label: label,
            value: value
          },
          event: (interactive ? 'interactive' : 'non-interactive') + ' event'
        });
      };
    </script>
  <script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/cash.min.js"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/js.cookie.min.js" onload="Cookies.defaults = { path: '/', domain: '.manta.com' }"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/axios.min.js" onload="window.axios = window.redaxios"></script>
<script>var loadScript=(function(d,s,c,e,f){return function(u){if(c[u]){return c[u];}e=d.createElement(s);e.async=!0;e.src=u;f=d.getElementsByTagName(s)[0];f.parentNode.insertBefore(e,f);return c[u]=e;};})(document,'script',{});</script>
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css"></noscript>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/simpleLightbox.min.js"></script>
<script>
  window.__PRELOADED_STATE__ = {
    toggledFeatures: {"experimentId":"","experimentVariation":"-1","forceGzip":false,"adsenseChannel":"1","scrollTracking":false,"exitTracking":true,"fakeMixpanel":true,"fakeGa":false,"useSubscriptionTestMode":false,"yextAllowDuplicatesMode":false,"allowSurveys":true,"isOffHoursOverride":false,"isBusinessHoursOverride":false,"skipDittoVendorRequests":false,"marketplaceJobs":false,"claimCtaText":"Own This Business?","claimCtaColor":"btn-primary","useLongBundleDesc":false,"isSiteMaintenanceMode":false,"homeyouLeads":false,"updoptProdOverride":false,"enableUpdoptDossier":true,"adsenseSlotMobile":"1160948055","adsenseSlotDesktop":"5730748454","useFapiForSubscriptions":false,"popOverlayOnCheckout":true,"useBuilderToAdd":false,"checkoutTemplate":"checkout","buyerlink_treatmentA":true,"adsenseHeroPlacement":true,"refreshAds":false,"show300x250":true,"showTaboola":true,"showDesktopAdhesionBanner":true,"homeyouWidgetPlacement":"about","newStack":true,"similarBusinessesUp":true,"reducedAdDensity":true,"navAffiliateBusinessCreditAd":true,"trackNumbers":true,"homeyouNumberTest":true,"homeyouTrackingNumber":"+***********","unclaimedStatic":true,"includeGMBScan":true,"postponeMerchantScans":true,"embedYextDashboard":true,"showPaywallPage":false,"checkoutPaywallTreatment":"paywall-control","enableWebsiteAddOn":true,"useBriteVerify":true,"useGooglePlacesInClaimBuilder":true,"useGoogleMaps":true,"useGoogleAutocomplete":true,"enableFacebookSignIn":true,"enableGoogleSignIn":true,"logCookieErrors":true,"rightRailDoubleAds":true,"triggerDemoScheduler":true,"showCovid":true,"covidShow":true,"es_search":true,"es_related":true,"useNewMemberDashboard":true,"showDetailedDescription":true,"useInternationalSearch":true,"useNewEditPage":true,"useElasticMb":true,"showMarketStats":false,"useNewAnalyticsService":true,"useElasticWorld":true,"requireTermsOfService":true,"useTaboolaAds":false,"usePlaywire":true,"adSenseSearchPages":true,"adSenseProfilePages":true,"oldSurveyModal":true,"useRepSalesDashboard":true,"blockSICM":true,"useNewCheckout":true,"showBanner":false,"copyTextBanner":"Promo Banner","redeemByTextBanner":"Redeem by 19th march","expiryDateBanner":"03/18/2023","catchPhraseBanner":"Special Offer!","validForBanner":"hasDitto, hasLmReviews, hasWebsite, hasPpcAds, hasDisplayAds, hasFeaturelessPlan, freeUser","toggleUrlCheckout":true,"changeVersion3":true,"showReviewTile":false,"showAdapexAds":true},
    referral_source: '',
    abTreatment: 'no-test',
    gamNetworkCode: '6009',
    visitor: {"ip":"**************","id":"fc94ac57-131b-4a7d-a33a-b12963335ad5","pageDepth":1,"customerSegment":{"threshold":1,"id":"c","label":"consumer"},"smallBusinessInterest":false,"xri":"c602191559235f0da2e2fc589d8e07da"},
    pageComponents: {},
    clientIp: '**************',
    isCalifornia: false,
    isDev: false,
    env: 'production',
    member: undefined  };
</script>
<script>
  var cache = {};
  window.logError = function(e, info) {
    var lines = (e.stack || '').split('\n');
    var callsite = lines.length > 1 ? lines[1].match(/(app\.js:\d+:\d+)/) : null;
    var key = e.message + (callsite && callsite[1] ? ' at ' + callsite[1] : '');

    if (!cache[key]) {
      try {
        window.axios && axios.post('/fapi/errors', {
          message: e.message || 'Unknown error',
          stack: e.stack || 'No stack trace available',
          info: info,
                    userAgent: (window.navigator && window.navigator.userAgent) || 'unknown'
        }).catch(function() {});
      } catch (e) {
        // Obviously, this isn't async/await, so it won't catch the
        // axios call, but I just want to _assure_ we don't throw
        // from the onerror handler.
      }
    }
  };
  window.onerror = function(message, source, lineno, colno, error) {
    // Don't log errors that come from ads and crap like that
    if (source.indexOf('manta.com') > -1) {
      logError(error, { source: source, lineno: lineno, colno: colno });
    }
  };
</script>
<!-- The script below is going to be commented until we figure out what could be a better implementation talking about performance -->
<!-- <script defer src="//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js" importance="low"></script> --><script defer src="https://btloader.com/tag?o=5150306120761344&upapi=true"></script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>

      <script data-cfasync="false">
        window.ramp = window.ramp || {};
        window.ramp.que = window.ramp.que || [];
      </script>
    
    <script type="text/javascript">
      window.ramp = window.ramp || {};
      window.ramp.que = window.ramp.que || [];
    </script>
      <title>Your Trusted Digital Marketing Agency for Small Businesses | Manta</title>
  <link rel="canonical" href="https://manta.com">
  <link href="//cc3.manta-r3.com/dist/fc1ac3e4/css/glider.min.css" rel="stylesheet">
  
  <style>
  .lazy-img {
    filter: blur(10px);
  }

  .bg-image {
    background: linear-gradient(0deg, rgba(134, 135, 137, 0.15), rgba(134, 135, 137, 0.15)), url("//cc3.manta-r3.com/dist/fc1ac3e4/img/content/directorysearchbg-blur.webp");
  }

  @media screen and (min-width: 1500px) {
    .bg-image {
      background-image: 
        linear-gradient(0deg, rgba(134, 135, 137, 0.15), rgba(134, 135, 137, 0.15)), 
        url("//cc3.manta-r3.com/dist/fc1ac3e4/img/content/directorysearchlarger.png");
    }
  }

  @media screen and (min-width: 640px) {
    .bg-image-height {
      height: 600px;
    }
  }
  @media screen and (min-width: 1100px) {
    .bg-image-height {
      height: 700px;
    }
  }

  @media screen and (min-width: 1024px) {
    .seohvacbg-img-position {
      background-size: cover;
      background-position: 68% bottom;
    }
  }

  @media screen and (max-width: 639px) {
    .mobile-mb-4 {
      margin-bottom: 1rem;
    }
  }
  @media screen and (max-width: 320px) {
    .bg-image-height {
      height: 354px;
    }
  }

	@media screen and (max-width: 639px) {
		.search-services-menu {
				top: 102px;
				border-bottom: 2px solid #979BA3;
			}
		.search-location-menu {
			top: 50px;
			width: 100vw;
			left: -2.75rem;
			border-bottom: 2px solid #979BA3;
		}
	}
  .absolute-center {
    right: 50px;
    transform: translateY(-50%);
  }

  @media screen and (min-width: 1250px) {
    .show-desktop-ads {
      display: block;
    }
  }
  </style>
</head>
<body>
  <!-- homebrand -->
<svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" xmlns="http://www.w3.org/2000/svg"><defs><symbol id="homebrand" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 89" fill="none"><path d="m82.262 7.48 1.103-2.847h1.145V8.66h-.777V5.528L82.548 8.66h-.53l-1.186-3.132V8.66h-.735V4.633h1.144l1.021 2.847Zm-3.064-2.196h-1.225V8.66h-.777V5.284h-1.225v-.65h3.227v.65ZM44.593 31.434c-7.19 0-13.033 5.816-13.033 12.973 0 7.158 5.843 12.974 13.033 12.974 7.191 0 13.033-5.816 13.033-12.974 0-7.157-5.842-12.973-13.033-12.973Z" fill="#FF6B61"/><path d="M75.154 74.868c-1.635 1.627-4.25 1.627-5.843 0l-12.87-12.81c-8.252 5.53-19.569 4.676-26.882-2.603-8.335-8.337-8.335-21.717-.04-30.014 8.334-8.256 21.775-8.256 30.11 0 7.313 7.28 8.17 18.505 2.614 26.76l12.87 12.811a4.147 4.147 0 0 1 .04 5.856Zm13.972-30.867c-.122-11.225-4.412-22.409-13.033-30.95-17.404-17.365-45.635-17.365-63.04 0-17.404 17.325-17.404 45.428 0 62.793 8.58 8.54 19.815 12.852 31.091 12.974h41.264a3.74 3.74 0 0 0 3.76-3.742l-.042-41.075Z" fill="#FF6B61"/></symbol></defs></svg>
<!-- real estate -->
<svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" xmlns="http://www.w3.org/2000/svg"><defs><symbol id="realestate" viewBox="0 0 101 72" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="m56.696 39.18-23.731-.003 1.587 1.326a.69.69 0 0 1 .092.957.662.662 0 0 1-.517.247v.002h-2.816v17.86l25.074-.017v-16.92h-1.893a.677.677 0 0 1-.67-.683c0-.224.105-.424.27-.548l2.605-2.222v.001Zm29.07 31.17c.37 0 .671.306.671.683 0 .377-.3.683-.67.683H64.38a.677.677 0 0 1-.67-.683c0-.377.3-.683.67-.683h21.387Zm-35.598 0c.37 0 .67.306.67.683 0 .377-.3.683-.67.683h-31.76a.677.677 0 0 1-.671-.683c0-.377.3-.683.67-.683h31.761ZM12.262 66.66a.677.677 0 0 1-.67-.683c0-.377.3-.683.67-.683h78.585c.37 0 .67.306.67.683 0 .377-.3.683-.67.683H12.262Zm72.797-5.43a.675.675 0 0 1-.666-.682.675.675 0 0 1 .671-.678l4.43.026a.732.732 0 0 1-.018-.155L89.401 48c-1.008-.157-2.066-.65-3.647-1.46a17.24 17.24 0 0 1-2.43-1.494c-.783-.586-1.503-1.251-2.09-2.014-2.521-3.265-1.303-6.83-.08-10.404.356-1.038.71-2.075.965-3.106.295-1.198.582-2.437.865-3.673 1.622-7.033 3.2-13.877 6.922-13.76 3.688.117 5.264 6.952 6.878 13.958.288 1.248.577 2.502.876 3.712.254 1.03.609 2.069.964 3.106 1.224 3.574 2.442 7.14-.079 10.404a10.27 10.27 0 0 1-1.732 1.732 14.762 14.762 0 0 1-2.293 1.487c-1.65.876-2.746 1.377-3.78 1.522l.075 11.725a.672.672 0 0 1-.021.167l5.152.03a.675.675 0 0 1 .666.682.675.675 0 0 1-.67.678l-10.883-.062Zm4.301-19.663a.686.686 0 0 1-.05-.091l-3.83-8.44a.687.687 0 0 1 .329-.904.666.666 0 0 1 .889.333l2.642 5.822-.096-14.982a.675.675 0 0 1 .666-.683.674.674 0 0 1 .67.678l.055 8.64 2.631-5.803a.665.665 0 0 1 .89-.333.685.685 0 0 1 .328.904l-3.828 8.44.073 11.488c.806-.155 1.751-.604 3.17-1.355a13.433 13.433 0 0 0 2.089-1.348 8.99 8.99 0 0 0 1.502-1.504c2.07-2.681.971-5.891-.131-9.113-.369-1.079-.74-2.157-1.003-3.222-.303-1.231-.592-2.485-.88-3.733-1.495-6.49-2.954-12.822-5.608-12.907-2.63-.084-4.086 6.225-5.58 12.709a265.358 265.358 0 0 1-.872 3.693c-.263 1.065-.631 2.145-1.002 3.223-1.103 3.22-2.201 6.432-.131 9.113.51.66 1.14 1.244 1.83 1.758.704.526 1.472.981 2.24 1.376 1.34.688 2.253 1.122 3.034 1.292l-.032-5.05.005-.002Zm-31.634 17.99 17.966-.013V42.636H57.726v16.92Zm-.508 1.36a.66.66 0 0 1-.329 0l-26.192.018H4.07a.677.677 0 0 1-.67-.683v-18.54H.67A.677.677 0 0 1 0 41.03c0-.234.116-.44.292-.563L3.41 37.86V21.87l-2.045-.01a.675.675 0 0 1-.665-.683c0-.082.015-.159.04-.232H.738L6.056 6.279a.67.67 0 0 1 .63-.447H42.2L50.358.12a.664.664 0 0 1 .771.007l9.925 6.952V3.414c0-.377.3-.682.67-.682h7.786c.37 0 .67.305.67.682v10.058l3.653 2.558c.124.087.21.21.254.345a.694.694 0 0 1 .102.43l-.06 20.264 5.11 4.359a.69.69 0 0 1 .084.96.666.666 0 0 1-.514.245v.001h-1.787v17.587h-.003c0 .376-.299.68-.67.68l-19.143.013.012.002Zm11.628-48.384V4.097h-6.442V8.02l6.442 4.512ZM27.12 22 4.76 21.879v14.863l12.22-10.208a.66.66 0 0 1 .858.008l9.284 7.754V21.998 22ZM40.258 7.198H7.161L2.335 20.506l24.786.135v-3.896c0-.294.183-.544.438-.64a.761.761 0 0 1 .089-.075l12.61-8.832Zm32.549 28.734.054-18.505H28.463V35.42l2.875 2.402c.017-.002.035-.002.052-.002l26.897.004 7.938-6.772a.662.662 0 0 1 .867.007l5.713 4.876.002-.003Zm-.499 1.354-5.652-4.823-10.322 8.806H76.98l-4.669-3.985-.003.002ZM29.96 16.06h41.565L50.742 1.507 29.958 16.061h.002ZM5.847 40.347c.37 0 .67.305.67.682 0 .378-.3.683-.67.683H4.75V59.57H11.7V44.047c0-.377.3-.683.67-.683h10.162c.37 0 .67.306.67.683V59.57h6.764V41.712h-1.188a.677.677 0 0 1-.67-.683c0-.377.3-.682.67-.682h3.477L17.401 27.939 2.548 40.347h3.299Zm7.193 19.222h8.82v-14.84h-8.82v14.84Z" fill="#0F2E33"/></symbol></defs></svg>
<!-- restaurant -->
<svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" xmlns="http://www.w3.org/2000/svg"><defs><svg id="restaurants" viewBox="0 0 75 59" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M66.06 31.251c.26 1.32 2.34.87 2.71.86.37-.01.41 1.38.33 2.12-.66 5.19-3.3 22.78 1.56 22.78 2.52 0 2.6-4.56 2.7-10.35.1-5.79 0-14.32 0-14.32V1.911c0-.58-3.41.83-5.85 7.13-2.31 6.1-2.24 18-1.45 22.21ZM11.95 13.97c0 5.42-3.43 7.69-3.63 8.83-.2 1.14-.53 2.37 0 6.33s2.55 27.88-2 27.88-2.55-23.92-2-27.88c.41-2.09.41-4.239 0-6.33-.2-1.14-3.28-2.57-3.28-8.83M1.03 13.971 1 2.791M4.68 13.971l-.03-11.18M8.33 13.971 8.3 2.791M11.98 13.971l-.03-11.18M37.95 45.68a15.6 15.6 0 0 1-15.6-15.59M37.95 14.491a15.56 15.56 0 0 1 9.92 3.56" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M37.93 53.681c13.028 0 23.59-10.562 23.59-23.59S50.958 6.501 37.93 6.501s-23.59 10.562-23.59 23.59 10.562 23.59 23.59 23.59Z" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" /></svg></defs></svg>
<!-- landscaping -->
<svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" xmlns="http://www.w3.org/2000/svg"><defs><symbol id="landscaping" viewBox="0 0 57 69" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.118 20.712a10.457 10.457 0 0 0-5.117 9.16c.08 5.624 4.966 9.817 10.09 10.347 5.482.564 6.878-1.417 6.878-1.417M25.135 9.267a8.68 8.68 0 0 1 3.914 14.569M5.21 20.712a6.922 6.922 0 0 1-.444-2.485 7.31 7.31 0 0 1 6.922-7.317" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M29.049 23.834a8.834 8.834 0 0 1-3.39 16.792c-4.662.227-7.692-1.828-7.692-1.828M11.688 10.91a6.733 6.733 0 0 1-.33-2.109 6.898 6.898 0 1 1 13.791 0c0 .159 0 .318-.01.466M17.987 22.71v26.483M17.987 30.007l3.776-3.421M11.688 29.588l6.3 5.865M39.809 49.593V29.678M39.809 36.421l3.339-3.112" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M33.833 40.303c2.07.424 4.222.14 6.111-.804a9.05 9.05 0 1 0 8.525-15.883 8.7 8.7 0 0 0-13.32-8.589M2.545 50.763l4.659-5.349 4.658 5.349v16.993H2.545V50.763ZM16.955 50.763l4.658-5.349 4.658 5.349v16.993h-9.316V50.763ZM31.364 50.763l4.659-5.349 4.656 5.349v16.993h-9.315V50.763ZM31.37 55.248H26.27M16.955 55.248h-5.099M31.37 61.8H26.27M45.772 50.763l4.658-5.349 4.658 5.349v16.993h-9.316V50.763ZM45.777 55.248H40.68M45.777 61.8H40.68M16.955 61.8h-5.099" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></symbol></defs></svg>
<!-- drafters -->
<svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" xmlns="http://www.w3.org/2000/svg"><defs><symbol id="drafters" viewBox="0 0 58 57" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.045 47.167H1.002v8.588h16.043v-8.588Z" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M34.885 47.167h-17.84v8.588h17.84v-8.588Z" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M52.724 47.167h-17.84v8.588h17.84v-8.588ZM17.043 29.988H1v8.589h16.043v-8.589ZM34.883 29.988h-17.84v8.589h17.84v-8.589ZM8.124 38.577H1.002v8.588h7.122v-8.588ZM25.966 38.577H8.126v8.588h17.84v-8.588ZM8.122 21.4H1v8.588h7.122V21.4ZM25.963 21.4H8.123v8.588h17.84V21.4ZM26.566 8.194 9.914 1.792 6.832 9.808l16.652 6.403 3.082-8.017Z" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M43.805 38.577h-17.84v8.588h17.84v-8.588ZM42.8 26.214l-2.41 2.41c-.926.928-2.556.48-2.979-.804l-5.257-16.128a.768.768 0 0 1 .977-.975l16.126 5.25c1.295.421 1.738 2.052.803 2.98l-2.409 2.41M41.745 20.298l3.582 3.582 3.058 3.058M56.156 33.315l-1.428 1.43a1.626 1.626 0 0 1-2.302 0l-4.775-4.763a1.628 1.628 0 0 1 0-2.302l.731-.73.7-.701a1.626 1.626 0 0 1 2.302 0l4.772 4.772a1.627 1.627 0 0 1 0 2.294v0Z" stroke="#0F2E33" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></symbol></defs></svg>
<!-- dentist -->
<svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" xmlns="http://www.w3.org/2000/svg"><defs><symbol id="dentist" viewBox="0 0 38 54" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_753_6500)"><path d="M31.132.792a6.825 6.825 0 0 0-3.493 1C23.093 4.47 22.18 4.8 18.842 4.815c-3.339.015-4.258-.324-8.826-3.023a6.877 6.877 0 0 0-3.479-1 6.215 6.215 0 0 0-4.89 2.427C.071 5.205-.407 8.147.344 11.2c.595 2.442 1.235 4.641 1.86 6.767C4.17 24.68 5.59 29.527 2.75 33.632c-2.84 4.104-2.45 10.554.926 ************.075.155.103.236a2.052 2.052 0 0 0 1.883 1.184 2.001 2.001 0 0 0 1.471-.64c.817-.838 1.17-2.441 1.089-4.89-.11-3.17 1.515-6.944 4.236-9.849 2.052-2.206 4.413-3.479 6.355-3.545 2.331.074 5.03 1.758 7.237 4.494 2.206 2.736 3.508 6.105 3.42 8.9-.081 2.537.265 4.17 1.074 5a1.88 1.88 0 0 0 1.397.611 2.208 2.208 0 0 0 1.964-1.25c0-.044.051-.133.103-.25 3.368-7.87 3.677-14.408.92-18.431-2.759-4.024-1.42-8.951.55-15.666.619-2.126 1.266-4.325 1.862-6.767.735-3.052.257-5.994-1.302-7.98A6.208 6.208 0 0 0 31.132.792Zm4.413 9.973c-.588 2.405-1.22 4.582-1.839 6.686-2.03 6.928-3.633 12.4-.301 17.225 3.243 4.7.735 12.452-1.096 16.659l-.11.264a.47.47 0 0 1-.265.126l-.081-.045c-.155-.161-.647-.897-.559-3.677.11-3.221-1.324-7.002-3.825-10.113-2.5-3.111-5.744-5.082-8.664-5.148-2.544.08-5.28 1.552-7.693 4.126-3.023 3.228-4.861 7.509-4.736 11.142.088 2.663-.412 3.398-.574 3.56-.162.162-.118.074-.14.074a.382.382 0 0 1-.198-.074c0-.059-.06-.14-.103-.235-1.802-4.207-4.34-11.96-1.096-16.66C7.597 29.852 6 24.38 3.97 17.452c-.618-2.104-1.258-4.28-1.84-6.686-.61-2.515-.264-4.825.964-6.406a4.486 4.486 0 0 1 6.002-.985c4.597 2.728 5.84 3.302 9.745 3.302 3.906 0 5.149-.552 9.738-3.28a4.487 4.487 0 0 1 5.987.963c1.236 1.581 1.589 3.89.978 6.406Z" fill="#0F2E33"/></g><defs><clipPath id="clip0_753_6500"><path fill="#fff" transform="translate(0 .792)" d="M0 0h37.672v52.771H0z"/></clipPath></defs></symbol></defs></svg>
<!-- trucking -->
<svg aria-hidden="true" style="position:absolute;width:0;height:0;overflow:hidden" xmlns="http://www.w3.org/2000/svg"><defs><symbol id="trucking-icon" stroke-linejoin="round" stroke-width="2" stroke="#0F2E33" viewBox="0 0 67 57" fill="none"><style>.a{stroke-linejoin:round;stroke-width:2;stroke:#0f2e33}</style><path d="M7.7 45.7c1.8 0 3.2 2.2 3.2 5s-1.4 5-3.2 5c-1.7 0-3.1-2.2-3.1-5 0-2.7 1.4-5 3.1-5ZM33.3 43.4c2.2 0 3.9 2.8 3.9 6.1 0 3.4-1.7 6.1-3.9 6.1-2.1 0-3.8-2.7-3.8-6.1 0-3.3 1.7-6.1 3.8-6.1ZM60.8 49.5c0 3.4-1.8 6.1-3.9 6.1-2.2 0-3.9-2.7-3.9-6.1M25.2 49.5h3.5" class="a"/><path d="M4.6 49.5H1V12.1L25.2 1.4v48.1H10.9M25.2 1.4h31.6v9.2M25.2 14.4l11.1-3.8L40.8 28c.3 1.4.5 2.9.5 4.4v17.1H38" class="a"/><path d="M36.3 10.6h22.1c.7 0 1.3.2 *******.4.9 1 1 1.6l4 15.2c.4 1.4.5 2.9.5 4.4v13.5c0 .9-.3 1.9-1 2.6-.7.6-1.6 1-2.6 1H41.3M46.3 36.1h2.8M58.4 36.1h2.8M40.8 28h24.4M36.2 27.7l-5.6.6V18.1" class="a"/></symbol></defs></svg>  <main class="text-darks-v1">
    <section class="sticky top-0 z-50">
      <div class="mobile-menu hidden fixed w-screen h-screen bg-white p-4 z-50 overflow-auto">
        <div class="float-right" onclick="$('.mobile-menu').addClass('hidden'); $('body').removeClass('overflow-hidden')">
          <i class="text-darks-v1 text-3xl fa fa-times"></i>
        </div>
        <ul class="text-gray-600 my-16 text-lg">
  <li class="mb-2 hover:font-bold">
    <a href="/services">For Businesses</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/free-business-listing">Free Company Listing</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/listings-management">Premium Business Listings</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/small-business-marketing/websites">Websites</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/organic-seo-company">SEO</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/affordable-local-seo">Local SEO</a>
  </li>
  <li>
    <a href="/services/national-seo-company">National SEO</a>
  </li>
</ul>
          <div class="flex flex-col lg:flex-row">
    <a class="btn bg-primary-light-v1 text-gray-800 font-bold flex-1 mb-4 py-3" href="/member/login">Log In</a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="mb-4 btn bg-primary-v1 text-white inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
      </div>
      </div>
      <style>

  .dropdown-mega-menu {
    top: 100%
  }
  @media(max-width: 1060px) {
    .seo-left {
      left: 2px;
    }
  }
  @media(max-width: 1279px) {
    .seo-left {
      left: -100px;
    }
  }
  @media(min-width: 1280px) {
    .seo-left {
      left: -200px;
    }
  }
  @media(max-width: 1070px) {
    .business-right {
      right: -350px;
    }
  }
  @media(min-width: 1071px) {
    .business-right {
      right: -280px;
    }
  }

  .dropdown-arrow {
    display:none;
    position:absolute;
    bottom:0;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid rgba(245, 242, 240, 1);
  }

  .arrow-seo {
    left:49px;
  }

  .arrow-who {
    left: 80px;
  }

  .arrow-resources {
    left: 61px;
  }

  @media (min-width: 1024px) and (max-width: 1110px) {
    .search-sizing {
      width: 30%;
    }
  }

  @media (min-width: 1101px) and (max-width: 1220px) {
    .search-sizing {
      width: 35%;
    }
  }

  @media (min-width: 1221px) {
    .search-sizing {
      width: 40%;
    }
  }
</style>

<div class="mobile-search hidden fixed w-screen h-screen bg-white z-50 overflow-y-scroll">
  <div class="justify-center py-3 mx-auto max-w-header flex items-center bg-darks-v1 px-4 relative">
    <div onclick="$('.mobile-search').addClass('hidden');$('.pre-mobile-search').removeClass('hidden')" class="lg:hidden text-primary-light-v1 cursor-pointer absolute left-0 top-0 mt-6 ml-5">Cancel</div>
    <div class="flex sm:pr-3">
      <a href="/" data-test="btn-logo-navbar">
        <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo" width="109" height="25">
      </a>
    </div>
  </div>
  <div class="bg-darks-v1 text-gray-dark search-component-mobile"></div>
</div>
<div class="px-6 h-auto bg-darks-v1 text-white">
  <div class="justify-between py-3 mx-auto max-w-header flex items-center">
        <div>
  <a href="/" data-test="btn-logo-navbar">
    <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo"  width="109" height="25" >
    <span class="sr-only">Manta Home</span>
  </a>
</div>        <div class="hidden lg:flex text-sm items-center">
      <div class="dropdown-mega py-2 relative">
        <a data-test="btn-seo-navbar-desktop" class="font-bold hover:underline" href="/services/organic-seo-company">Services <i class="fa fa-angle-down"></i></a>
        <div class="dropdown-arrow arrow-seo"></div>
                  <div class="dropdown-mega-menu seo-left p-8 text-nordic-v1 bg-primary-light-v1 max-w-fit">
                  <div class="flex justify-center">
            <ul>
              <li><a data-test="btn-digital-mkt-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white font-bold font-sans" href="/digital-marketing-services">Digital Marketing Services</a></li>
              <li><a data-test="btn-seo-services-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white font-bold font-sans" href="/services/organic-seo-company">SEO Services</a></li>
              <li><a data-test="btn-local-seo-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/affordable-local-seo">Local SEO</a></li>
              <li><a data-test="btn-national-seo-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/national-seo-company">National SEO</a></li>
              <li><a data-test="btn-ecommerce-seo-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/ecommerce-seo-packages">Ecommerce SEO</a></li>
              <li><a data-test="btn-link-building-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/white-hat-link-building">Link Building</a></li>
              <li><a data-test="btn-keyword-research-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/keyword-research-agency">Keyword Research</a></li>
              <li><a data-test="btn-seo-content-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/seo/seo-content-services">SEO Content</a></li>
                            <li><a data-test="btn-seo-faqs-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/seo-faqs">SEO FAQ</a></li>
              <li><a data-test="btn-free-seo-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/free-seo-website-test">Free SEO Website Test</a></li>
            </ul>
            <ul class="border-l border-nordic-v1">
              <li class="py-2 pl-8 pr-10 font-bold">Paid Ads Services</li>
              <li><a data-test="btn-display-ads-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/display-advertising">Display Advertising</a></li>
              <li><a data-test="btn-ppc-consulting-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/services/ppc-consulting">PPC Consulting</a></li>
              <li><a data-test="btn-ppc-dentists-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/who-we-help/ppc-consulting/dental-ppc">PPC for Dentists</a></li>
              <li class="py-2 pl-8 pr-10 font-bold">Websites</li>
              <li><a data-test="btn-web-packages-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/small-business-marketing/websites">Website Packages</a></li>
              <li><a data-test="btn-web-galery-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/small-business-marketing/websites#website-gallery">Website Gallery</a></li>
            </ul>
            <ul class="border-l border-nordic-v1">
              <li class="py-2 pl-8 pr-10 font-bold">Business Listings</li>
              <li><a data-test="btn-business-listings-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/business-listings/listings-management">Listings Management</a></li>
              <li><a data-test="btn-free-listings-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/business-listings/free-business-listing">Free Business Listing</a></li>
              <li><a data-test="btn-listings-faq-navbar-desktop" class="mb-2 py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/business-listings/listings-faq">Business Listings FAQ</a></li>
              <li class="py-2 pl-8 pr-10 font-bold">Find a Company</li>
                            <li><a data-test="btn-search-directory-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/" target="_blank">Search Business Directory</a></li>
                            <li><a data-test="btn-home-quotes-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/costs" target="_blank">Get Home Improvement Quotes</a></li>
            </ul>
                      </div>
        </div>
      </div>

      <div class="dropdown-mega py-2 relative ml-8 cursor-pointer">
        <p class="font-bold hover:underline" tabindex="0">Resources <i class="fa fa-angle-down"></i></p>
        <div class="dropdown-arrow arrow-resources"></div>
        <div class="dropdown-mega-menu p-4 text-nordic-v1 bg-primary-light-v1">
          <div class="flex justify-center">
            <ul>
              <li><a data-test="btn-about-us-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/about-us">About us</a></li>
                            <li><a data-test="btn-terms-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/terms-and-conditions">Terms and conditions</a></li>
              <li><a data-test="btn-privacy-policy-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/privacy-policy">Privacy Policy</a></li>
              <li><a data-test="btn-reviews-navbar-desktop" class="py-2 pl-8 pr-10 block hover:bg-primary-v1 hover:text-white" href="/manta-reviews">Manta Reviews</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="py-2 ml-8">
        <a data-test="btn-choose-navbar-desktop" class="hover:underline font-bold" href="/about-us">Why Choose Manta?</a>
      </div>
              <a data-test="btn-login-navbar-desktop" class="hover:underline font-bold ml-4" href="/member/login">Log In</a>
        <a data-test="btn-add-company-navbar-desktop" target="_blank" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="btn bg-primary-v1 text-white inline-block font-bold border-2 border-primary-v1 ml-4" href="/business-listings/add-your-company">Claim My Listing</a>
                  </div>
    <div onclick="$('.mobile-menu').removeClass('hidden');$('body').addClass('overflow-hidden')" class="flex lg:hidden"><i class="text-2xl fa fa-bars"></i></div>
    </div>
  </div>
</div>      <div class="pl-0 lg:px-6 bg-primary-dark text-white overflow-x-hidden faded faded-x-primary-dark hidden">
  <div class="py-1 mx-auto max-w-header flex items-center overflow-x-auto">
    <div class="inline-block py-2 text-sm whitespace-no-wrap ml-5">
      <a class="cursor-pointer" href="/mb_33_A6_000/professional_services">Business Services<span class="align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_C4_000/restaurants_and_bars">Food & Beverage<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_B3_000/consumer_services">Consumer Products & Services<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_D0_000/healthcare">Health<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_G4_000/information_technology">Tech & Communications<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer mr-5" href="/mb_33_E6_000/industrial_machinery">Industrial</a>
    </div>
  </div>
</div>
      

    </section>
    <section 
      class="bg-image lg:bg-cover bg-image-height hero-img-position bg-no-repeat flex seohvacbg-img-position"
      data-lazy="//cc3.manta-r3.com/dist/fc1ac3e4/img/content/directorysearchmd.webp"
    >
			<div class="w-full px-5 py-10 sm:py-20 sm:px-8 flex justify-center items-center">
				<div>
          <div>
            <div>
              <div class="flex justify-center items-center pt-28 lg:pt-0">
                <div class="hidden lg:flex justify-center items-center h-20">
                  <svg width="89" height="89" role="img">
                    <use href="#homebrand"></use>
                  </svg>
                </div>
                <div class="lg:hidden flex justify-center items-start md:items-center h-20">
                  <svg width="56" height="56" role="img">
                    <use href="#homebrand"></use>
                  </svg>
                </div>
                <div class="ml-4 md:ml-8">
                  <h1 class="text-4xl text-darks-v1 font-serif">Manta Business Directory</h1>
                  <p class="text-primary-v1 text-xl md:text-4xl">Find a Business Near You</p>
                </div>
              </div>
              <div class="mt-10 md:mt-16 xxl:px-4 flex justify-center">
                <div class="md:max-w-xl">
                  <div class="order-2 sm:order-none rounded text-gray-dark mx-auto inline-block search-component search-component-mobile w-full mb-4">
                    <form name="searchForm">
  <div class="flex flex-col sm:flex-row px-3 sm:px-0" style="border-radius: 4px 4px 4px 0px;">
    <div class="flex sm:w-1/2 relative px-3 py-2 bg-white my-1 sm:my-0 rounded sm:rounded-l-lg sm:rounded-r-none">
      <div class="flex justify-center items-center mr-4 w-5">
        <span class="text-primary-v1 fa fa-search text-xl"></span>
      </div>
      <div class="flex w-full">
        <label for="header-search" class="sr-only">Search</label>
        <input
          id="header-search"
          name="search"
          placeholder="I'm looking for..."
          class="w-full outline-none"
          onfocus="loadSearchBar('.search-services-menu')"
          autocomplete="off"
          value=""
        />
      </div>
      <div class="absolute search-services-menu hidden" style="z-index: 10000">
        <ul class="p-0 m-0 text-gray-600">
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-utensils mr-3"></span
            ></span>
            <span class="text-small">Restaurants</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-stethoscope mr-3"></span
            ></span>
            <span class="text-small">Doctors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-gavel mr-3"></span
            ></span>
            <span class="text-small">Lawyers</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-hammer mr-3"></span
            ></span>
            <span class="text-small">Contractors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"><span class="fa fa-car mr-3"></span></span>
            <span class="text-small">Automotive</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-tooth mr-3"></span
            ></span>
            <span class="text-small">Dentists</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex py-2 sm:py-0 sm:w-1/2">
      <div class="flex w-full relative px-3 sm:pl-0 sm:pr-3 py-2 bg-white rounded-l-lg sm:rounded-none">
        <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
        <div class="self-center w-full text-gray-800">
          <label for="header-location" class="sr-only">Location</label>
          <input
            id="header-location"
            name="location"
            placeholder="City, State, Country, Zip"
            class="w-full outline-none"
            onfocus="loadSearchBar('.search-location-menu')"
            autocomplete="off"
            value=""
          />
        </div>
        <div class="search-location-menu hidden" style="z-index: 10000">
          <ul class="m-0 p-0 locations">
            <li
              class="px-4 py-3 text-primary-v1 hover:bg-gray-200 cursor-pointer"
            >
              <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
              <span class="small loc-name">Current Location</span>
            </li>
          </ul>
        </div>
      </div>
      <button
        type="submit"
        class="sm:flex items-center justify-center bg-primary-v1 text-white px-3 py-2 overflow-hidden rounded-r-lg">
        <span class="sr-only">Search</span>
        <span class="not-sr-only text-white fa fa-search text-xl"></span>
      </button>
    </div>
  </div>
</form>
<script>
  (function () {
    var loc = localStorage.getItem("locHistory");
    if (loc) {
      var li = $(
        '<li class="px-4 py-3 text-gray-600 hover:bg-gray-200 cursor-pointer border-t border-gray-300 sm:border-none"><span class="fa fa-clock mr-4"></span><span class="small loc-name">Current Location</span></li>'
      );
      JSON.parse(loc).forEach(function (l) {
        if (l.stateAbbrv) {
          li.find(".loc-name").text(l.formatted);
          $(".locations").append(li.clone());
        }
      });
    }
  })();
  var loadSearchBar = (function () {
    return function (c) {
      $(c).removeClass("hidden");
      loadScript("//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js");
    };
  })();
  $("form[name=searchForm]").on("submit", function (e) {
    e.preventDefault();
    var search = $("input[name=search]").val();
    var locationInput = $("input[name=location]").val().trim();
    if (!locationInput) {
      const lastGeo = Cookies.get('lastGeo');
      if (lastGeo) {
        try {
          const geo = JSON.parse(lastGeo);
          if (geo && geo.city && geo.stateAbbrv) locationInput = `${geo.city}, ${geo.stateAbbrv}`;
        } catch (e) {
          return;
        }
      }
    };

    if (!locationInput) return;

    var device = "desktop";
    if (window.screen.availWidth <= 500) {
      device = "mobile";
    } else if (window.screen.availWidth <= 1024) {
      device = "tablet";
    }
    var parts = locationInput.split(/[, ]+/);
    var state = parts.pop();
    var city = parts.join(" ");
    window.location =
      "/search?search_source=nav&search=" +
      encodeURIComponent(search) +
      "&city=" +
      encodeURIComponent(city) +
      "&state=" +
      encodeURIComponent(state) +
      "&device=" +
      device +
      "&screenResolution=" +
      window.screen.availWidth +
      "x" +
      window.screen.availHeight;
  });
</script>
                  </div>
                  <p class="mt-6 md:mt-10 text-center">As the industry-leading business directory for over 20 years, Manta has been connecting people to local and small businesses located in their neighborhoods. Use Manta’s business search directory to find a local business near you to support today!</p>
                </div>
              </div>
            </div>
          </div>
				</div>
			</div>
    </section>
    		<section class="p-8 bg-primary-v1 text-white flex justify-center">
      <div class="text-left md:text-center">
        <p class="font-bold mb-8 lg:hidden text-center">Search Cities</p>
        <div class="w-full grid grid-cols-2 md:grid-cols-3 gap-8 lg:flex lg:justify-center lg:items-center">
          <p class="font-bold hidden lg:block">Search Cities</p>
          <a href="/mb_53_B8_EEQ/consumer_products_and_services/new_york_ny" class="hover:font-bold">New York</a>
          <a href="/mb_53_B8_KKS/consumer_products_and_services/houston_tx" class="hover:font-bold">Houston</a>
          <a href="/mb_53_B8_4C6/consumer_products_and_services/chicago_il" class="hover:font-bold">Chicago</a>
          <a href="/mb_53_B8_5FK/consumer_products_and_services/indianapolis_in" class="hover:font-bold">Indianapolis</a>
          <a href="/mb_53_B8_8Z4/consumer_products_and_services/boston_ma" class="hover:font-bold">Boston</a>
          <a href="/mb_53_B8_3DZ/consumer_products_and_services/atlanta_ga" class="hover:font-bold">Atlanta</a>
          <a href="/mb_53_B8_IJ7/consumer_products_and_services/pittsburgh_pa" class="hover:font-bold">Pittsburgh</a>
          <a href="/mb_53_B8_1UH/consumer_products_and_services/los_angeles_ca" class="hover:font-bold">Los Angeles</a>
          <a href="/mb_53_B8_KCQ/consumer_products_and_services/dallas_tx" class="hover:font-bold">Dallas</a>
          <a href="/mb" class="md:hidden lg:block hover:font-bold">See More</a>
        </div>
        <a href="/mb" class="hidden md:block lg:hidden hover:font-bold mt-8">See More</a>
      </div>
    </section>
    <section class="w-full py-12 sm:py-24 px-8 flex justify-center items-center">
      <div class="flex flex-col sm:flex-row sm:justify-between items-center max-w-page">
        <div class="sm:w-1/2 text-center sm:text-left mb-8 sm:mb-0 sm:mr-8">
          <h4 class="text-2xl md:text-3xl font-serif">Calculate Your Home Project Costs</h4>
          <div class="my-6 leading-6 text-lg font-medium">
            <p>Getting ready to start some home projects for the fall?</p>
						<p>Start planning by calculating typical project costs for your area.</p>
          </div>
          <div>
            <a href="/costs" id="cost-calculator" class="btn btn-lg bg-primary-v1 text-white hover:bg-white hover:text-primary-v1 border-2 border-primary-v1 my-6 font-bold" data-test="home-btn-claim" target="_blank">Calculate Your Costs</a>
          </div>
        </div>
        <div class="flex justify-center items-center">
          <img 
            src="/dist/img/content/homeyou_large_small.webp" 
            class="lazy-img" 
            width="346" 
            height="282" 
            alt="Invoice and calculator demonstrating what the Homeyou cost calculator can provide"
            loading="lazy"
            data-lazy="//cc3.manta-r3.com/dist/fc1ac3e4/img/content/homeyou_large_optim.webp"
          />
        </div>
      </div>
    </section>
    <section class="w-full px-5 py-10 sm:py-20 sm:px-8 flex justify-center items-center bg-secondary-light-v1 text-center">
      <div class="max-w-page">
        <h2 class="text-3xl md:text-4xl mb-8 md:mb-20 font-serif font-medium leading-tight lg:px-16">Find What You’re Looking for in Manta’s Small Business Directory</h2>
        <div class="flex justify-center items-center">
          <div class="md:flex md:flex-wrap md:justify-evenly">
						<a href="/mb_33_C4_000/restaurants_and_bars" target="_blank" class="py-4 bg-primary-light-v1 w-56 h-35 flex justify-center items-center border-2 border-primary-light-v1 md:mr-8 mb-8">
							<div class="h-full flex flex-col items-center justify-between">
								<div class="flex justify-center items-center h-20">
									<svg width="72" height="55" role="img">
										<use href="#restaurants"></use>
									</svg>
								</div>
								<p class="-m-2">Restaurants</p>
							</div>
						</a>
						<a href="/mb_33_C0_000/agriculture" target="_blank" class="py-4 bg-primary-light-v1 w-56 h-35 flex justify-center items-center border-2 border-primary-light-v1 md:mr-8 mb-8">
							<div class="h-full flex flex-col items-center justify-between">
								<div class="flex justify-center items-center h-20">
									<svg width="54" height="66" role="img">
										<use href="#landscaping"></use>
									</svg>
								</div>
								<p>Landscaping</p>
							</div>
						</a>
						<a href="/mb_33_E0_000/construction" target="_blank" class="py-4 bg-primary-light-v1 w-56 h-35 flex justify-center items-center border-2 border-primary-light-v1 md:mr-8 mb-8">
							<div class="h-full flex flex-col items-center justify-between">
								<div class="flex justify-center items-center h-20">
									<svg width="56" height="54" role="img">
										<use href="#drafters"></use>
									</svg>
								</div>
								<p>Construction</p>
							</div>
						</a>
						<a href="/mb_33_D0_000/healthcare" target="_blank" class="py-4 bg-primary-light-v1 w-56 h-35 flex justify-center items-center border-2 border-primary-light-v1 md:mr-8 mb-8">
							<div class="h-full flex flex-col items-center justify-between">
								<div class="flex justify-center items-center h-20">
									<svg width="38" height="53" role="img">
										<use href="#dentist"></use>
									</svg>
								</div>
								<p>Healthcare</p>
							</div>
						</a>  
						<a href="/mb_35_B82C70B4_000/heating_and_air_conditioning_contractors" target="_blank" class="py-4 bg-primary-light-v1 w-56 h-35 flex justify-center items-center border-2 border-primary-light-v1 md:mr-8 mb-8">
							<div class="h-full flex flex-col items-center justify-between">
								<div class="flex justify-center items-center h-20">
                  <img 
                    src="/dist/img/content/home_hvac.png" 
                    width="87" 
                    height="64" 
                    alt="Invoice and calculator demonstrating what the Homeyou cost calculator can provide"
                  >
                </div>
								<p>HVAC</p>
							</div>
						</a>          
						<a href="/mb_35_A7213000_000/real_estate_agents_and_managers" target="_blank" class="py-4 bg-primary-light-v1 w-56 h-35 flex justify-center items-center border-2 border-primary-light-v1 md:mr-8 mb-8">
							<div class="h-full flex flex-col items-center justify-between">
								<div class="flex justify-center items-center h-20">
									<svg width="100" height="71" role="img">
										<use xlink:href="#realestate"></use>
									</svg>
								</div>
								<p>Real Estate</p>
							</div>
						</a>          
						<a href="/mb_34_B50D5_000/trucking_except_local" target="_blank" class="py-4 bg-primary-light-v1 w-56 h-35 flex justify-center items-center border-2 border-primary-light-v1 mb-8 md:mr-8 ">
							<div class="h-full flex flex-col items-center justify-between">
								<div class="flex justify-center items-center h-20">
									<svg width="65" height="54" role="img">
										<use href="#trucking-icon"></use>
									</svg>
								</div>
								<p>Trucking</p>
							</div>
						</a>
        	</div>
      	</div>
			</div>
    </section>
  </main>
  <style>
  @media (max-width: 360px) {
    .xs-device {
      font-size: 0.875rem;
    }
    .xs-8 {
      height: 2rem;
      width: 2rem;
    }
  }
</style>
<footer>
  <div class="w-full border-t bg-darks-v1 text-primary-light-v1 py-8 lg:py-16 flex flex-col justify-center items-center px-4 sm:px-6">
    <div class="grid grid-cols-8 gap-4 sm:gap-8 max-w-header w-full">
      <div class="hidden lg:flex justify-between mt-6 flex-col col-span-2">
        <div>
          <div>
            <a href="/" data-test="btn-logo-navbar">
              <img loading="lazy" width="162" height="37" class="mb-6 mr-3" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo">
              <span class="sr-only">Manta Home</span>
            </a>
          </div>
          <div class="flex">
            <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
                <span class="sr-only">Manta on Twitter</span>
              </span>
            </a>
            <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
                <span class="sr-only">Manta on Facebook</span>
              </span>
            </a>
            <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
                <span class="sr-only">Manta on LinkedIn</span>
              </span>
            </a>
          </div>
        </div>
        <div class="text-primary-light-v1 xs-device">
          © 2025 Manta Media Inc.<br>All rights reserved.
                  </div>
      </div>
      <div class="grid grid-cols-2 sm:grid-cols-3 gap-8 col-span-8 sm:col-span-5 lg:col-span-4">
        <div class="flex flex-col">
          <span class="font-serif">Manta</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-contact-footer-desktop" href="/contact" class="hover:font-bold py-1">Contact Us</a>
            <a data-test="btn-about-footer-desktop" href="/about-us" class="hover:font-bold py-1">About Us</a>
            <a data-test="btn-reviews-footer-desktop" href="/manta-reviews" class="hover:font-bold py-1">Reviews</a>
            <a data-test="btn-careers-footer-desktop" href="/careers" class="hover:font-bold py-1">Careers</a>
            <a data-test="btn-termsConditions-footer-desktop" href="/terms-and-conditions" class="hover:font-bold py-1">Terms & Conditions</a>
            <a data-test="btn-privacyPolicy-footer-desktop" href="/privacy-policy" class="hover:font-bold py-1">Privacy Policy</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Services</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-digital-mkt-footer-desktop" href="/digital-marketing-services" class="hover:font-bold py-1">Digital Marketing Services</a>
            <a data-test="btn-seo-services-footer-desktop" href="/services/organic-seo-company" class="hover:font-bold py-1">SEO Services</a>
            <a data-test="btn-local-seo-footer-desktop" href="/services/affordable-local-seo" class="hover:font-bold py-1">Local SEO</a>
            <a data-test="btn-national-seo-footer-desktop" href="/services/national-seo-company" class="hover:font-bold py-1">National SEO</a>
            <a data-test="btn-free-seo-footer-desktop" href="/free-seo-website-test" class="hover:font-bold py-1">Free SEO Website Test</a>
            <a data-test="btn-listings-footer-desktop" href="/business-listings/listings-management" class="hover:font-bold py-1">Listings Management</a>
            <a data-test="btn-display-ads-footer-desktop" href="/services/display-advertising" class="hover:font-bold py-1">Display Ads</a>
            <a data-test="btn-ppc-footer-desktop" href="/services/ppc-consulting" class="hover:font-bold py-1">PPC Consulting</a>
            <a data-test="btn-websites-footer-desktop" href="/small-business-marketing/websites" class="hover:font-bold py-1">Website Creation</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Resources</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-seo-faq-footer-desktop" href="/seo-faqs" class="hover:font-bold py-1">SEO FAQ</a>
            <a data-test="btn-ecommerce-footer-desktop" href="/seo-industry-guide/ecommerce-seo-guide" class="hover:font-bold py-1">Ecommerce SEO Guide</a>
            <a data-test="btn-construction-footer-desktop" href="/seo-industry-guide/seo-for-construction-companies" class="hover:font-bold py-1">Construction SEO Guide</a>
            <a data-test="btn-hvac-footer-desktop" href="/seo-industry-guide/seo-for-hvac" class="hover:font-bold py-1">HVAC SEO Guide</a>
            <a data-test="btn-homeyou-footer-desktop" href="/costs" class="hover:font-bold py-1">Home Services Cost</a>
          </div>
        </div>
      </div>
      <div class="border-t border-primary-light-v1 sm:border-none pt-6 sm:pt-0 flex flex-col col-span-8 sm:col-span-3 lg:col-span-2">
        <span class="font-serif">Manta Members</span>
                <div class="flex items-center mt-6">
          <a data-test="btn-login-footer-desktop" class="hover:font-bold mr-4" href="/member/login">Log In</a>
          <a data-test="btn-login-footer-desktop" class="btn bg-primary-v1 text-white inline-block hover:font-bold" href="/member/register">Sign Up</a>
        </div>
                <div class="mt-6">
                          <p class="mb-2">Search Manta's Directory to find the Small Business you're looking for</p>
              <a data-test="btn-index-footer-desktop" class="bg-primary-v1 py-2 px-4 rounded text-white inline-block hover:font-bold" href="/">Find a Business Near You</a>
                      </div>
      </div>
    </div>
    <div class="lg:hidden border-b border-t border-primary-light-v1 py-4 my-6 w-full">
      <div class="flex text-primary-light-v1">
        <a data-test="btn-help-footer-desktop-mobile" href="/contact" class="mr-6">Help</a>
        <a data-test="btn-termsConditions-footer-mobile" href="/terms-and-conditions" class="mr-6">Terms</a>
        <a data-test="btn-privacyPolicy-footer-mobile" href="/privacy-policy" class="mr-6">Privacy</a>
      </div>
    </div>
    <div class="flex lg:hidden justify-between items-center w-full">
      <div class="text-primary-light-v1 xs-device">
        © 2025 Manta Media Inc.<br>All rights reserved.
              </div>
      <div class="flex">
        <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
            <span class="sr-only">Manta on Twitter</span>
          </span>
        </a>
        <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
            <span class="sr-only">Manta on Facebook</span>
          </span>
        </a>
        <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
            <span class="sr-only">Manta on LinkedIn</span>
          </span>
        </a>
      </div>
    </div>
  </div>
      </footer>  <script>
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/dist/sw.js', { scope: '/' })
      .then(registration => registration)
      .catch(error => error);
  }
</script>  <script id="lazy-loading-images">
  const imgTargets = document.querySelectorAll('[data-lazy]');
  const loadImg = function(entries, observer) {
    const [entry] = entries;
    if(!entry.isIntersecting) return;
    if(entry.target.classList.contains('bg-image')) {
      entry.target.style.transition = 'background-image 0.5s ease-in-out';
      entry.target.style.backgroundImage = `url(${entry.target.dataset.lazy})`;
      entry.target.classList.remove('bg-image');
    }
    if(entry.target.classList.contains('lazy-img')) {
      entry.target.src = entry.target.dataset.lazy;
      entry.target.addEventListener('load', () => {
        entry.target.classList.remove('lazy-img');
      })
    }
    observer.unobserve(entry.target);
  }
  const imgObserver = new IntersectionObserver(loadImg, {
    root: null,
    threshold: [0, 1.0],
    rootMargin: '-50px'
  })
  imgTargets.forEach(img => imgObserver.observe(img));
</script>	<script>
    (function() {
      if (screen.width > 767) return;
      let search = document.querySelector('.search-component');
      search.querySelectorAll('input').forEach(function(el) {
        el.addEventListener('click', function() {
          search.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
        });
      });
    })();
		let calculator = document.querySelector('#cost-calculator');
    calculator.addEventListener('click', (e) => {
      gaTrackSS.sendEvent('Homeyou Calculate Your Costs');
    });
	</script>
</body>
</html>