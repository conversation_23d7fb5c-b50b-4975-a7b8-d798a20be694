# 👤 People Search in Streamlit Interface

## 🚀 Quick Start Guide

The people search functionality is now fully integrated into your Streamlit interface! Here's how to use it:

### 1. 🌐 Launch the Streamlit App
```bash
streamlit run streamlit_app.py
```

### 2. 🔍 Navigate to People Search
- In the sidebar, select **"👤 People Search"**
- This will open the people search configuration interface

### 3. 📋 Enter Search Parameters

#### **Required Fields:**
- **First Name**: Person's first name (e.g., "<PERSON>")
- **Last Name**: Person's last name (e.g., "<PERSON>")

#### **Optional Fields:**
- **City**: City where the person lives (e.g., "Houston")
- **State**: State abbreviation (e.g., "TX", "CA", "NY")
- **Full Address**: Complete address if known

### 4. 🔍 Select Data Sources

The interface shows available people search sources:

- **✅ TruthFinder Browser** (Priority 1)
  - Comprehensive personal records with demographics
  - Uses your existing cookie system
  - Response time: ~44 seconds

- **✅ TruePeopleSearch** (Priority 2)
  - Public records and contact information
  - Response time: ~15 seconds

- **✅ CyberBackgroundChecks** (Priority 3)
  - Background check and public records
  - Response time: ~30 seconds

### 5. ⚙️ Configure Options

#### **Export Format:**
- Excel (.xlsx) - Recommended for comprehensive data
- CSV (.csv) - For simple data processing

#### **Cross-Reference:**
- ☑️ **Cross-reference with Business Data** - Finds business connections

#### **Advanced Options:**
- **Maximum Results per Source**: 10-100 (default: 50)

### 6. 🚀 Execute Search

Click **"🚀 Start People Search"** to begin the search process.

## 📊 What You'll See

### **Progress Indicators:**
1. "Initializing real-time people search..."
2. "Scraper initialized, starting people search..."
3. "Searching people data sources..."
4. "Processing and enriching people data..."
5. "Exporting people search results..."
6. "✅ Real-time people search completed successfully!"

### **Results Display:**
- **Total Results Found**: Number of person records
- **Export File Location**: Path to saved results
- **Search Summary**: Sources used, processing time, etc.

## 📁 Results Include

### **Personal Information:**
- Full name and known aliases
- Current and previous addresses
- Age and demographic information
- Phone numbers (multiple with carrier info)
- Email addresses (multiple with verification status)

### **Family & Associates:**
- Family members with relationships
- Associates and connections
- Spouse and children information

### **Historical Data:**
- Address history with date ranges
- Employment history
- Property records
- Court and criminal records (if available)

### **Business Connections:**
- Related business ownership
- Business affiliations
- Cross-referenced business data

## 🔧 Cookie Management Integration

The people search automatically uses your existing cookie system:

### **Automatic Cookie Loading:**
- Loads fresh cookies from `sessions/` directory
- Uses the most recent session files
- Applies proper headers and user agents

### **Cookie Refresh:**
- Automatically refreshes cookies when they expire
- Uses your existing `get_fresh_cookies.py` script
- Falls back to hardcoded cookies as last resort

### **Session Files Used:**
- `sessions/truthfinder_cookies_*.json` - Fresh cookies
- `sessions/truthfinder_session.json` - Complete session data
- `sessions/truthfinder_api_config.json` - API configuration

## 🎯 Example Workflow

### **Scenario: Find John Smith in Houston, TX**

1. **Open Streamlit**: `streamlit run streamlit_app.py`
2. **Select**: "👤 People Search" from sidebar
3. **Enter**:
   - First Name: `John`
   - Last Name: `Smith`
   - City: `Houston`
   - State: `TX`
4. **Select Sources**: ✅ TruthFinder Browser, ✅ TruePeopleSearch
5. **Enable**: ☑️ Cross-reference with Business Data
6. **Click**: "🚀 Start People Search"

### **Expected Results:**
- Multiple John Smith records in Houston area
- Contact information (phones, emails, addresses)
- Family member information
- Business ownership connections
- Historical address data
- Exported Excel/CSV file with all data

## 🔄 Demo Mode vs Real Mode

### **Demo Mode** (Default):
- Uses sample data for testing
- No real API calls
- Instant results
- Safe for development

### **Real Mode**:
- Makes actual API calls to data sources
- Uses fresh cookies and session data
- Real person search results
- Requires valid session cookies

## 🛠️ Troubleshooting

### **No Results Found:**
1. Check if cookies are fresh: `python get_fresh_cookies.py truthfinder`
2. Verify internet connection
3. Try different name variations
4. Check if person exists in the database

### **403 Forbidden Errors:**
1. Refresh cookies: `python get_fresh_cookies.py truthfinder --automated`
2. Check session files in `sessions/` directory
3. Verify API key is still valid

### **Slow Performance:**
1. Reduce maximum results per source
2. Select fewer data sources
3. Use specific location filters

## 🎉 Success!

Your people search is now fully integrated with:
- ✅ Streamlit frontend interface
- ✅ Existing cookie extraction system
- ✅ Automatic session management
- ✅ Real-time progress tracking
- ✅ Comprehensive data export
- ✅ Cross-reference with business data

The system provides the same professional user experience as your business search, with all the same anti-bot protection and data processing capabilities!
