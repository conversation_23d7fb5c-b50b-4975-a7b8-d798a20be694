#!/usr/bin/env python3
"""
Direct API test with fresh cookies.

This script tests the TruthFinder API directly using the fresh cookies
that were just extracted.
"""

import sys
import os
import json
import asyncio
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_truthfinder_api_direct():
    """Test TruthFinder API directly with fresh cookies."""
    print("🧪 Testing TruthFinder API with Fresh Cookies")
    print("=" * 50)
    
    try:
        from playwright.async_api import async_playwright
        
        # Load the fresh cookies
        sessions_dir = "sessions"
        cookie_files = [f for f in os.listdir(sessions_dir) 
                       if f.startswith("truthfinder_cookies_") and f.endswith(".json")]
        
        if not cookie_files:
            print("❌ No fresh cookie files found")
            print("💡 Run: python get_fresh_cookies.py truthfinder --automated")
            return False
        
        # Get the most recent cookie file
        latest_cookie_file = max(cookie_files, 
                               key=lambda x: os.path.getctime(os.path.join(sessions_dir, x)))
        cookie_path = os.path.join(sessions_dir, latest_cookie_file)
        
        print(f"📁 Loading cookies from: {latest_cookie_file}")
        
        with open(cookie_path, 'r') as f:
            session_data = json.load(f)
        
        cookies = session_data.get('cookies', {})
        user_agent = session_data.get('user_agent', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36')
        
        print(f"✅ Loaded {len(cookies)} cookies")
        print(f"🌐 User agent: {user_agent[:50]}...")
        
        # Show key cookies
        key_cookies = ['__cf_bm', 'sessionId', 'device-id', 'sessionCreated']
        for cookie_name in key_cookies:
            if cookie_name in cookies:
                value = cookies[cookie_name]
                print(f"   🔑 {cookie_name}: {value[:30]}...")
        
        # Test API call with Playwright
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent=user_agent,
                viewport={'width': 1920, 'height': 1080}
            )
            
            # Set the fresh cookies
            cookies_to_set = []
            for name, value in cookies.items():
                cookies_to_set.append({
                    'name': name,
                    'value': value,
                    'domain': '.truthfinder.com',
                    'path': '/'
                })
            
            await context.add_cookies(cookies_to_set)
            print(f"✅ Set {len(cookies_to_set)} cookies in browser context")
            
            page = await context.new_page()
            
            try:
                # Test API call for Saad Momin
                print("\n📋 Testing API call for: Saad Momin")
                
                api_url = "https://api2.truthfinder.com/v1/people/"
                params = {
                    'firstName': 'Saad',
                    'lastName': 'Momin',
                    'fields': 'names,locations,related_persons'
                }
                
                headers = {
                    'accept': '*/*',
                    'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                    'api-key': 'B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98',
                    'app-id': 'tf-web',
                    'cache-control': 'no-cache',
                    'origin': 'https://www.truthfinder.com',
                    'pragma': 'no-cache',
                    'priority': 'u=1, i',
                    'referer': 'https://www.truthfinder.com/search/?utm_source=VBDA&traffic[source]=VBDA&utm_medium=affiliate&traffic[medium]=affiliate&utm_campaign=truepeoplesearch&traffic[campaign]=Banner:truepeoplesearch&utm_term=first&traffic[term]=first&utm_content=&traffic[content]=&s1=truepeoplesearch&s2=Banner&s3=first&s4=&s5=&traffic[placement]=&traffic[funnel]=bg&ck_rsid=3757101803&firstName=saad&lastName=momin',
                    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                    'sec-ch-ua-mobile': '?1',
                    'sec-ch-ua-platform': '"Android"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-site',
                    'user-agent': user_agent
                }
                
                print(f"🔍 Making API request to: {api_url}")
                print(f"📋 Parameters: {params}")
                
                # Make the API request
                response = await page.request.get(api_url, params=params, headers=headers)
                
                print(f"📊 Response status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print("✅ API call successful!")
                    
                    results = data.get('results', [])
                    print(f"📊 Found {len(results)} results")
                    
                    # Show first result if available
                    if results:
                        person = results[0]
                        print(f"\n👤 Sample Result:")
                        
                        # Extract names
                        names = person.get('names', [])
                        if names:
                            primary_name = names[0]
                            full_name = f"{primary_name.get('first', '')} {primary_name.get('last', '')}".strip()
                            print(f"   Name: {full_name}")
                            
                            if primary_name.get('middle'):
                                print(f"   Middle: {primary_name.get('middle')}")
                        
                        # Extract locations
                        locations = person.get('locations', [])
                        if locations:
                            current_location = locations[0]
                            if 'address' in current_location:
                                addr = current_location['address']
                                address_str = f"{addr.get('street', '')} {addr.get('city', '')}, {addr.get('state_code', '')} {addr.get('zip_code', '')}".strip()
                                print(f"   Address: {address_str}")
                        
                        # Extract related persons
                        related_persons = person.get('related_persons', [])
                        if related_persons:
                            print(f"   Related persons: {len(related_persons)}")
                            for relative in related_persons[:3]:  # Show first 3
                                rel_name = f"{relative.get('first_name', '')} {relative.get('last_name', '')}".strip()
                                relationship = relative.get('relationship', 'unknown')
                                print(f"     - {rel_name} ({relationship})")
                    
                    return True
                    
                elif response.status == 403:
                    response_text = await response.text()
                    print("❌ 403 Forbidden - Cloudflare blocking")
                    print("💡 This means cookies are expired or invalid")
                    
                    if "cloudflare" in response_text.lower():
                        print("🛡️  Cloudflare challenge detected")
                        print("🔄 Need to refresh cookies manually")
                        print("💡 Try: python get_fresh_cookies.py truthfinder")
                        print("   (without --automated for manual browsing)")
                    
                    return False
                    
                else:
                    response_text = await response.text()
                    print(f"❌ API call failed with status {response.status}")
                    print(f"Response: {response_text[:200]}...")
                    return False
                    
            except Exception as e:
                print(f"❌ Error making API request: {e}")
                return False
            finally:
                await browser.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_manual_cookie_extraction():
    """Guide user through manual cookie extraction."""
    print("\n🔄 Manual Cookie Extraction Guide")
    print("=" * 50)
    
    print("The automated cookie extraction got cookies, but they may not have")
    print("proper Cloudflare clearance. For best results:")
    print("")
    print("1. 🌐 Run manual cookie extraction:")
    print("   python get_fresh_cookies.py truthfinder")
    print("")
    print("2. 🖱️  In the browser that opens:")
    print("   - Accept any terms/conditions")
    print("   - Perform a sample search (e.g., 'John Smith')")
    print("   - Wait for results to load")
    print("   - Press ENTER in the terminal")
    print("")
    print("3. 🧪 Test the API again:")
    print("   python test_direct_api.py")
    print("")
    print("This ensures you have proper Cloudflare clearance cookies.")

def test_existing_session_files():
    """Check what session files are available."""
    print("\n📁 Checking Existing Session Files")
    print("=" * 50)
    
    try:
        sessions_dir = "sessions"
        if not os.path.exists(sessions_dir):
            print("❌ Sessions directory not found")
            return False
        
        files = os.listdir(sessions_dir)
        truthfinder_files = [f for f in files if 'truthfinder' in f.lower()]
        
        print(f"📋 Found {len(truthfinder_files)} TruthFinder session files:")
        
        for file in truthfinder_files:
            file_path = os.path.join(sessions_dir, file)
            file_size = os.path.getsize(file_path)
            mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            age_minutes = (datetime.now() - mod_time).total_seconds() / 60
            
            print(f"   📄 {file}")
            print(f"      Size: {file_size} bytes")
            print(f"      Modified: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"      Age: {age_minutes:.1f} minutes")
            
            # Check if it's a cookie file
            if file.endswith('.json') and 'cookies' in file:
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)
                    
                    cookies = data.get('cookies', {})
                    print(f"      Cookies: {len(cookies)}")
                    
                    # Check for Cloudflare cookies
                    cf_cookies = [name for name in cookies.keys() if 'cf' in name.lower()]
                    if cf_cookies:
                        print(f"      CF cookies: {', '.join(cf_cookies)}")
                    else:
                        print("      ⚠️  No Cloudflare cookies found")
                        
                except Exception as e:
                    print(f"      ❌ Error reading file: {e}")
            
            print()
        
        return len(truthfinder_files) > 0
        
    except Exception as e:
        print(f"❌ Error checking session files: {e}")
        return False

def show_next_steps():
    """Show next steps based on test results."""
    print("\n💡 Next Steps")
    print("=" * 30)
    
    print("🔄 To get working cookies for TruthFinder API:")
    print("")
    print("1. 🌐 Manual extraction (RECOMMENDED):")
    print("   python get_fresh_cookies.py truthfinder")
    print("   - Browser will open")
    print("   - Accept terms, perform a search")
    print("   - Press ENTER when done")
    print("")
    print("2. 🧪 Test the API:")
    print("   python test_direct_api.py")
    print("")
    print("3. 👤 Use in people search:")
    print("   python demo_people_search_with_cookies.py")
    print("   streamlit run streamlit_app.py")
    print("")
    print("🔑 Key points:")
    print("- Automated extraction may not get Cloudflare clearance")
    print("- Manual browsing ensures proper session establishment")
    print("- Cookies typically last 30-60 minutes")
    print("- The system will auto-refresh when they expire")

async def main():
    """Run the direct API test."""
    print("🚀 Direct TruthFinder API Test")
    print("=" * 40)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    # Check session files first
    session_files_exist = test_existing_session_files()
    
    if session_files_exist:
        # Test the API with fresh cookies
        api_success = await test_truthfinder_api_direct()
        
        if api_success:
            print("\n🎉 SUCCESS!")
            print("✅ TruthFinder API is working with fresh cookies")
            print("✅ People search functionality is ready to use")
            print("✅ Streamlit interface will work correctly")
            print("")
            print("🚀 Ready to use:")
            print("- streamlit run streamlit_app.py")
            print("- Select '👤 People Search' tab")
            print("- Enter person details and search!")
        else:
            print("\n⚠️  API Test Failed")
            print("❌ Fresh cookies may not have proper Cloudflare clearance")
            await test_manual_cookie_extraction()
    else:
        print("❌ No session files found")
        await test_manual_cookie_extraction()
    
    show_next_steps()

if __name__ == "__main__":
    asyncio.run(main())
