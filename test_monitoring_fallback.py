#!/usr/bin/env python3
"""
Test script for monitoring and fallback systems.
"""

import sys
import os
import logging
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_monitoring_system():
    """Test the monitoring system functionality."""
    print("🧪 TESTING MONITORING SYSTEM")
    print("=" * 60)
    
    try:
        from src.utils.monitoring import ScraperMonitor, ScraperMetrics, SystemMetrics
        
        print("📋 Testing Monitoring Components:")
        
        # Test configuration
        config = {
            'monitoring': {
                'enabled': True,
                'metrics_retention_hours': 24,
                'health_check_interval': 300,
                'alert_thresholds': {
                    'error_rate': 0.5,
                    'success_rate': 0.3,
                    'response_time': 30.0
                }
            }
        }
        
        monitor = ScraperMonitor(config)
        print(f"   ✅ Monitor initialized: {monitor.enabled}")
        
        # Test scraper registration
        test_scrapers = ['bbb', 'manta', 'truthfinder_api', 'linkedin']
        for scraper_name in test_scrapers:
            enabled = scraper_name != 'linkedin'  # LinkedIn disabled
            monitor.register_scraper(scraper_name, enabled)
            print(f"   ✅ Registered {scraper_name}: {'enabled' if enabled else 'disabled'}")
        
        # Test request recording
        print(f"\n📊 Testing Request Recording:")
        
        # Simulate various request scenarios
        test_scenarios = [
            ('bbb', True, 2.5, False, None),  # Successful request
            ('bbb', False, 15.0, True, 'Blocked by Cloudflare'),  # Blocked request
            ('manta', True, 3.2, False, None),  # Successful request
            ('manta', False, 45.0, False, 'Connection timeout'),  # Failed request
            ('truthfinder_api', True, 1.8, False, None),  # API success
            ('linkedin', False, 8.0, True, '403 Forbidden'),  # LinkedIn blocked
        ]
        
        for scraper, success, response_time, blocked, error in test_scenarios:
            monitor.record_request(scraper, success, response_time, blocked, error)
            status = "✅ Success" if success else ("🚫 Blocked" if blocked else "❌ Failed")
            print(f"   {status} {scraper}: {response_time}s")
        
        # Test results recording
        monitor.record_results('bbb', 5)
        monitor.record_results('manta', 3)
        monitor.record_results('truthfinder_api', 8)
        
        # Test status retrieval
        print(f"\n📈 Testing Status Retrieval:")
        
        system_status = monitor.get_system_status()
        print(f"   📊 System Status:")
        print(f"      Total scrapers: {system_status.total_scrapers}")
        print(f"      Active scrapers: {system_status.active_scrapers}")
        print(f"      Healthy scrapers: {system_status.healthy_scrapers}")
        print(f"      Failed scrapers: {system_status.failed_scrapers}")
        print(f"      Overall success rate: {system_status.overall_success_rate:.1%}")
        print(f"      Total results: {system_status.total_results_collected}")
        
        # Test individual scraper status
        for scraper_name in test_scrapers:
            scraper_status = monitor.get_scraper_status(scraper_name)
            if scraper_status:
                print(f"   📊 {scraper_name}: {scraper_status.status} "
                      f"({scraper_status.success_rate:.1%} success, "
                      f"{scraper_status.average_response_time:.1f}s avg)")
        
        # Test health report
        print(f"\n📋 Testing Health Report:")
        health_report = monitor.get_health_report()
        
        print(f"   ✅ Health report generated")
        print(f"   📊 Alerts: {len(health_report['alerts'])}")
        print(f"   💡 Recommendations: {len(health_report['recommendations'])}")
        
        if health_report['alerts']:
            print(f"   🚨 Sample alert: {health_report['alerts'][0]['message']}")
        
        if health_report['recommendations']:
            print(f"   💡 Sample recommendation: {health_report['recommendations'][0]}")
        
        # Test monitoring start/stop
        print(f"\n🔄 Testing Monitoring Control:")
        monitor.start_monitoring()
        print(f"   ✅ Monitoring started")
        
        time.sleep(1)  # Brief pause
        
        monitor.stop_monitoring()
        print(f"   ✅ Monitoring stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during monitoring test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_system():
    """Test the fallback system functionality."""
    print("\n🧪 TESTING FALLBACK SYSTEM")
    print("=" * 60)
    
    try:
        from src.utils.fallback_system import FallbackSystem, FallbackRule, HealthChecker, AlertSystem
        
        print("📋 Testing Fallback Components:")
        
        # Test configuration
        config = {
            'fallback_system': {
                'enabled': True,
                'rules': [
                    {
                        'primary_scraper': 'bbb',
                        'fallback_scrapers': ['manta', 'truthfinder_api'],
                        'trigger_conditions': {
                            'error_rate': 0.7,
                            'consecutive_failures': 3,
                            'response_time': 30.0
                        },
                        'cooldown_minutes': 15,
                        'max_attempts': 2
                    }
                ]
            },
            'health_checker': {
                'enabled': True,
                'check_interval': 300
            },
            'alerts': {
                'enabled': True,
                'channels': ['log'],
                'alert_file': 'logs/alerts.log'
            }
        }
        
        fallback_system = FallbackSystem(config)
        health_checker = HealthChecker(config)
        alert_system = AlertSystem(config)
        
        print(f"   ✅ Fallback system initialized: {fallback_system.enabled}")
        print(f"   ✅ Health checker initialized: {health_checker.enabled}")
        print(f"   ✅ Alert system initialized: {alert_system.enabled}")
        
        # Test fallback rules
        print(f"\n📋 Testing Fallback Rules:")
        print(f"   📊 Loaded {len(fallback_system.fallback_rules)} fallback rules")
        
        for primary, rule in fallback_system.fallback_rules.items():
            print(f"   🔄 {primary} → {', '.join(rule.fallback_scrapers)}")
        
        # Test fallback triggering
        print(f"\n🧪 Testing Fallback Triggering:")
        
        test_metrics = [
            ('bbb', {'error_rate': 0.8, 'consecutive_failures': 4, 'average_response_time': 35.0}),
            ('manta', {'error_rate': 0.3, 'consecutive_failures': 1, 'average_response_time': 5.0}),
            ('linkedin', {'error_rate': 0.9, 'consecutive_failures': 5, 'average_response_time': 25.0})
        ]
        
        for scraper, metrics in test_metrics:
            should_fallback = fallback_system.should_use_fallback(scraper, metrics)
            status = "🔄 Fallback triggered" if should_fallback else "✅ No fallback needed"
            print(f"   {status} for {scraper} (error: {metrics['error_rate']:.1%})")
        
        # Test fallback scraper selection
        print(f"\n🎯 Testing Fallback Scraper Selection:")
        
        available_scrapers = ['bbb', 'manta', 'truthfinder_api', 'cyberbackgroundchecks']
        
        for primary in ['bbb', 'manta', 'linkedin']:
            fallback_scrapers = fallback_system.get_fallback_scrapers(primary, available_scrapers)
            if fallback_scrapers:
                print(f"   🔄 {primary} fallbacks: {', '.join(fallback_scrapers)}")
            else:
                print(f"   ⚠️  No fallbacks available for {primary}")
        
        # Test fallback attempt recording
        print(f"\n📊 Testing Fallback Attempt Recording:")
        
        fallback_system.record_fallback_attempt('bbb', 'manta', True)
        print(f"   ✅ Recorded successful fallback: bbb → manta")
        
        fallback_system.record_fallback_attempt('linkedin', 'truthfinder_api', False)
        print(f"   ❌ Recorded failed fallback: linkedin → truthfinder_api")
        
        # Test fallback status
        fallback_status = fallback_system.get_fallback_status()
        print(f"\n📈 Fallback Status:")
        print(f"   📊 Rules: {fallback_status['rules_count']}")
        print(f"   🕐 Active cooldowns: {len(fallback_status['active_cooldowns'])}")
        print(f"   📈 Recent attempts: {len(fallback_status['recent_attempts'])}")
        
        # Test health checker
        print(f"\n🏥 Testing Health Checker:")
        
        # Register a simple health check
        def test_health_check():
            return {'status': 'healthy', 'test': True}
        
        health_checker.register_health_check('test_check', test_health_check)
        print(f"   ✅ Registered test health check")
        
        health_results = health_checker.run_health_checks()
        print(f"   📊 Health check results: {len(health_results)} checks")
        
        for check_name, result in health_results.items():
            print(f"   🏥 {check_name}: {result['status']}")
        
        # Test alert system
        print(f"\n🚨 Testing Alert System:")
        
        alert_system.send_alert('test_alert', 'This is a test alert', 'warning')
        print(f"   ✅ Test alert sent")
        
        alert_system.send_alert('critical_test', 'This is a critical test', 'critical')
        print(f"   🚨 Critical alert sent")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during fallback test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """Test integration with main scraper."""
    print("\n🧪 TESTING MAIN SCRAPER INTEGRATION")
    print("=" * 60)
    
    try:
        from main import BusinessOwnerScraper
        
        print("📋 Testing Main Scraper Integration:")
        
        scraper = BusinessOwnerScraper("config.yaml")
        print(f"   ✅ Main scraper initialized")
        
        # Test monitoring status
        if hasattr(scraper, 'monitor') and scraper.monitor.enabled:
            print(f"   ✅ Monitoring system active")
            
            status = scraper.get_monitoring_status()
            print(f"   📊 Monitoring status retrieved")
            
            if 'system_metrics' in status:
                system = status['system_metrics']
                print(f"      Total scrapers: {system.total_scrapers}")
                print(f"      Active scrapers: {system.active_scrapers}")
        else:
            print(f"   ⚠️  Monitoring system not active")
        
        # Test fallback system
        if hasattr(scraper, 'fallback_system') and scraper.fallback_system.enabled:
            print(f"   ✅ Fallback system active")
            
            fallback_status = scraper.fallback_system.get_fallback_status()
            print(f"   🔄 Fallback rules: {fallback_status['rules_count']}")
        else:
            print(f"   ⚠️  Fallback system not active")
        
        # Test cleanup
        scraper.cleanup()
        print(f"   ✅ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during integration test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    # Set up logging
    logging.basicConfig(
        level=logging.WARNING,  # Reduce log noise
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    print("🚀 MONITORING AND FALLBACK SYSTEMS TEST SUITE")
    print("=" * 80)
    
    # Test monitoring system
    monitoring_success = test_monitoring_system()
    
    # Test fallback system
    fallback_success = test_fallback_system()
    
    # Test integration
    integration_success = test_integration()
    
    # Final summary
    print("\n" + "=" * 80)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 80)
    
    print(f"✅ Monitoring System: {'PASSED' if monitoring_success else 'FAILED'}")
    print(f"✅ Fallback System: {'PASSED' if fallback_success else 'FAILED'}")
    print(f"✅ Integration Test: {'PASSED' if integration_success else 'FAILED'}")
    
    if monitoring_success and fallback_success and integration_success:
        print(f"\n🎉 LOW PRIORITY TASK 5 COMPLETED: Monitoring and Fallback Systems")
        print(f"   ✅ Comprehensive monitoring system implemented")
        print(f"   ✅ Multi-tier fallback system created")
        print(f"   ✅ Health checking and alerting added")
        print(f"   ✅ Full integration with main scraper")
        print(f"   ✅ Real-time metrics and reporting")
        print(f"\n🎉 ALL PRIORITY TASKS COMPLETED SUCCESSFULLY!")
    else:
        print(f"\n❌ Monitoring and fallback systems test failed")
    
    sys.exit(0 if (monitoring_success and fallback_success and integration_success) else 1)
