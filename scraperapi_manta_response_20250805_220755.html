<!DOCTYPE html>
<html lang="en">
  <head><script>(function(w,i,g){w[g]=w[g]||[];if(typeof w[g].push=='function')w[g].push(i)})
(window,'GTM-R7B4','google_tags_first_party');</script><script>(function(w,d,s,l){w[l]=w[l]||[];(function(){w[l].push(arguments);})('set', 'developer_id.dY2E1Nz', true);
		var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s);j.async=true;j.src='/pxhh/';
		f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer');</script>
      <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Restaurants and Bars | United States - Manta.com</title>
<link rel="icon" href="https://cc3.manta-r3.com/assets-gz/2e2a97f56/img/favicon.png" type="image/png">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com">
<link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/app.css">
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css"></noscript>
<style>
  iframe[name=d_ifrm] {
  display: none;
  }
</style>
        <script rel="gtm-render">
      var screenWidth = screen.width;
      var sSz = 'lg';
      var wsSz = 'lg';
      var wSz = 'lg';
      if (screenWidth < 768) {
        sSz = wsSz = wSz = 'xs';
      } else if (screenWidth < 992) {
        sSz = wsSz = wSz = 'sm';
      } else if (screenWidth < 1200) {
        sSz = wsSz = wSz = 'md';
      }

      var gtmData = {
        ua_property: "UA-10299948-11",
        googleExperimentId: "",
        googleExperimentVariation: "-1",
        pageTitle: "Restaurants and Bars | United States - Manta.com",
        page_type: "company-content", // TODO: make this work when we do search/browse
        is_pagespeed: Boolean('false'),

        
        visitor_id: "d33bfa45-92e4-49f7-9692-1682f6cabaf9, city=j:null",
        customer_segment: "sbo",
        page_depth: "2",
        scr_win_width: sSz + '-' + wSz,

        
        treatment: "no-test",

                  altTreatment1: "LM $49 Price Test CONTROL",
                  altTreatment2: "",
                  altTreatment3: "",
        
        ip: "**************",

                  // Older cookies might not have stateAbbrv and countryAbbrv,
          // so fall back to state and country
          user_state: "null",
          user_country: "US",
        
        url_hash: window.location.hash,
        timestamp: new Date().toString(),
        sbi: "false",
        statusCode: "200",  // TODO: actual status
      };

              var dataLayer = [gtmData];
      
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-R7B4');
    </script>
    <script>
      var gaTrack = function (category, action, label, value, interactive) {
        if (arguments.length === 1) {
          // Non-event tracking stuff
          dataLayer.push(category);
          return;
        } else if (arguments.length === 3) {
          value = 1;
          interactive = true;
        }
        if (arguments.length === 4) {
          if (typeof value === 'boolean') {
            interactive = value;
            value = 1;
          } else {
            interactive = true;
          }
        }

        // if label is an object, serialize it into name=value pairs
        if (typeof label === 'object' && !Array.isArray(label)) {
          var pairs = [];
          for (var key in label) {
            pairs.push(key + '=' + label[key]);
          }
          label = pairs.join(',');
        }

        dataLayer.push({
          eventData: {
            category: category,
            action: action,
            label: label,
            value: value
          },
          event: (interactive ? 'interactive' : 'non-interactive') + ' event'
        });
      };
    </script>
  <script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/cash.min.js"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/js.cookie.min.js" onload="Cookies.defaults = { path: '/', domain: '.manta.com' }"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/axios.min.js" onload="window.axios = window.redaxios"></script>
<script>var loadScript=(function(d,s,c,e,f){return function(u){if(c[u]){return c[u];}e=d.createElement(s);e.async=!0;e.src=u;f=d.getElementsByTagName(s)[0];f.parentNode.insertBefore(e,f);return c[u]=e;};})(document,'script',{});</script>
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css"></noscript>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/simpleLightbox.min.js"></script>
<script>
  window.__PRELOADED_STATE__ = {
    toggledFeatures: {"experimentId":"","experimentVariation":"-1","forceGzip":false,"adsenseChannel":"1","scrollTracking":false,"exitTracking":true,"fakeMixpanel":true,"fakeGa":false,"useSubscriptionTestMode":false,"yextAllowDuplicatesMode":false,"allowSurveys":true,"isOffHoursOverride":false,"isBusinessHoursOverride":false,"skipDittoVendorRequests":false,"marketplaceJobs":false,"claimCtaText":"Own This Business?","claimCtaColor":"btn-primary","useLongBundleDesc":false,"isSiteMaintenanceMode":false,"homeyouLeads":false,"updoptProdOverride":false,"enableUpdoptDossier":true,"adsenseSlotMobile":"1160948055","adsenseSlotDesktop":"5730748454","useFapiForSubscriptions":false,"popOverlayOnCheckout":true,"useBuilderToAdd":false,"checkoutTemplate":"checkout","buyerlink_treatmentA":true,"adsenseHeroPlacement":true,"refreshAds":false,"show300x250":true,"showTaboola":true,"showDesktopAdhesionBanner":true,"homeyouWidgetPlacement":"about","newStack":true,"similarBusinessesUp":true,"reducedAdDensity":true,"navAffiliateBusinessCreditAd":true,"trackNumbers":true,"homeyouNumberTest":true,"homeyouTrackingNumber":"+***********","unclaimedStatic":true,"includeGMBScan":true,"postponeMerchantScans":true,"embedYextDashboard":true,"showPaywallPage":false,"checkoutPaywallTreatment":"paywall-control","enableWebsiteAddOn":true,"useBriteVerify":true,"useGooglePlacesInClaimBuilder":true,"useGoogleMaps":true,"useGoogleAutocomplete":true,"enableFacebookSignIn":true,"enableGoogleSignIn":true,"logCookieErrors":true,"rightRailDoubleAds":true,"triggerDemoScheduler":true,"showCovid":true,"covidShow":true,"es_search":true,"es_related":true,"useNewMemberDashboard":true,"showDetailedDescription":true,"useInternationalSearch":true,"useNewEditPage":true,"useElasticMb":true,"showMarketStats":false,"useNewAnalyticsService":true,"useElasticWorld":true,"requireTermsOfService":true,"useTaboolaAds":false,"usePlaywire":true,"adSenseSearchPages":true,"adSenseProfilePages":true,"oldSurveyModal":true,"useRepSalesDashboard":true,"blockSICM":true,"useNewCheckout":true,"showBanner":false,"copyTextBanner":"Promo Banner","redeemByTextBanner":"Redeem by 19th march","expiryDateBanner":"03/18/2023","catchPhraseBanner":"Special Offer!","validForBanner":"hasDitto, hasLmReviews, hasWebsite, hasPpcAds, hasDisplayAds, hasFeaturelessPlan, freeUser","toggleUrlCheckout":true,"changeVersion3":true,"showReviewTile":false,"showAdapexAds":true},
    referral_source: '',
    abTreatment: 'no-test',
    gamNetworkCode: '6009',
    visitor: {"ip":"**************","id":"d33bfa45-92e4-49f7-9692-1682f6cabaf9, city=j:null","pageDepth":2,"customerSegment":{"threshold":7,"id":"s","label":"sbo"},"smallBusinessInterest":false,"xri":"c28c60d537c09a6219045e7f2c7bc8da"},
    pageComponents: {},
    clientIp: '**************',
    isCalifornia: false,
    isDev: false,
    env: 'production',
    member: undefined  };
</script>
<script>
  var cache = {};
  window.logError = function(e, info) {
    var lines = (e.stack || '').split('\n');
    var callsite = lines.length > 1 ? lines[1].match(/(app\.js:\d+:\d+)/) : null;
    var key = e.message + (callsite && callsite[1] ? ' at ' + callsite[1] : '');

    if (!cache[key]) {
      try {
        window.axios && axios.post('/fapi/errors', {
          message: e.message || 'Unknown error',
          stack: e.stack || 'No stack trace available',
          info: info,
                    userAgent: (window.navigator && window.navigator.userAgent) || 'unknown'
        }).catch(function() {});
      } catch (e) {
        // Obviously, this isn't async/await, so it won't catch the
        // axios call, but I just want to _assure_ we don't throw
        // from the onerror handler.
      }
    }
  };
  window.onerror = function(message, source, lineno, colno, error) {
    // Don't log errors that come from ads and crap like that
    if (source.indexOf('manta.com') > -1) {
      logError(error, { source: source, lineno: lineno, colno: colno });
    }
  };
</script>
<!-- The script below is going to be commented until we figure out what could be a better implementation talking about performance -->
<!-- <script defer src="//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js" importance="low"></script> --><script defer src="https://btloader.com/tag?o=5150306120761344&upapi=true"></script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>

      <script data-cfasync="false">
        window.ramp = window.ramp || {};
        window.ramp.que = window.ramp.que || [];
      </script>
    
    <script type="text/javascript">
      window.ramp = window.ramp || {};
      window.ramp.que = window.ramp.que || [];
    </script>
          <script>
  if($) {
    $.fn.swapWithNext = function() {
      this.hide();
      this.next().removeClass('hidden');
    }
  }
</script>
  <script>
    var maTrack = (function() { 
      return function(xri, type, listings, event, stopPropagation) {
        if (event && stopPropagation) {
          event.stopPropagation();
        }
        var params = {
          listings: typeof listings === 'string' ? JSON.parse(listings) : listings,
          t: type + (screen.width < 992 ? '_mobile' : ''),
          ts: Date.now(),
          total: listings.length
        };

        var fp = JSON.stringify(params);

        typeof axios === 'function' && axios({
          url: '/track',
          method: 'GET',
          params: { fp: fp },
          withCredentials: true,
          headers: { 'x-request-id': xri }
        }).catch(function(e) {
          logError(e, { trackData: params });
        });
      };
    })();
  </script>
  <script>
    var mantaTrack = (function() {
      var mat = {};

      mat.xri = 'c28c60d537c09a6219045e7f2c7bc8da';
      mat.device = screen.width < 992 ? 'mobile' : 'desktop';
                        
      mat.trackView = (function() {
        return function(context) {
          var events = [{
            emid: mat.emid,
            type: 'view',
            data: {
              context: context,
              device: mat.device,
              sicm: mat.sicm,
              city_code: mat.city_code,
              claim_source: mat.claim_source
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackClick = (function() {
        return function(category, context, section) {
          var events = [{
            emid: mat.emid,
            type: 'click',
            data: {
              context: context,
              device: mat.device,
              city_code: mat.city_code,
              sicm: mat.sicm,
              claim_source: mat.claim_source,
              category: category,
              section: section
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackSearch = (function() {
        return function(context, city, sicm, emid) {
          let events = [];
          var values = [{
            emid: emid,
            type: "view",
            data: {
              context: context,
              device: mat.device,
              city: city,
              sicm: sicm
            }
          }];
          values.forEach(val => {
            events.push(val)
          });
          return mantaTrack(events);
        }
      })();
      
      mat.trackAndGo = (function() {
        return function(location, category, context, section) {
          mat.trackClick(category, context, section);
          window.location.href = location;
        }
      })();

      var mantaTrack = (function() {
          return function(events) {
                  return true;
                };
      })();

      return mat;

    })();
  </script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        .store-logo {
          position: relative;
          overflow: hidden;
          width: 100px;
        }
        .store-logo:before {
          content: "";
          display: block;
          padding-top: 100%;
        }
        .store-logo > div {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          text-align: center;
        }
      </style>
  </head>
  <body class="bg-primary-light-v1 text-gray-800">
    <div class="relative">
  <a href="#start-of-content" class="text-xs text-darks-v1 focus:text-white absolute right-100">Skip to Content</a>
</div>
<style>
      .desktop-search-wrapper {
      width: 380px;
    }
    @media(min-width: 1110px) {
      .desktop-search-wrapper {
        width: 480px;
      }
    }
  
</style>

<header>
  <div class="mobile-menu hidden fixed w-screen h-screen bg-white p-4 z-50 overflow-auto">
    <div class="float-right" onclick="$('.mobile-menu').addClass('hidden'); $('body').removeClass('overflow-hidden')"><i class="text-text-darks-v1 text-3xl fa fa-times"></i></div>
    <ul class="text-gray-600 my-16 text-lg">
  <li class="mb-2 hover:font-bold">
    <a href="/services">For Businesses</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/free-business-listing">Free Company Listing</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/listings-management">Premium Business Listings</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/small-business-marketing/websites">Websites</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/organic-seo-company">SEO</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/affordable-local-seo">Local SEO</a>
  </li>
  <li>
    <a href="/services/national-seo-company">National SEO</a>
  </li>
</ul>
      <div class="flex flex-col lg:flex-row">
    <a class="btn bg-primary-light-v1 text-gray-800 font-bold flex-1 mb-4 py-3" href="/member/login">Log In</a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="mb-4 btn bg-primary-v1 text-white inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
      </div>
  </div>

  <div class="mobile-search hidden fixed w-screen h-screen bg-white z-50 overflow-y-scroll">
  <div class="justify-center py-3 mx-auto max-w-header flex items-center bg-darks-v1 px-4 relative">
    <div onclick="$('.mobile-search').addClass('hidden');$('.pre-mobile-search').removeClass('hidden')" class="lg:hidden text-primary-light-v1 cursor-pointer absolute left-0 top-0 mt-6 ml-5">Cancel</div>
    <div class="flex sm:pr-3">
      <a href="/" data-test="btn-logo-navbar">
        <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo" width="109" height="25">
      </a>
    </div>
  </div>
  <div class="bg-darks-v1 text-gray-dark search-component-mobile"></div>
</div>

  <div class="px-6 bg-darks-v1 h-auto text-white">
    <div class="justify-between py-3 mx-auto max-w-header flex items-center">

      <div onclick="$('.pre-mobile-search').addClass('hidden');loadSearchBar('.mobile-search')" class="flex md:hidden"><i class="text-2xl fa fa-search"></i></div>
      <div>
  <a href="/" data-test="btn-logo-navbar">
    <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo"  width="109" height="25" >
    <span class="sr-only">Manta Home</span>
  </a>
</div>
      <div class="hidden md:inline-block desktop-search-wrapper">
        <div class="rounded text-gray-dark hidden md:inline-block search-component w-full">
          <form name="searchForm">
  <div class="flex flex-col sm:flex-row px-3 sm:px-0" style="border-radius: 4px 4px 4px 0px;">
    <div class="flex sm:w-1/2 relative px-3 py-2 bg-white my-1 sm:my-0 rounded sm:rounded-l-lg sm:rounded-r-none">
      <div class="flex justify-center items-center mr-4 w-5">
        <span class="text-primary-v1 fa fa-search text-xl"></span>
      </div>
      <div class="flex w-full">
        <label for="header-search" class="sr-only">Search</label>
        <input
          id="header-search"
          name="search"
          placeholder="I'm looking for..."
          class="w-full outline-none"
          onfocus="loadSearchBar('.search-services-menu')"
          autocomplete="off"
          value=""
        />
      </div>
      <div class="absolute search-services-menu hidden" style="z-index: 10000">
        <ul class="p-0 m-0 text-gray-600">
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-utensils mr-3"></span
            ></span>
            <span class="text-small">Restaurants</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-stethoscope mr-3"></span
            ></span>
            <span class="text-small">Doctors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-gavel mr-3"></span
            ></span>
            <span class="text-small">Lawyers</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-hammer mr-3"></span
            ></span>
            <span class="text-small">Contractors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"><span class="fa fa-car mr-3"></span></span>
            <span class="text-small">Automotive</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-tooth mr-3"></span
            ></span>
            <span class="text-small">Dentists</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex py-2 sm:py-0 sm:w-1/2">
      <div class="flex w-full relative px-3 sm:pl-0 sm:pr-3 py-2 bg-white rounded-l-lg sm:rounded-none">
        <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
        <div class="self-center w-full text-gray-800">
          <label for="header-location" class="sr-only">Location</label>
          <input
            id="header-location"
            name="location"
            placeholder="City, State, Country, Zip"
            class="w-full outline-none"
            onfocus="loadSearchBar('.search-location-menu')"
            autocomplete="off"
            value=""
          />
        </div>
        <div class="search-location-menu hidden" style="z-index: 10000">
          <ul class="m-0 p-0 locations">
            <li
              class="px-4 py-3 text-primary-v1 hover:bg-gray-200 cursor-pointer"
            >
              <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
              <span class="small loc-name">Current Location</span>
            </li>
          </ul>
        </div>
      </div>
      <button
        type="submit"
        class="sm:flex items-center justify-center bg-primary-v1 text-white px-3 py-2 overflow-hidden rounded-r-lg">
        <span class="sr-only">Search</span>
        <span class="not-sr-only text-white fa fa-search text-xl"></span>
      </button>
    </div>
  </div>
</form>
<script>
  (function () {
    var loc = localStorage.getItem("locHistory");
    if (loc) {
      var li = $(
        '<li class="px-4 py-3 text-gray-600 hover:bg-gray-200 cursor-pointer border-t border-gray-300 sm:border-none"><span class="fa fa-clock mr-4"></span><span class="small loc-name">Current Location</span></li>'
      );
      JSON.parse(loc).forEach(function (l) {
        if (l.stateAbbrv) {
          li.find(".loc-name").text(l.formatted);
          $(".locations").append(li.clone());
        }
      });
    }
  })();
  var loadSearchBar = (function () {
    return function (c) {
      $(c).removeClass("hidden");
      loadScript("//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js");
    };
  })();
  $("form[name=searchForm]").on("submit", function (e) {
    e.preventDefault();
    var search = $("input[name=search]").val();
    var locationInput = $("input[name=location]").val().trim();
    if (!locationInput) {
      const lastGeo = Cookies.get('lastGeo');
      if (lastGeo) {
        try {
          const geo = JSON.parse(lastGeo);
          if (geo && geo.city && geo.stateAbbrv) locationInput = `${geo.city}, ${geo.stateAbbrv}`;
        } catch (e) {
          return;
        }
      }
    };

    if (!locationInput) return;

    var device = "desktop";
    if (window.screen.availWidth <= 500) {
      device = "mobile";
    } else if (window.screen.availWidth <= 1024) {
      device = "tablet";
    }
    var parts = locationInput.split(/[, ]+/);
    var state = parts.pop();
    var city = parts.join(" ");
    window.location =
      "/search?search_source=nav&search=" +
      encodeURIComponent(search) +
      "&city=" +
      encodeURIComponent(city) +
      "&state=" +
      encodeURIComponent(state) +
      "&device=" +
      device +
      "&screenResolution=" +
      window.screen.availWidth +
      "x" +
      window.screen.availHeight;
  });
</script>
        </div>
      </div>

      <div class="hidden lg:block text-sm">
        <div data-test="btn-products-navbar-desktop" class="dropdown inline-block py-4 text-primary-light-v1">
          <a data-test="btn-findBusiness-navbar-desktop" class="hover:underline font-bold px-3" href="/services">For Businesses <i class="fa fa-angle-down"></i></a>
          <div class="dropdown-tri"></div>
          <ul class="dropdown-menu py-2 text-nordic-v1">
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/free-business-listing">Free Company Listing</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/listings-management">Premium Business Listings</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/small-business-marketing/websites">Websites</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/organic-seo-company">SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/affordable-local-seo">Local SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/national-seo-company">National SEO</a></li>
          </ul>
        </div>
            <a data-test="btn-login-navbar-desktop" class="hover:underline text-primary-light-v1 font-bold"
    href="/member/login"><span class="lg:px-1 px-3 xl:px-3">Log In</span></a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="px-5 lg:px-2 xl:px-5 py-2 w-auto rounded cursor-pointer text-center  bg-primary-v1 text-white mx-3 lg:ml-3 lg:mr-2 xl:mx-3 inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
        </div>

      <div onclick="$('.mobile-menu').removeClass('hidden');" class="flex lg:hidden"><i class="text-2xl fa fa-bars"></i></div>
    </div>
      </div>

  <div class="pl-0 lg:px-6 bg-primary-dark text-white overflow-x-hidden faded faded-x-primary-dark hidden">
  <div class="py-1 mx-auto max-w-header flex items-center overflow-x-auto">
    <div class="inline-block py-2 text-sm whitespace-no-wrap ml-5">
      <a class="cursor-pointer" href="/mb_33_A6_000/professional_services">Business Services<span class="align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_C4_000/restaurants_and_bars">Food & Beverage<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_B3_000/consumer_services">Consumer Products & Services<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_D0_000/healthcare">Health<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_G4_000/information_technology">Tech & Communications<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer mr-5" href="/mb_33_E6_000/industrial_machinery">Industrial</a>
    </div>
  </div>
</div>
  

</header>
    <main>
      <div class="bg-white w-screen lg:mx-auto pb-8 pt-12 flex flex-row justify-center">
        <div class="flex flex-col w-page mx-4 xl:mx-0">
          <h1 class="text-3xl font-serif text-gray-900">Restaurants and Bars</h1>
          <p>
            <span>Manta has 597,603 businesses under Restaurants and Bars in</span>
                                          <span>the</span>
                            <span>United States</span>
                      </p>
        </div>
      </div>
      <div class="w-screen lg:w-page lg:mx-auto">
                <h1 class="text-2xl mx-4 xl:mx-0 font-serif text-gray-900">All Company Listings</h1>
                  <div data-test="mb-result-card-m1r4dpn" class="md:rounded bg-white border-b  border-t  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20230127vE4A_MiqV4)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1r4dpn" href="/c/m1r4dpn/gallery-grill-poke-house-llc" class="cursor-pointer font-serif text-gray-900 mr-2">Gallery Grill Poke House LLC</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">13 West Centre Street</div>
                                            <div>Baltimore, MD</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fgallerygrillpokehouse.com&s=454bbfecd14fa3c7e0f819e8ebe89138&cb=1874450" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4105394026" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Gallery%20Grill%20Poke%20House%20LLC,+13%20West%20Centre%20Street+Baltimore" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fgallerygrillpokehouse.com&s=454bbfecd14fa3c7e0f819e8ebe89138&cb=1874450" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Poke Bowls</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sushi</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sushi Boxes</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Poke</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sushi Rolls</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">All of our poke and sushi is made with high quality seafood to ensure the best and freshest experience.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm57lcy" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm57lcy" href="/c/mm57lcy/wall-street-north" class="cursor-pointer font-serif text-gray-900 mr-2">Wall Street North</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">100 North Jackson Street</div>
                                            <div>Athens, GA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:7062027734" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Wall%20Street%20North,+100%20North%20Jackson%20Street+Athens" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Night Clubs</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mkc69rs" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkc69rs" href="/c/mkc69rs/the-galleria-infused-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">The Galleria Infused Restaurant</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">757 East Oakwood Blvd</div>
                                            <div>Chicago, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.TheGalleriaInfusedRestaurant.com&s=ae8b60fcbd6067ac4f6674287086a758&cb=1874450" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3122755772" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=The%20Galleria%20Infused%20Restaurant,+757%20East%20Oakwood%20Blvd+Chicago" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.TheGalleriaInfusedRestaurant.com&s=ae8b60fcbd6067ac4f6674287086a758&cb=1874450" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Food Bars</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mh16tjl" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mh16tjl" href="/c/mh16tjl/laura-s-three-spoon" class="cursor-pointer font-serif text-gray-900 mr-2">LAURA'S THREE SPOON</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">8538 TERMINAL ROAD</div>
                                            <div>Lorton, VA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.laurasthreespoon.com%2F&s=cada442c9e6a72f47a0b3c1e7ec3f99d&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9292742312" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=LAURA'S%20THREE%20SPOON,+8538%20TERMINAL%20ROAD+Lorton" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.laurasthreespoon.com%2F&s=cada442c9e6a72f47a0b3c1e7ec3f99d&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Best CHEESECAKE</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmf5qg5" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20210104EDTONSfBUn)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmf5qg5" href="/c/mmf5qg5/coasters-coffee-co" class="cursor-pointer font-serif text-gray-900 mr-2">Coasters Coffee Co</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1007 Q St highway 34</div>
                                            <div>Aurora, NE</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fcoasterscoffeeco.com%2F&s=0d5dc36082e3233eebc86b68916c70ff&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4023639084" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Coasters%20Coffee%20Co,+1007%20Q%20St%20highway%2034+Aurora" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fcoasterscoffeeco.com%2F&s=0d5dc36082e3233eebc86b68916c70ff&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Coffee Shops</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">We don’t use powders in our frappes, we don’t use artificial flavors in our lattes, we use great tasting, pure, real ingredients as our customers deserve only the best. 

We are a locally owned coffee shop that was established in 2018 with great drinks, great food, great atmosphere, and great customer service as our goal. We are proud to be a small business in our town of Aurora, Nebraska, and welcome everyone through our doors with a smile.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm6r2ps" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20191101QlGpbE_X71)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm6r2ps" href="/c/mm6r2ps/tropical-breeze-bar-and-grill" class="cursor-pointer font-serif text-gray-900 mr-2">Tropical Breeze bar and grill</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1004 Zimalcrest Drive</div>
                                            <div>Columbia, SC</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:8033684948" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Tropical%20Breeze%20bar%20and%20grill,+1004%20Zimalcrest%20Drive+Columbia" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Grills (Eating Places)</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mk2g2l4" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20210806j13jVF91tT)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2g2l4" href="/c/mk2g2l4/el-vaquero-kirksville" class="cursor-pointer font-serif text-gray-900 mr-2">El Vaquero Kirksville</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2020 N Baltimore St</div>
                                            <div>Kirksville, MO</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Felvaquerokv.business.site&s=bd53066ef1ebbd596e21d7eca7c99864&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6606650256" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=El%20Vaquero%20Kirksville,+2020%20N%20Baltimore%20St+Kirksville" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Felvaquerokv.business.site&s=bd53066ef1ebbd596e21d7eca7c99864&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Fajitas </span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Chips And Salsa</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Queso</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Beer</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Margaritas</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">El Vaquero was founded out of our love and passion for catering delicious food made with only the freshest ingredients to our local communities. We pride ourselves in creating flavors that will make your mouth sing. We also are known for our gorgeous presentations and exceptional catering services. Let us make your next event memorable, easy and have everyone raving about the cuisine. We hope to hear from you soon!</div>
    </div>
  </div>                  <div data-test="mb-result-card-mx2hm50" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20120813n31uZmv3MN)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx2hm50" href="/c/mx2hm50/crossroads-of-ivanhoe" class="cursor-pointer font-serif text-gray-900 mr-2">Crossroads of Ivanhoe</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">20915 W Park</div>
                                            <div>Mundelein, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fcrossroadsofivanhoe.com&s=484efef2452a82cece64a36d434419e9&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8479499009" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Crossroads%20of%20Ivanhoe,+20915%20W%20Park+Mundelein" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fcrossroadsofivanhoe.com&s=484efef2452a82cece64a36d434419e9&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Eating places</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">The original Emil's Sports Bar and Pizza was established in 1978 by the Bowes family, with the idea that people in the community have a cozy eatery where they could relax and enjoy great food with friends and family at affordable prices. Since then, Emil's Sports Bar and Pizza in Mundelein has become a staple in Lake County.

In 1986, the Bowes family purchased "The Ivanhoe Inn", a cozy tavern just west of Mundelein at the "crossroads" of 60/83 & 176. The now CROSSROADS OF IVANHOE offerings include a full breakfast, lunch and dinner menu. CROSSROADS has become well known for their Fish Fry, Garlic Steamed Mussels, Giant Mozzarella Stix, Breakfast Skillets and many Homestyle Daily Specials.

Come on in and enjoy great food with family, friends and your neighbors at the Crossroads.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1whlv4" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20231107nRDG8Dtkxo)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1whlv4" href="/c/m1whlv4/hot-spicy-cuisine" class="cursor-pointer font-serif text-gray-900 mr-2">Hot Spicy Cuisine</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2114 Wolf Run Rd</div>
                                            <div>Cameron, WV</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.hotspicycuisine.com&s=81f8ace6d2503b66e2a086be963f4c5e&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3044587527" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Hot%20Spicy%20Cuisine,+2114%20Wolf%20Run%20Rd+Cameron" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.hotspicycuisine.com&s=81f8ace6d2503b66e2a086be963f4c5e&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Welcome to Hot Spicy Cuisine. We are a premier catering company offering our services in West Virginia and the surrounding areas. With both casual and sophisticated food options we are able to cater an expansive array of affairs. From extravagant weddings to small backyard get-together's, our catering consultants work closely with you to ensure your vision is brought to life.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1x1tf1" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1x1tf1" href="/c/m1x1tf1/fondue-chinoise" class="cursor-pointer font-serif text-gray-900 mr-2">Fondue Chinoise</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:100%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(1)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">430 Broadway</div>
                                            <div>San Francisco, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Ffonduesf.com&s=3b361a5344b31ff4d4b1414f974e2eae&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4152178888" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Fondue%20Chinoise,+430%20Broadway+San%20Francisco" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Ffonduesf.com&s=3b361a5344b31ff4d4b1414f974e2eae&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Classic Chicken Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Spicy Sichuan Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Herbal Mushroom Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Tomato Broth</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Bone Marrow Broth</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Guest cook thinly sliced meats, seafood, tofu, and vegetables directly in the broth at the table, customizing each bite.

A wide variety of dipping sauces—like sesame, soy, and chili oil—enhance the flavors and offer endless combinations

The experience is interactive, engaging, and ideal for gatherings with family or friends. Fondue Chinoise is not only fun, but also a healthy way to eat, as ingredients are cooked quickly with minimal oil, preserving their natural taste and nutrients. 

Different broths cater to all preferences, from mild herbal to spicy Sichuan. This style of dining promotes connection and conversation, turning every meal into a memorable event.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkcdzxs" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/202008207u1CpJcPDl)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkcdzxs" href="/c/mkcdzxs/la-styles-delivery" class="cursor-pointer font-serif text-gray-900 mr-2">LA Styles Delivery</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Harrisburg, PA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.lastylesdelivery.com&s=35ffd996873795a08bd42aaf31ffa62a&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4044875244" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                                  <a href="/urlverify?redirect=http%3A%2F%2Fwww.lastylesdelivery.com&s=35ffd996873795a08bd42aaf31ffa62a&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Hot Food</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Hot And Fresh</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Grocery Delivery</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Delivery </span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Delivery Near Me</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Located in Harrisburg, PA, we are a delivery business servicing our customers at their door and on their schedule

Whether we deliver to your home or place of business, our deliveries are designed to be affordable and on-time. Check for your favorite local grocery & restaurants on the site, and if you don’t see your favorites, just let us know! We are always adding local businesses to our site!</div>
    </div>
  </div>                  <div data-test="mb-result-card-mkbw3qv" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/202004168zrQsKBeaL)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mkbw3qv" href="/c/mkbw3qv/jimmyls-bbq-catering" class="cursor-pointer font-serif text-gray-900 mr-2">JimmyLs BBQ & Catering</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                                            <div>Sidney, NE</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.jimmyls.com&s=31e96fb96b39e8cebb7891fd7f4601e6&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3082491765" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                                  <a href="/urlverify?redirect=http%3A%2F%2Fwww.jimmyls.com&s=31e96fb96b39e8cebb7891fd7f4601e6&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Caterers</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Providing Nebraska, Colorado and Wyoming excellent food service. From catering to vending we look forward to serving our customers needs.

Bringing you the best BBQ and hospitality Western Nebraska has to offer.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk2ddtc" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20210904NyW3MAYSNv)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2ddtc" href="/c/mk2ddtc/china-king-illinois-chinese-asian-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">China King Illinois Chinese - Asian Restaurant</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">646 N Market St</div>
                                            <div>Waterloo, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fchinakingillinois.restaurant&s=f54499d4425529ee12f0ce60b9854def&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6189399888" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=China%20King%20Illinois%20Chinese%20-%20Asian%20Restaurant,+646%20N%20Market%20St+Waterloo" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fchinakingillinois.restaurant&s=f54499d4425529ee12f0ce60b9854def&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Egg Drop Soup</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Asian Restaurant </span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Chinese Food Delivery</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Chinese Food Takeaway</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Chinese Restaurant Dine In</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Order Chinese Food Online for Takeout / Delivery/ Dine-in.
Here at China King Illinois - you'll experience delicious Chinese food.
Taste our fresh & hot dishes, carefully prepared with fresh ingredients!
At China King Illinois, our secret to winning your heart is simple – Fantastic food & great customer service makes you return every time and craving for more Chinese food.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk2dqmj" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20210909X76xP6CSqq)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2dqmj" href="/c/mk2dqmj/bumblebees-bbq-grill" class="cursor-pointer font-serif text-gray-900 mr-2">Bumblebees BBQ & Grill</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">7962 S State Street</div>
                                            <div>Midvale, UT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fbumblebeeskbbq.com&s=df06711433e458ba16f8524db4f6ca82&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8015610608" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Bumblebees%20BBQ%20%26%20Grill,+7962%20S%20State%20Street+Midvale" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fbumblebeeskbbq.com&s=df06711433e458ba16f8524db4f6ca82&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Burgers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">K-Philly Sandwiches</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">K-Pop Fries</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Dup-Bop</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Tacos</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Fusing Korean BBQ with American style dishes we've created a fun range of unique food for your taste buds. We dish up anything ranging from Kimchi Hamburgers, Korean BBQ loaded fries, or Korean BBQ style Philly Cheesesteaks.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk2dcb4" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20210809XLv4ZQPKrO)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk2dcb4" href="/c/mk2dcb4/barpizzaque" class="cursor-pointer font-serif text-gray-900 mr-2">BarPizzaQue</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">6925 West 111th Street</div>
                                            <div>Worth, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fbarpizzaque.com%2F&s=9974eb05b93f91cce43484e3781bbf72&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7088665151" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=BarPizzaQue,+6925%20West%20111th%20Street+Worth" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fbarpizzaque.com%2F&s=9974eb05b93f91cce43484e3781bbf72&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Seafood</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Pizza</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Pasta</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Mediterranean Meals</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Nachos</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">BarPizzaQue became known for a great selection of different kinds of dishes that gather between Mediterranean Halal food, American, and seafood. We serve “SeaFood Boil,” which is a very special seafood combination of your choice of crab legs, shrimp or mussels and lobster served with garlic sauce, mild or spicy. It is an inspired dish made of high-quality seafood and special ingredients. You can choose our Mediterranean menu that includes a wide variety of our offerings from rich appetizers, such as yummy Falafel, creamy hummus, Tabouleh, Shawarma, to grill combinations and special Arabic breakfast.This menu gathers between the authenticity of the oriental food and the modern touch of our creative chefs.  We also serve the best pizza in town and fast food. BarPizzaQue is the perfect blend of different Medittrranean regions with a splash of American cuisine. You’ll be satisfied no matter what you’re looking for.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mk39z8f" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20220308sQEOBdnpIq)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mk39z8f" href="/c/mk39z8f/surah-steakhouse" class="cursor-pointer font-serif text-gray-900 mr-2">Surah Steakhouse</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">101 Ridgeside Ct.</div>
                                            <div>Mount Airy, MD</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=https%3A%2F%2Fwww.mtsurah.com%2F&s=a1c276658f31f7350f205ff2a9b6d6d8&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3018295682" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Surah%20Steakhouse,+101%20Ridgeside%20Ct.+Mount%20Airy" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=https%3A%2F%2Fwww.mtsurah.com%2F&s=a1c276658f31f7350f205ff2a9b6d6d8&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Steakhouse </span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Surah Japanese Steak & Seafood falls into a familiar rhythm around mealtimes. Chefs man tabletop hibachi grills and sear platefuls of filet mignon, scallops, or chicken right in front of patrons while entertaining them with witty banter, dexterous displays of culinary skill, and their ability to peel shrimp telepathically. Meanwhile, the sushi chefs avoid open flames entirely as they carefully tuck lobster, spring mix, or wasabi aioli into their signature rolls. The entire staff matches the friendly, energetic service of the chefs, striving to greet every guest by name by their second or even first visit.

About Our Staff
Surah Japanese Steak & Seafood falls into a familiar rhythm around mealtimes. Chefs man tabletop hibachi grills and sear platefuls of filet mignon, scallops, or chicken right in front of patrons while entertaining them with witty banter, dexterous displays of culinary skill, and their ability to peel shrimp telepathically. Meanwhile, the sushi chefs avoid open flames entirely as they carefully tuck lobster, spring mix, or wasabi aioli into their signature rolls. The entire staff matches the friendly, energetic service of the chefs, striving to greet every guest by name by their second or even first visit.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1rc64w" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1rc64w" href="/c/m1rc64w/us-custom-concessions" class="cursor-pointer font-serif text-gray-900 mr-2">US Custom Concessions</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4601 N. Lois Ave</div>
                                            <div>Tampa, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fuscustomconcessions.com&s=cc85dbfccd7736b49e5d57aee4560d36&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8134426504" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=US%20Custom%20Concessions,+4601%20N.%20Lois%20Ave+Tampa" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fuscustomconcessions.com&s=cc85dbfccd7736b49e5d57aee4560d36&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">New Concession Trailers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Used Concessions Trailers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Concession Trailer Repair</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Mobile Concessions</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">US Custom Concessions is a builder of custom concession and mobile business trailers and trucks. We can custom build any design and any size trailer. Whether it is a single trailer or a fleet for your franchised business, we can handle all of it.
There are no builds too big or too small.</div>
    </div>
  </div>                  <div data-test="mb-result-card-m1x126n" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20250415tsLYFArhCW)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-m1x126n" href="/c/m1x126n/spicy-deluxe-catering" class="cursor-pointer font-serif text-gray-900 mr-2">Spicy Deluxe Catering</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:100%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(2)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">5913 E. Johnson Ave.</div>
                                            <div>Jonesboro, AR</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.spicydeluxecatering.com&s=b187b4792c31d3340643cb55efee9442&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8705684511" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Spicy%20Deluxe%20Catering,+5913%20E.%20Johnson%20Ave.+Jonesboro" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.spicydeluxecatering.com&s=b187b4792c31d3340643cb55efee9442&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Caterer</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Catering</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">From extravagant weddings to small backyard get-together's, our catering consultants work closely with you to ensure your vision is brought to life. It is safe to say that food is our passion – eating it, creating it, and serving it to you! Everything on our menu is prepared using the freshest ingredients and we aim to support local markets whenever possible. Having catered a wide range of events in the past, we are more than happy to provide you with suggestions and we look forward to helping you narrow down your menu ideas.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmg3kh9" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20221206p6xVv7VVyR)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmg3kh9" href="/c/mmg3kh9/eppies-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">Eppies Restaurant</a>
                      <div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>
                  </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:100%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(5)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4025 Lake Road</div>
                                            <div>West Sacramento, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=https%3A%2F%2Feppieswestsac.com&s=7208ab6193e10f3268e7dd874c093482&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9163717767" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Eppies%20Restaurant,+4025%20Lake%20Road+West%20Sacramento" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=https%3A%2F%2Feppieswestsac.com&s=7208ab6193e10f3268e7dd874c093482&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                          <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
  <i class="text-white fa fa-badge-check"></i>
</div>
              <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                MANTA VERIFIED
              </div>
                        </div>
                          <p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving west sacramento and the Surrounding Area</p>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Restaurant</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Eppies is the best family restaurant in West Sacramento. 
Family owned business with very good service and broad offerings. Large free parking lot. ADA compliant. Beer & Wine with meals. 
TV's in dining areas. 
Weekly Specials: Meatloaf Wednesday, Ribs Thursday, Prime Rib Friday.  Ask Server and see board at front for others.
 We are open for Indoor dining and Take-Out daily from 7:00 am to 8:30pm.
Menu available at https://eppieswestsac.com/eppies-menus/
Please Call 916-371-7767 to order.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm0z1bf" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm0z1bf" href="/c/mm0z1bf/nassau-bagel-co" class="cursor-pointer font-serif text-gray-900 mr-2">Nassau Bagel CO</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">179 Nassau St</div>
                                            <div>Princeton, NJ</div>
                          </div>
          </div>
                                  <div class="flex md:hidden mt-3">
                                      <a href="https://maps.google.com/maps?q=Nassau%20Bagel%20CO,+179%20Nassau%20St+Princeton" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Sushi Bars</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm2522l" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm2522l" href="/c/mm2522l/king-jade-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">King Jade Restaurant</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1229 Providence Road</div>
                                            <div>Whitinsville, MA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:5082345968" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=King%20Jade%20Restaurant,+1229%20Providence%20Road+Whitinsville" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Chinese Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm39b4k" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/201509107CjGqSN4Z4)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm39b4k" href="/c/mm39b4k/lovash-indian-restaurant-bar" class="cursor-pointer font-serif text-gray-900 mr-2">Lovash Indian Restaurant & Bar</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">236 South Street</div>
                                            <div>Philadelphia, PA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Flovashrestaurant.com%2F&s=d76b8db4278c502030ad43d4860d7f0b&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2155150066" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Lovash%20Indian%20Restaurant%20%26%20Bar,+236%20South%20Street+Philadelphia" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Flovashrestaurant.com%2F&s=d76b8db4278c502030ad43d4860d7f0b&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Indian Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Lovash is the perfect place to enjoy Indian cuisine in Philly, with an impressive menu of vibrant Indian dishes served in a friendly and inviting atmosphere. At Lovash, we take pride in serving a great variety of Indian food made with fresh, high-quality ingredients. We specialize in Halal food, and are vegan and vegetarian-friendly. Everything we serve is made fresh in-house, and we use only the finest vegetables and ingredients to make each dish. For the best authentic Indian cuisine in Philly, look no further than Lovash!</div>
    </div>
  </div>                  <div data-test="mb-result-card-mr3qxq3" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mr3qxq3" href="/c/mr3qxq3/j-r-bingo" class="cursor-pointer font-serif text-gray-900 mr-2">J & R Bingo</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2103 E Cone Blvd #105</div>
                                            <div>Greensboro, NC</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:3363755353" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=J%20%26%20R%20Bingo,+2103%20E%20Cone%20Blvd%20%23105+Greensboro" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Nail School</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Richard Henderson/Jean Henderson is proposing Bar, Nail School, Bingo Hall, Billiards and Cocktail Bar in Greensboro, NC.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm46h2v" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/201803202gPQ6KBTtM)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm46h2v" href="/c/mm46h2v/new-york-buffet" class="cursor-pointer font-serif text-gray-900 mr-2">New York Buffet</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">7900 NW 27th Ave</div>
                                            <div>Miami, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.chinesebuffetmiami.com&s=c627876f19b6aca6cdceca74c394986c&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3056968229" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=New%20York%20Buffet,+7900%20NW%2027th%20Ave+Miami" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.chinesebuffetmiami.com&s=c627876f19b6aca6cdceca74c394986c&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx229y8" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx229y8" href="/c/mx229y8/fatmamas-chicken-and-barbecue" class="cursor-pointer font-serif text-gray-900 mr-2">FatMamas Chicken and Barbecue</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">9 chruch avenue</div>
                                            <div>Oshkosh, WI</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9203551227" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=FatMamas%20Chicken%20and%20Barbecue,+9%20chruch%20avenue+Oshkosh" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Barbecue</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm31bvt" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm31bvt" href="/c/mm31bvt/natili-north-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">Natili North Restaurant</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">204 N Main Street</div>
                                            <div>Butler, PA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:7242832149" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Natili%20North%20Restaurant,+204%20N%20Main%20Street+Butler" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Italian Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mhpy2kq" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhpy2kq" href="/c/mhpy2kq/midnight-pizza" class="cursor-pointer font-serif text-gray-900 mr-2">Midnight Pizza</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center">
            <div class="text-primary-v1 font-bold mr-4">5</div>
            
    <div class="relative whitespace-no-wrap inline-block text-sm" style="width:95px; height: 21px;">
      <div class="absolute">
        <i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i><i class="fa fa-star text-primary-light-v1"></i>
      </div>
      <div class="absolute overflow-hidden" style="width:100%">
        <i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i><i class="fa fa-star text-primary-v1"></i>
      </div>
    </div>
              <div class="ml-4">(1)</div>
          </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">33516 N 75th Way</div>
                                            <div>Chicago, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:3424242322" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Midnight%20Pizza,+33516%20N%2075th%20Way+Chicago" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Pizza</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Best pizza at odd time Best pizza at odd time Best pizza at odd time Best pizza at odd time Best pizza at odd time Best pizza at odd time Best pizza at odd time Best pizza at odd time Best pizza at odd time Best pizza at odd time</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm2g2l0" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm2g2l0" href="/c/mm2g2l0/wendy-s" class="cursor-pointer font-serif text-gray-900 mr-2">Wendy's</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">7609 W Emerald Street</div>
                                            <div>Boise, ID</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2083367400" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Wendy's,+7609%20W%20Emerald%20Street+Boise" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Chain Fast Food Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mhp8gmp" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhp8gmp" href="/c/mhp8gmp/dickey-s" class="cursor-pointer font-serif text-gray-900 mr-2">Dickey's</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1586 Gateway Blvd.</div>
                                            <div>Fairfield, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Forder.dickeys.com%2Fmenu%2Fdickeys-fairfield&s=1ba4aa6f73b51d93db6dc5d4dd38a45d&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7074005040" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Dickey's,+1586%20Gateway%20Blvd.+Fairfield" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Forder.dickeys.com%2Fmenu%2Fdickeys-fairfield&s=1ba4aa6f73b51d93db6dc5d4dd38a45d&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Steak and Barbecue Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm8q65j" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20150824lNfPdqRhTo)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm8q65j" href="/c/mm8q65j/a-j-s-diner" class="cursor-pointer font-serif text-gray-900 mr-2">A J's Diner</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">7102 North Mesa St</div>
                                            <div>El Paso, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9155842514" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=A%20J's%20Diner,+7102%20North%20Mesa%20St+El%20Paso" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Diners</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mxcp3qg" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mxcp3qg" href="/c/mxcp3qg/thunder-joe-s-cafe" class="cursor-pointer font-serif text-gray-900 mr-2">Thunder Joe's Cafe</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1299 Main Street</div>
                                            <div>Susanville, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:5302504425" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Thunder%20Joe's%20Cafe,+1299%20Main%20Street+Susanville" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Bakery</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Deli</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Barbecued Pork</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Espresso</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mb0m5wf" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20161207gjaq5kPbzs)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mb0m5wf" href="/c/mb0m5wf/little-house" class="cursor-pointer font-serif text-gray-900 mr-2">Little House</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">417 Charlotte St #D</div>
                                            <div>Johnstown, CO</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Flittlehouseco.net&s=19696f63d4ec8ebc6e6940378e5fa0d8&cb=1874451" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9705870098" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Little%20House,+417%20Charlotte%20St%20%23D+Johnstown" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Flittlehouseco.net&s=19696f63d4ec8ebc6e6940378e5fa0d8&cb=1874451" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Ethnic Food Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx7wk5t" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20120203bRLIuZIjW2)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx7wk5t" href="/c/mx7wk5t/bout-time-pub-grub" class="cursor-pointer font-serif text-gray-900 mr-2">Bout Time Pub & Grub</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4002 S Highland Drive</div>
                                            <div>Salt Lake City, UT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.bouttimepub.com%2F&s=c6b50462a14f69d76abd5ebd24cc38eb&cb=1874452" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8012785100" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Bout%20Time%20Pub%20%26%20Grub,+4002%20S%20Highland%20Drive+Salt%20Lake%20City" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.bouttimepub.com%2F&s=c6b50462a14f69d76abd5ebd24cc38eb&cb=1874452" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sports Bar</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Bar And Grills</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Pubs</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Beer Gardens</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Pool Hall</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Welcome to 'Bout Time Pub & Grub!  We are a casual, locally owned sports pub serving up great food, great drinks, and great times.  Our food is unmatched, our service friendly and our atmosphere fun and exciting.
 
There is no better place to watch sports than in our pubs.  All of our locations are outfitted with the highest quality HD video equipment so your game will be crystal clear.  We carry the NFL Sunday ticket, MLB Extra Innings and the MLS Direct Kick  from Direct TV, so no matter who your team is, you will be able to cheer them on.  So come root for the Jazz, Utes, Real Salt Lake, or whatever team or sport you like to watch.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mtwnmpx" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20110330o5jRbcDf4e)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtwnmpx" href="/c/mtwnmpx/tropical-smoothie-cafe" class="cursor-pointer font-serif text-gray-900 mr-2">Tropical Smoothie Cafe</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3049 John Hawkins Parkway # 100</div>
                                            <div>Birmingham, AL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2054440612" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Tropical%20Smoothie%20Cafe,+3049%20John%20Hawkins%20Parkway%20%23%20100+Birmingham" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Birthdays</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Fundraisers</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Gourmet Shops</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mmcnqxg" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmcnqxg" href="/c/mmcnqxg/our-place-saloon" class="cursor-pointer font-serif text-gray-900 mr-2">Our Place Saloon</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">7651 Sw Highway 200 # 502</div>
                                            <div>Ocala, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:3528544711" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Our%20Place%20Saloon,+7651%20Sw%20Highway%20200%20%23%20502+Ocala" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Saloons</span>
          </div>
              </div>
    </div>
  </div>
  </div>                <div id="more-results">
        </div>
        <div data-test="mb-infinite-scroll-trigger" id="infinite-scroll-trigger"></div>
        <div data-test="mb-spinner-loader" id="loading-more-content" class="hidden my-8">
          <img class="mx-auto" width="100" height="100" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/spinner.svg" />
        </div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb">Companies</a>
                              <span class="mx-1 fa fa-angle-right"></span>
                          </span>
                      <span>
              <a class="hover:underline" href="/mb_33_C4_000/restaurants_and_bars">Restaurants and Bars</a>
                          </span>
                  </div>
        <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse Subcategories</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C432D_000/bars_taverns">Bars / Taverns</a> (62,324)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C4091_000/confectionery">Confectionery</a> (3,976)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C432C_000/restaurants">Restaurants</a> (531,303)
          </div>
              </div>
    </div>
  </div>
</div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb_33_C4_000/restaurants_and_bars">United States</a>
                          </span>
                  </div>
                  <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse undefined</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_01/restaurants_and_bars/alabama">Alabama</a> (8,092)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_02/restaurants_and_bars/alaska">Alaska</a> (1,526)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_03/restaurants_and_bars/arizona">Arizona</a> (10,729)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_04/restaurants_and_bars/arkansas">Arkansas</a> (4,032)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_05/restaurants_and_bars/california">California</a> (76,869)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_06/restaurants_and_bars/colorado">Colorado</a> (12,444)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_07/restaurants_and_bars/connecticut">Connecticut</a> (7,868)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_08/restaurants_and_bars/delaware">Delaware</a> (1,993)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_09/restaurants_and_bars/district_of_columbia">District of Columbia</a> (1,717)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_10/restaurants_and_bars/florida">Florida</a> (39,458)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_11/restaurants_and_bars/georgia">Georgia</a> (15,885)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_12/restaurants_and_bars/hawaii">Hawaii</a> (2,555)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_13/restaurants_and_bars/idaho">Idaho</a> (2,690)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_14/restaurants_and_bars/illinois">Illinois</a> (25,322)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_15/restaurants_and_bars/indiana">Indiana</a> (9,388)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_16/restaurants_and_bars/iowa">Iowa</a> (5,374)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_17/restaurants_and_bars/kansas">Kansas</a> (5,093)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_18/restaurants_and_bars/kentucky">Kentucky</a> (6,528)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_19/restaurants_and_bars/louisiana">Louisiana</a> (7,535)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_20/restaurants_and_bars/maine">Maine</a> (2,416)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_21/restaurants_and_bars/maryland">Maryland</a> (11,988)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_22/restaurants_and_bars/massachusetts">Massachusetts</a> (13,317)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_23/restaurants_and_bars/michigan">Michigan</a> (14,704)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_24/restaurants_and_bars/minnesota">Minnesota</a> (8,536)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_25/restaurants_and_bars/mississippi">Mississippi</a> (3,874)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_26/restaurants_and_bars/missouri">Missouri</a> (11,039)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_27/restaurants_and_bars/montana">Montana</a> (2,447)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_28/restaurants_and_bars/nebraska">Nebraska</a> (3,917)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_29/restaurants_and_bars/nevada">Nevada</a> (4,978)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_30/restaurants_and_bars/new_hampshire">New Hampshire</a> (2,375)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_31/restaurants_and_bars/new_jersey">New Jersey</a> (19,840)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_32/restaurants_and_bars/new_mexico">New Mexico</a> (3,115)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_33/restaurants_and_bars/new_york">New York</a> (39,986)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_34/restaurants_and_bars/north_carolina">North Carolina</a> (17,288)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_35/restaurants_and_bars/north_dakota">North Dakota</a> (1,621)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_36/restaurants_and_bars/ohio">Ohio</a> (20,123)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_37/restaurants_and_bars/oklahoma">Oklahoma</a> (5,551)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_38/restaurants_and_bars/oregon">Oregon</a> (6,741)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_39/restaurants_and_bars/pennsylvania">Pennsylvania</a> (27,523)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_53/restaurants_and_bars/puerto_rico">Puerto Rico</a> (332)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_40/restaurants_and_bars/rhode_island">Rhode Island</a> (2,641)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_41/restaurants_and_bars/south_carolina">South Carolina</a> (12,132)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_42/restaurants_and_bars/south_dakota">South Dakota</a> (1,632)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_43/restaurants_and_bars/tennessee">Tennessee</a> (11,419)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_44/restaurants_and_bars/texas">Texas</a> (56,253)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_45/restaurants_and_bars/utah">Utah</a> (3,877)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_46/restaurants_and_bars/vermont">Vermont</a> (1,290)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_47/restaurants_and_bars/virginia">Virginia</a> (14,596)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_48/restaurants_and_bars/washington">Washington</a> (10,911)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_49/restaurants_and_bars/west_virginia">West Virginia</a> (3,862)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_50/restaurants_and_bars/wisconsin">Wisconsin</a> (10,112)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_51/restaurants_and_bars/wyoming">Wyoming</a> (1,125)
          </div>
              </div>
    </div>
  </div>
</div>
                      </div>
    </main>
    <style>
  @media (max-width: 360px) {
    .xs-device {
      font-size: 0.875rem;
    }
    .xs-8 {
      height: 2rem;
      width: 2rem;
    }
  }
</style>
<footer>
  <div class="w-full border-t bg-darks-v1 text-primary-light-v1 py-8 lg:py-16 flex flex-col justify-center items-center px-4 sm:px-6">
    <div class="grid grid-cols-8 gap-4 sm:gap-8 max-w-header w-full">
      <div class="hidden lg:flex justify-between mt-6 flex-col col-span-2">
        <div>
          <div>
            <a href="/" data-test="btn-logo-navbar">
              <img loading="lazy" width="162" height="37" class="mb-6 mr-3" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo">
              <span class="sr-only">Manta Home</span>
            </a>
          </div>
          <div class="flex">
            <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
                <span class="sr-only">Manta on Twitter</span>
              </span>
            </a>
            <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
                <span class="sr-only">Manta on Facebook</span>
              </span>
            </a>
            <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
                <span class="sr-only">Manta on LinkedIn</span>
              </span>
            </a>
          </div>
        </div>
        <div class="text-primary-light-v1 xs-device">
          © 2025 Manta Media Inc.<br>All rights reserved.
                  </div>
      </div>
      <div class="grid grid-cols-2 sm:grid-cols-3 gap-8 col-span-8 sm:col-span-5 lg:col-span-4">
        <div class="flex flex-col">
          <span class="font-serif">Manta</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-contact-footer-desktop" href="/contact" class="hover:font-bold py-1">Contact Us</a>
            <a data-test="btn-about-footer-desktop" href="/about-us" class="hover:font-bold py-1">About Us</a>
            <a data-test="btn-reviews-footer-desktop" href="/manta-reviews" class="hover:font-bold py-1">Reviews</a>
            <a data-test="btn-careers-footer-desktop" href="/careers" class="hover:font-bold py-1">Careers</a>
            <a data-test="btn-termsConditions-footer-desktop" href="/terms-and-conditions" class="hover:font-bold py-1">Terms & Conditions</a>
            <a data-test="btn-privacyPolicy-footer-desktop" href="/privacy-policy" class="hover:font-bold py-1">Privacy Policy</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Services</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-digital-mkt-footer-desktop" href="/digital-marketing-services" class="hover:font-bold py-1">Digital Marketing Services</a>
            <a data-test="btn-seo-services-footer-desktop" href="/services/organic-seo-company" class="hover:font-bold py-1">SEO Services</a>
            <a data-test="btn-local-seo-footer-desktop" href="/services/affordable-local-seo" class="hover:font-bold py-1">Local SEO</a>
            <a data-test="btn-national-seo-footer-desktop" href="/services/national-seo-company" class="hover:font-bold py-1">National SEO</a>
            <a data-test="btn-free-seo-footer-desktop" href="/free-seo-website-test" class="hover:font-bold py-1">Free SEO Website Test</a>
            <a data-test="btn-listings-footer-desktop" href="/business-listings/listings-management" class="hover:font-bold py-1">Listings Management</a>
            <a data-test="btn-display-ads-footer-desktop" href="/services/display-advertising" class="hover:font-bold py-1">Display Ads</a>
            <a data-test="btn-ppc-footer-desktop" href="/services/ppc-consulting" class="hover:font-bold py-1">PPC Consulting</a>
            <a data-test="btn-websites-footer-desktop" href="/small-business-marketing/websites" class="hover:font-bold py-1">Website Creation</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Resources</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-seo-faq-footer-desktop" href="/seo-faqs" class="hover:font-bold py-1">SEO FAQ</a>
            <a data-test="btn-ecommerce-footer-desktop" href="/seo-industry-guide/ecommerce-seo-guide" class="hover:font-bold py-1">Ecommerce SEO Guide</a>
            <a data-test="btn-construction-footer-desktop" href="/seo-industry-guide/seo-for-construction-companies" class="hover:font-bold py-1">Construction SEO Guide</a>
            <a data-test="btn-hvac-footer-desktop" href="/seo-industry-guide/seo-for-hvac" class="hover:font-bold py-1">HVAC SEO Guide</a>
            <a data-test="btn-homeyou-footer-desktop" href="/costs" class="hover:font-bold py-1">Home Services Cost</a>
          </div>
        </div>
      </div>
      <div class="border-t border-primary-light-v1 sm:border-none pt-6 sm:pt-0 flex flex-col col-span-8 sm:col-span-3 lg:col-span-2">
        <span class="font-serif">Manta Members</span>
                  <div class="flex items-center mt-6">
            <a data-test="btn-login-footer-desktop" class="hover:font-bold mr-4" href="/member/login">Log In</a>
            <a data-test="btn-login-footer-desktop" class="btn bg-primary-v1 text-white inline-block hover:font-bold" href="/member/register">Sign Up</a>
          </div>
                  <div class="mt-6">
                      <p class="mb-2">Search Manta's Directory to find the Small Business you're looking for</p>
            <a data-test="btn-index-footer-desktop" class="bg-primary-v1 py-2 px-4 rounded text-white inline-block hover:font-bold" href="/">Find a Business Near You</a>
                  </div>
      </div>
    </div>
    <div class="lg:hidden border-b border-t border-primary-light-v1 py-4 my-6 w-full">
      <div class="flex text-primary-light-v1">
        <a data-test="btn-help-footer-desktop-mobile" href="/contact" class="mr-6">Help</a>
        <a data-test="btn-termsConditions-footer-mobile" href="/terms-and-conditions" class="mr-6">Terms</a>
        <a data-test="btn-privacyPolicy-footer-mobile" href="/privacy-policy" class="mr-6">Privacy</a>
      </div>
    </div>
    <div class="flex lg:hidden justify-between items-center w-full">
      <div class="text-primary-light-v1 xs-device">
        © 2025 Manta Media Inc.<br>All rights reserved.
              </div>
      <div class="flex">
        <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
            <span class="sr-only">Manta on Twitter</span>
          </span>
        </a>
        <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
            <span class="sr-only">Manta on Facebook</span>
          </span>
        </a>
        <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
            <span class="sr-only">Manta on LinkedIn</span>
          </span>
        </a>
      </div>
    </div>
  </div>
  
      <script data-cfasync="false" async src="//cdn.intergient.com/1024347/72853/ramp.js"></script>
         </footer>    <script id="lazy-load-images">
  (function(hasObserver) {
    var createObservers = function(list, loadFn) {
      if (hasObserver) {
        var observer = new IntersectionObserver(function(entries, self) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              loadFn(entry.target);
              self.unobserve(entry.target);
            }
          });
        }, { rootMargin: '0px 0px 200px 0px' });

        list.forEach(function(el) {
          observer.observe(el);
        });
      } else {
        list.forEach(loadFn);
      }
    };

    var imgs = document.querySelectorAll('[lazy-load]');
    if (imgs.length) {
      createObservers(imgs, function(el) {
        el.setAttribute('src', el.getAttribute('lazy-load'));
      });
    }

    var bgs = document.querySelectorAll('.lazy');
    if (bgs.length) {
      createObservers(bgs, function(el) {
        el.classList.remove('lazy');
      });
    }
  })(typeof IntersectionObserver !== 'undefined');
</script>
    <script>
  const createCompany = company => {
    return (`
    <div class="flex items-start justify-between">
      <div>
        <div class="border border-gray-200 store-logo rounded">
          ${
            company.logo ? `<div class="bg-no-repeat bg-center bg-contain" style="background-image: url(${company.logo})"></div>`
            : `<div class="hidden md:block">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400" style="font-size: 4rem"></i>
            </div>
          </div>
          <div class="md:hidden">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400 text-6xl"></i>
            </div>
          </div>`
          }
        </div>
      </div>
      <div class="flex flex-grow justify-center">
        <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
          <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
            <a data-test="mb-result-card-title-${company.emid}" href="${company.url}" class="cursor-pointer font-serif text-gray-900 mr-2 break-words">${company.name}</a>
            ${company.claimStatus === 'PBL' 
            ? `<div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>`
            : company.claimStatus === 'CLAIMED'
            ? `<div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>`
            : ''
            }
          </div>
          ${company.recommendations.averageRating.ratingCount > 0
          ? `<div class="flex items-center">
              <div class="text-primary-v1 font-bold mr-4">${company.recommendations.averageRating.ratingValue}</div>
              {stars({ stars: ${company.recommendations.averageRating.ratingValue} })/}
              <div class="ml-4">(${company.recommendations.averageRating.ratingCount})</div>
            </div>` 
          : ''}
          <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
              ${company.location.address !== undefined ? `<div class="hidden md:block">${company.location.address}</div>` : ''}
              ${company.location.country && company.location.country !== 'United States' 
              ? `<div>${company.location.city}, ${company.location.country}</div>`
              : `<div>${company.location.city}, ${company.location.stateAbbrv}</div>`}
            </div>
          </div>
          ${company.contactInfo.phone ? `<div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>${company.contactInfo.phone}</div>
          </div>` : ''}
          ${company.contactInfo.website ? `<div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="${company.contactInfo.website}" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>` : ''}
          ${company.contactInfo.phone || company.contactInfo.website || company.location.address 
            ? `
            <div class="flex md:hidden mt-3">
              ${company.contactInfo.phone ? `<a href="tel:${company.contactInfo.phone}" rel="nofollow noopener"class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
              ${company.location.address ? `
                <a href="${`https://maps.google.com/maps?q=${encodeURIComponent(company.name)},` + '+' + `${encodeURIComponent(company.location.address)}` + '+' + `${encodeURIComponent(company.location.city)}`}" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                ` : ''}
                ${company.contactInfo.website ? `
                  <a href="${company.contactInfo.website}" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
            </div>` 
            : ''}
        </div>
        <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
          <div class="flex rounded w-full mb-4">
            <div class="flex justify-between w-full">
              <div class="flex justify-between items-center">
                ${company.claimStatus === 'PBL' 
                ? `
                <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
                  <i class="text-white fa fa-badge-check"></i>
                </div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                  MANTA VERIFIED
                </div>` 
                : company.claimStatus === 'CLAIMED'
                ? `<div class="mt-1 rounded-l bg-success px-2">
                    <i class="text-xs text-white fa fa-check"></i>
                  </div>
                  <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                    CLAIMED
                  </div>`
                : ''
                }
                ${company?.serviceAreasString && `<p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving ${company.serviceAreasString.split(',')[0]} and the Surrounding Area</p>`}
              </div>
            </div>
          </div>
          ${company.products.productTerms.list.length > 0 
            ? company.products.productTerms.list.forEach(term => {
              return `
                <div class="flex items-baseline mb-1">
                  <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
                  <span class="text-gray-800">${term.name}</span>
                </div>
              `
            })
            : `<div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under ${company.industry.sicm.description}</span>
          </div>`}
        </div>
      </div>
    </div>
    <div>
    ${company.detailedDescription 
      ? `<div class="hidden lg:block">
    <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">${company.detailedDescription}</div>
    </div>` 
      : ''}
    </div>
    `);
  }
</script>    <script>

  const trigger = document.querySelector('#infinite-scroll-trigger');
  const loader = document.querySelector('#loading-more-content');
  const results = document.querySelector('#more-results');

  const config = {
    root: null,
    threshold: 0.1,
    rootMargin: '0px'
  };

  let search = 2;

  const sicAndGeoLastIndex = window.location.pathname.lastIndexOf('/');
  const sicAndGeoFirstIndex = window.location.pathname.indexOf('_');
  const sicAndGeo = window.location.pathname.slice(sicAndGeoFirstIndex + 1, sicAndGeoLastIndex);

  const observer = new IntersectionObserver(async (entries, self) => {
    try {
      const [entry] = entries;
      if (!entry.isIntersecting) return;
      loader.classList.toggle('hidden');
      const response = await axios.get(`/more-results/${sicAndGeo}?pg=${search}`, {
        headers: { 'x-request-id': 'c28c60d537c09a6219045e7f2c7bc8da' }
      });
      if (response.data.companies.list.length === 0) {
        self.unobserve(entry.target);
      }
      loader.classList.toggle('hidden');
      let companies = [];
      response.data.companies.list.forEach((company) => {
        const resultContainer = document.createElement('div');
        resultContainer.setAttribute('data-test', `mb-result-card-${company.emid}`);
        resultContainer.classList.add('md:rounded', 'bg-white', 'border-b', 'border-primary-light-v1', 'px-3', 'py-4', 'md:p-8', 'md:mt-4', 'mx-4', 'xl:mx-0');
        resultContainer.innerHTML = createCompany(company);
        companies.push(resultContainer);
      })
      results.append(...companies);
      search++;
    } catch(error) {
      loader.classList.toggle('hidden');
      self.unobserve(entry.target);
    }
  });

  observer.observe(trigger, config);

</script>  </body>
</html>
