<!DOCTYPE html>
<html lang="en">
  <head><script>(function(w,i,g){w[g]=w[g]||[];if(typeof w[g].push=='function')w[g].push(i)})
(window,'GTM-R7B4','google_tags_first_party');</script><script>(function(w,d,s,l){w[l]=w[l]||[];(function(){w[l].push(arguments);})('set', 'developer_id.dY2E1Nz', true);
		var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s);j.async=true;j.src='/pxhh/';
		f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer');</script>
      <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Restaurants and Bars | United States - Manta.com</title>
<link rel="icon" href="https://cc3.manta-r3.com/assets-gz/2e2a97f56/img/favicon.png" type="image/png">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com">
<link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/app.css">
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/directory/css/fa.css"></noscript>
<style>
  iframe[name=d_ifrm] {
  display: none;
  }
</style>
        <script rel="gtm-render">
      var screenWidth = screen.width;
      var sSz = 'lg';
      var wsSz = 'lg';
      var wSz = 'lg';
      if (screenWidth < 768) {
        sSz = wsSz = wSz = 'xs';
      } else if (screenWidth < 992) {
        sSz = wsSz = wSz = 'sm';
      } else if (screenWidth < 1200) {
        sSz = wsSz = wSz = 'md';
      }

      var gtmData = {
        ua_property: "UA-10299948-11",
        googleExperimentId: "",
        googleExperimentVariation: "-1",
        pageTitle: "Restaurants and Bars | United States - Manta.com",
        page_type: "company-content", // TODO: make this work when we do search/browse
        is_pagespeed: Boolean('false'),

        
        visitor_id: "037d0847-8bde-4cc2-8648-6b099957b95f",
        customer_segment: "sbo",
        page_depth: "2",
        scr_win_width: sSz + '-' + wSz,

        
        treatment: "no-test",

                  altTreatment1: "LM $49 Price Test CONTROL",
                  altTreatment2: "",
                  altTreatment3: "",
        
        ip: "**************",

                  // Older cookies might not have stateAbbrv and countryAbbrv,
          // so fall back to state and country
          user_state: "j:null",
          user_country: "US",
        
        url_hash: window.location.hash,
        timestamp: new Date().toString(),
        sbi: "false",
        statusCode: "200",  // TODO: actual status
      };

              var dataLayer = [gtmData];
      
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-R7B4');
    </script>
    <script>
      var gaTrack = function (category, action, label, value, interactive) {
        if (arguments.length === 1) {
          // Non-event tracking stuff
          dataLayer.push(category);
          return;
        } else if (arguments.length === 3) {
          value = 1;
          interactive = true;
        }
        if (arguments.length === 4) {
          if (typeof value === 'boolean') {
            interactive = value;
            value = 1;
          } else {
            interactive = true;
          }
        }

        // if label is an object, serialize it into name=value pairs
        if (typeof label === 'object' && !Array.isArray(label)) {
          var pairs = [];
          for (var key in label) {
            pairs.push(key + '=' + label[key]);
          }
          label = pairs.join(',');
        }

        dataLayer.push({
          eventData: {
            category: category,
            action: action,
            label: label,
            value: value
          },
          event: (interactive ? 'interactive' : 'non-interactive') + ' event'
        });
      };
    </script>
  <script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/cash.min.js"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/js.cookie.min.js" onload="Cookies.defaults = { path: '/', domain: '.manta.com' }"></script>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/axios.min.js" onload="window.axios = window.redaxios"></script>
<script>var loadScript=(function(d,s,c,e,f){return function(u){if(c[u]){return c[u];}e=d.createElement(s);e.async=!0;e.src=u;f=d.getElementsByTagName(s)[0];f.parentNode.insertBefore(e,f);return c[u]=e;};})(document,'script',{});</script>
<link rel="preload" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="//cc3.manta-r3.com/dist/fc1ac3e4/css/simpleLightbox.min.css"></noscript>
<script src="//cc3.manta-r3.com/dist/fc1ac3e4/js/simpleLightbox.min.js"></script>
<script>
  window.__PRELOADED_STATE__ = {
    toggledFeatures: {"experimentId":"","experimentVariation":"-1","forceGzip":false,"adsenseChannel":"1","scrollTracking":false,"exitTracking":true,"fakeMixpanel":true,"fakeGa":false,"useSubscriptionTestMode":false,"yextAllowDuplicatesMode":false,"allowSurveys":true,"isOffHoursOverride":false,"isBusinessHoursOverride":false,"skipDittoVendorRequests":false,"marketplaceJobs":false,"claimCtaText":"Own This Business?","claimCtaColor":"btn-primary","useLongBundleDesc":false,"isSiteMaintenanceMode":false,"homeyouLeads":false,"updoptProdOverride":false,"enableUpdoptDossier":true,"adsenseSlotMobile":"1160948055","adsenseSlotDesktop":"5730748454","useFapiForSubscriptions":false,"popOverlayOnCheckout":true,"useBuilderToAdd":false,"checkoutTemplate":"checkout","buyerlink_treatmentA":true,"adsenseHeroPlacement":true,"refreshAds":false,"show300x250":true,"showTaboola":true,"showDesktopAdhesionBanner":true,"homeyouWidgetPlacement":"about","newStack":true,"similarBusinessesUp":true,"reducedAdDensity":true,"navAffiliateBusinessCreditAd":true,"trackNumbers":true,"homeyouNumberTest":true,"homeyouTrackingNumber":"+***********","unclaimedStatic":true,"includeGMBScan":true,"postponeMerchantScans":true,"embedYextDashboard":true,"showPaywallPage":false,"checkoutPaywallTreatment":"paywall-control","enableWebsiteAddOn":true,"useBriteVerify":true,"useGooglePlacesInClaimBuilder":true,"useGoogleMaps":true,"useGoogleAutocomplete":true,"enableFacebookSignIn":true,"enableGoogleSignIn":true,"logCookieErrors":true,"rightRailDoubleAds":true,"triggerDemoScheduler":true,"showCovid":true,"covidShow":true,"es_search":true,"es_related":true,"useNewMemberDashboard":true,"showDetailedDescription":true,"useInternationalSearch":true,"useNewEditPage":true,"useElasticMb":true,"showMarketStats":false,"useNewAnalyticsService":true,"useElasticWorld":true,"requireTermsOfService":true,"useTaboolaAds":false,"usePlaywire":true,"adSenseSearchPages":true,"adSenseProfilePages":true,"oldSurveyModal":true,"useRepSalesDashboard":true,"blockSICM":true,"useNewCheckout":true,"showBanner":false,"copyTextBanner":"Promo Banner","redeemByTextBanner":"Redeem by 19th march","expiryDateBanner":"03/18/2023","catchPhraseBanner":"Special Offer!","validForBanner":"hasDitto, hasLmReviews, hasWebsite, hasPpcAds, hasDisplayAds, hasFeaturelessPlan, freeUser","toggleUrlCheckout":true,"changeVersion3":true,"showReviewTile":false,"showAdapexAds":true},
    referral_source: '',
    abTreatment: 'no-test',
    gamNetworkCode: '6009',
    visitor: {"ip":"**************","id":"037d0847-8bde-4cc2-8648-6b099957b95f","pageDepth":2,"customerSegment":{"threshold":7,"id":"s","label":"sbo"},"smallBusinessInterest":false,"xri":"3dc20d8f63f91a6ff36a9722273055c4"},
    pageComponents: {},
    clientIp: '**************',
    isCalifornia: false,
    isDev: false,
    env: 'production',
    member: undefined  };
</script>
<script>
  var cache = {};
  window.logError = function(e, info) {
    var lines = (e.stack || '').split('\n');
    var callsite = lines.length > 1 ? lines[1].match(/(app\.js:\d+:\d+)/) : null;
    var key = e.message + (callsite && callsite[1] ? ' at ' + callsite[1] : '');

    if (!cache[key]) {
      try {
        window.axios && axios.post('/fapi/errors', {
          message: e.message || 'Unknown error',
          stack: e.stack || 'No stack trace available',
          info: info,
                    userAgent: (window.navigator && window.navigator.userAgent) || 'unknown'
        }).catch(function() {});
      } catch (e) {
        // Obviously, this isn't async/await, so it won't catch the
        // axios call, but I just want to _assure_ we don't throw
        // from the onerror handler.
      }
    }
  };
  window.onerror = function(message, source, lineno, colno, error) {
    // Don't log errors that come from ads and crap like that
    if (source.indexOf('manta.com') > -1) {
      logError(error, { source: source, lineno: lineno, colno: colno });
    }
  };
</script>
<!-- The script below is going to be commented until we figure out what could be a better implementation talking about performance -->
<!-- <script defer src="//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js" importance="low"></script> --><script defer src="https://btloader.com/tag?o=5150306120761344&upapi=true"></script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>

      <script data-cfasync="false">
        window.ramp = window.ramp || {};
        window.ramp.que = window.ramp.que || [];
      </script>
    
    <script type="text/javascript">
      window.ramp = window.ramp || {};
      window.ramp.que = window.ramp.que || [];
    </script>
          <script>
  if($) {
    $.fn.swapWithNext = function() {
      this.hide();
      this.next().removeClass('hidden');
    }
  }
</script>
  <script>
    var maTrack = (function() { 
      return function(xri, type, listings, event, stopPropagation) {
        if (event && stopPropagation) {
          event.stopPropagation();
        }
        var params = {
          listings: typeof listings === 'string' ? JSON.parse(listings) : listings,
          t: type + (screen.width < 992 ? '_mobile' : ''),
          ts: Date.now(),
          total: listings.length
        };

        var fp = JSON.stringify(params);

        typeof axios === 'function' && axios({
          url: '/track',
          method: 'GET',
          params: { fp: fp },
          withCredentials: true,
          headers: { 'x-request-id': xri }
        }).catch(function(e) {
          logError(e, { trackData: params });
        });
      };
    })();
  </script>
  <script>
    var mantaTrack = (function() {
      var mat = {};

      mat.xri = '3dc20d8f63f91a6ff36a9722273055c4';
      mat.device = screen.width < 992 ? 'mobile' : 'desktop';
                        
      mat.trackView = (function() {
        return function(context) {
          var events = [{
            emid: mat.emid,
            type: 'view',
            data: {
              context: context,
              device: mat.device,
              sicm: mat.sicm,
              city_code: mat.city_code,
              claim_source: mat.claim_source
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackClick = (function() {
        return function(category, context, section) {
          var events = [{
            emid: mat.emid,
            type: 'click',
            data: {
              context: context,
              device: mat.device,
              city_code: mat.city_code,
              sicm: mat.sicm,
              claim_source: mat.claim_source,
              category: category,
              section: section
            }
          }];
          return mantaTrack(events);
        };
      })();

      mat.trackSearch = (function() {
        return function(context, city, sicm, emid) {
          let events = [];
          var values = [{
            emid: emid,
            type: "view",
            data: {
              context: context,
              device: mat.device,
              city: city,
              sicm: sicm
            }
          }];
          values.forEach(val => {
            events.push(val)
          });
          return mantaTrack(events);
        }
      })();
      
      mat.trackAndGo = (function() {
        return function(location, category, context, section) {
          mat.trackClick(category, context, section);
          window.location.href = location;
        }
      })();

      var mantaTrack = (function() {
          return function(events) {
                  return true;
                };
      })();

      return mat;

    })();
  </script>
  <script>
  window.addEventListener('DOMContentLoaded', function () {
    var gaTrackSS = {
      events: [],
      attempts: 0,

      actionToEventMap : {
        "related company-view": "related_company_view",
        "related company-click": "related_company_click",
        "Homeyou Calculate Your Costs": "home_you_click", 
      },

      getEventName: function(action) {
        return this.actionToEventMap[action];
      },

      getClientId: function() {
        let cookie = Cookies.get('_ga');
        let parts = cookie.split('.');
        let newResult = parts[2] + '.' + parts[3];
        return newResult;
      },

      addEvent: function(action, label, value, customDimensions, nonInteractive) {
        const event = {
          category: 'Server Side Tracking',
          action: action,
          value: value,
          customDimensions: customDimensions,
          nonInteractive: nonInteractive,
          client_id: this.getClientId(),
          eventName: this.getEventName(action),
        };

        let ga4Label;

        if (label) {
            const ga4 = label.replace("emid", '"emid"').replace('claimSource', '"claimSource"');
            ga4Label = JSON.parse(ga4);
            event.label = label;
        }

        if (ga4Label) {
            event.params = {
                emid: ga4Label.emid,
                claimSource: ga4Label.claimSource
            };
        }

        this.events.push(event);
      },

      sendEvent: function(action, label, value, customDimensions, nonInteractive) {
        this.addEvent(action, label, value, customDimensions, nonInteractive);
        this.sendEvents();
      },

      sendEvents: function() {
        if (this.gaLoaded()) {
          clearInterval(this.gaCheck);
          this._sendEvents();
        } else {
          if (!this.gaCheck) {
            this.gaCheck = setInterval(() => {
              if (this.attempts >= 5) {
                clearInterval(this.gaCheck);
                return;
              }
              this.sendEvents();
            }, 500);
          } else if (this.attempts >= 10) {
            clearInterval(this.gaCheck);
          }
        }
      },

      gaLoaded: function() {
        this.attempts++;
        return Cookies.get('_ga');
      },

      _sendEvents: function() {
        if (this.events.length) {
          typeof axios === 'function' && axios({
            url: '/gatrack',
            method: 'POST',
            data: this.events,
            withCredentials: true
          }).catch(function(e) {
            logError(e, { events: this.events });
          });
          this.events = [];
        }
      }
    };
  });
  </script>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        .store-logo {
          position: relative;
          overflow: hidden;
          width: 100px;
        }
        .store-logo:before {
          content: "";
          display: block;
          padding-top: 100%;
        }
        .store-logo > div {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          text-align: center;
        }
      </style>
  </head>
  <body class="bg-primary-light-v1 text-gray-800">
    <div class="relative">
  <a href="#start-of-content" class="text-xs text-darks-v1 focus:text-white absolute right-100">Skip to Content</a>
</div>
<style>
      .desktop-search-wrapper {
      width: 380px;
    }
    @media(min-width: 1110px) {
      .desktop-search-wrapper {
        width: 480px;
      }
    }
  
</style>

<header>
  <div class="mobile-menu hidden fixed w-screen h-screen bg-white p-4 z-50 overflow-auto">
    <div class="float-right" onclick="$('.mobile-menu').addClass('hidden'); $('body').removeClass('overflow-hidden')"><i class="text-text-darks-v1 text-3xl fa fa-times"></i></div>
    <ul class="text-gray-600 my-16 text-lg">
  <li class="mb-2 hover:font-bold">
    <a href="/services">For Businesses</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/free-business-listing">Free Company Listing</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/business-listings/listings-management">Premium Business Listings</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/small-business-marketing/websites">Websites</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/organic-seo-company">SEO</a>
  </li>
  <li class="mb-2 hover:font-bold">
    <a href="/services/affordable-local-seo">Local SEO</a>
  </li>
  <li>
    <a href="/services/national-seo-company">National SEO</a>
  </li>
</ul>
      <div class="flex flex-col lg:flex-row">
    <a class="btn bg-primary-light-v1 text-gray-800 font-bold flex-1 mb-4 py-3" href="/member/login">Log In</a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="mb-4 btn bg-primary-v1 text-white inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
      </div>
  </div>

  <div class="mobile-search hidden fixed w-screen h-screen bg-white z-50 overflow-y-scroll">
  <div class="justify-center py-3 mx-auto max-w-header flex items-center bg-darks-v1 px-4 relative">
    <div onclick="$('.mobile-search').addClass('hidden');$('.pre-mobile-search').removeClass('hidden')" class="lg:hidden text-primary-light-v1 cursor-pointer absolute left-0 top-0 mt-6 ml-5">Cancel</div>
    <div class="flex sm:pr-3">
      <a href="/" data-test="btn-logo-navbar">
        <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo" width="109" height="25">
      </a>
    </div>
  </div>
  <div class="bg-darks-v1 text-gray-dark search-component-mobile"></div>
</div>

  <div class="px-6 bg-darks-v1 h-auto text-white">
    <div class="justify-between py-3 mx-auto max-w-header flex items-center">

      <div onclick="$('.pre-mobile-search').addClass('hidden');loadSearchBar('.mobile-search')" class="flex md:hidden"><i class="text-2xl fa fa-search"></i></div>
      <div>
  <a href="/" data-test="btn-logo-navbar">
    <img class="h-6 my-3 sm:mr-3 not-sr-only" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo"  width="109" height="25" >
    <span class="sr-only">Manta Home</span>
  </a>
</div>
      <div class="hidden md:inline-block desktop-search-wrapper">
        <div class="rounded text-gray-dark hidden md:inline-block search-component w-full">
          <form name="searchForm">
  <div class="flex flex-col sm:flex-row px-3 sm:px-0" style="border-radius: 4px 4px 4px 0px;">
    <div class="flex sm:w-1/2 relative px-3 py-2 bg-white my-1 sm:my-0 rounded sm:rounded-l-lg sm:rounded-r-none">
      <div class="flex justify-center items-center mr-4 w-5">
        <span class="text-primary-v1 fa fa-search text-xl"></span>
      </div>
      <div class="flex w-full">
        <label for="header-search" class="sr-only">Search</label>
        <input
          id="header-search"
          name="search"
          placeholder="I'm looking for..."
          class="w-full outline-none"
          onfocus="loadSearchBar('.search-services-menu')"
          autocomplete="off"
          value=""
        />
      </div>
      <div class="absolute search-services-menu hidden" style="z-index: 10000">
        <ul class="p-0 m-0 text-gray-600">
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-utensils mr-3"></span
            ></span>
            <span class="text-small">Restaurants</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-stethoscope mr-3"></span
            ></span>
            <span class="text-small">Doctors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-gavel mr-3"></span
            ></span>
            <span class="text-small">Lawyers</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-hammer mr-3"></span
            ></span>
            <span class="text-small">Contractors</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"><span class="fa fa-car mr-3"></span></span>
            <span class="text-small">Automotive</span>
          </li>
          <li class="flex flex-row px-4 py-3 hover:bg-gray-200 text-gray-600 border-b border-gray-300 last:border-none sm:border-none truncate">
            <span class="self-start"
              ><span class="fa fa-tooth mr-3"></span
            ></span>
            <span class="text-small">Dentists</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex py-2 sm:py-0 sm:w-1/2">
      <div class="flex w-full relative px-3 sm:pl-0 sm:pr-3 py-2 bg-white rounded-l-lg sm:rounded-none">
        <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
        <div class="self-center w-full text-gray-800">
          <label for="header-location" class="sr-only">Location</label>
          <input
            id="header-location"
            name="location"
            placeholder="City, State, Country, Zip"
            class="w-full outline-none"
            onfocus="loadSearchBar('.search-location-menu')"
            autocomplete="off"
            value=""
          />
        </div>
        <div class="search-location-menu hidden" style="z-index: 10000">
          <ul class="m-0 p-0 locations">
            <li
              class="px-4 py-3 text-primary-v1 hover:bg-gray-200 cursor-pointer"
            >
              <svg
  xmlns="http://www.w3.org/2000/svg"
  width="20"
  height="24"
  viewBox="0 0 36 47"
  fill="none"
  class="sm:pl-2 sm:border-l sm:mr-2 w-5 sm:w-6 mr-4"
  aria-labelledby="mapPin"
>
  <title id="mapPin"></title>
  <path
    fill-rule="evenodd"
    clip-rule="evenodd"
    d="M6.24146 18.1348C6.24146 11.724 11.4778 6.49365 17.9783 6.49365C24.4788 6.49365 29.7151 11.724 29.7151 18.1348C29.7151 20.3685 28.9815 22.9195 27.7022 25.615C26.4344 28.2866 24.7147 30.9254 22.946 33.2855C21.1834 35.6375 19.4138 37.6578 18.0826 39.0913L17.9783 39.2034L17.874 39.0913C16.5428 37.6578 14.7732 35.6375 13.0106 33.2855C11.2419 30.9254 9.52223 28.2866 8.25436 25.615C6.97513 22.9195 6.24146 20.3685 6.24146 18.1348ZM15.9132 45.6698C15.9138 45.6703 15.9143 45.6708 17.9783 43.4937L15.9143 45.6708C17.0716 46.7679 18.885 46.7679 20.0423 45.6708L17.9783 43.4937C20.0423 45.6708 20.0428 45.6703 20.0434 45.6698L20.0447 45.6685L20.0485 45.6649L20.06 45.6539L20.0987 45.6168C20.1315 45.5854 20.1778 45.5407 20.2368 45.4832C20.3548 45.3683 20.5237 45.2022 20.7363 44.989C21.1612 44.5627 21.7616 43.9469 22.4792 43.1741C23.9112 41.6322 25.8258 39.4478 27.7474 36.8836C29.663 34.3275 31.6275 31.3383 33.1228 28.1875C34.6067 25.0606 35.7151 21.5949 35.7151 18.1348C35.7151 8.37347 27.7556 0.493652 17.9783 0.493652C8.20097 0.493652 0.241455 8.37347 0.241455 18.1348C0.241455 21.5949 1.34988 25.0606 2.83381 28.1875C4.3291 31.3383 6.2936 34.3275 8.20918 36.8836C10.1308 39.4478 12.0454 41.6322 13.4774 43.1741C14.195 43.9469 14.7954 44.5627 15.2203 44.989C15.4329 45.2022 15.6018 45.3683 15.7198 45.4832C15.7788 45.5407 15.8251 45.5854 15.8578 45.6168L15.8966 45.6539L15.9081 45.6649L15.9119 45.6685L15.9132 45.6698Z"
    fill="#FF6B61"
  />
</svg>
              <span class="small loc-name">Current Location</span>
            </li>
          </ul>
        </div>
      </div>
      <button
        type="submit"
        class="sm:flex items-center justify-center bg-primary-v1 text-white px-3 py-2 overflow-hidden rounded-r-lg">
        <span class="sr-only">Search</span>
        <span class="not-sr-only text-white fa fa-search text-xl"></span>
      </button>
    </div>
  </div>
</form>
<script>
  (function () {
    var loc = localStorage.getItem("locHistory");
    if (loc) {
      var li = $(
        '<li class="px-4 py-3 text-gray-600 hover:bg-gray-200 cursor-pointer border-t border-gray-300 sm:border-none"><span class="fa fa-clock mr-4"></span><span class="small loc-name">Current Location</span></li>'
      );
      JSON.parse(loc).forEach(function (l) {
        if (l.stateAbbrv) {
          li.find(".loc-name").text(l.formatted);
          $(".locations").append(li.clone());
        }
      });
    }
  })();
  var loadSearchBar = (function () {
    return function (c) {
      $(c).removeClass("hidden");
      loadScript("//cc3.manta-r3.com/dist/fc1ac3e4/react/search.bundle.js");
    };
  })();
  $("form[name=searchForm]").on("submit", function (e) {
    e.preventDefault();
    var search = $("input[name=search]").val();
    var locationInput = $("input[name=location]").val().trim();
    if (!locationInput) {
      const lastGeo = Cookies.get('lastGeo');
      if (lastGeo) {
        try {
          const geo = JSON.parse(lastGeo);
          if (geo && geo.city && geo.stateAbbrv) locationInput = `${geo.city}, ${geo.stateAbbrv}`;
        } catch (e) {
          return;
        }
      }
    };

    if (!locationInput) return;

    var device = "desktop";
    if (window.screen.availWidth <= 500) {
      device = "mobile";
    } else if (window.screen.availWidth <= 1024) {
      device = "tablet";
    }
    var parts = locationInput.split(/[, ]+/);
    var state = parts.pop();
    var city = parts.join(" ");
    window.location =
      "/search?search_source=nav&search=" +
      encodeURIComponent(search) +
      "&city=" +
      encodeURIComponent(city) +
      "&state=" +
      encodeURIComponent(state) +
      "&device=" +
      device +
      "&screenResolution=" +
      window.screen.availWidth +
      "x" +
      window.screen.availHeight;
  });
</script>
        </div>
      </div>

      <div class="hidden lg:block text-sm">
        <div data-test="btn-products-navbar-desktop" class="dropdown inline-block py-4 text-primary-light-v1">
          <a data-test="btn-findBusiness-navbar-desktop" class="hover:underline font-bold px-3" href="/services">For Businesses <i class="fa fa-angle-down"></i></a>
          <div class="dropdown-tri"></div>
          <ul class="dropdown-menu py-2 text-nordic-v1">
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/free-business-listing">Free Company Listing</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/business-listings/listings-management">Premium Business Listings</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/small-business-marketing/websites">Websites</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/organic-seo-company">SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/affordable-local-seo">Local SEO</a></li>
            <li><a class="py-2 px-3 hover:font-bold w-full block" href="/services/national-seo-company">National SEO</a></li>
          </ul>
        </div>
            <a data-test="btn-login-navbar-desktop" class="hover:underline text-primary-light-v1 font-bold"
    href="/member/login"><span class="lg:px-1 px-3 xl:px-3">Log In</span></a>
    <a data-test="btn-claim-business-navbar-desktop" onclick="gaTrack('Nav Bar', 'Link Click', 'Claim My Listing - Main CTA', 1, true)" class="px-5 lg:px-2 xl:px-5 py-2 w-auto rounded cursor-pointer text-center  bg-primary-v1 text-white mx-3 lg:ml-3 lg:mr-2 xl:mx-3 inline-block font-bold" href="/business-listings/add-your-company">Claim My Listing</a>
        </div>

      <div onclick="$('.mobile-menu').removeClass('hidden');" class="flex lg:hidden"><i class="text-2xl fa fa-bars"></i></div>
    </div>
      </div>

  <div class="pl-0 lg:px-6 bg-primary-dark text-white overflow-x-hidden faded faded-x-primary-dark hidden">
  <div class="py-1 mx-auto max-w-header flex items-center overflow-x-auto">
    <div class="inline-block py-2 text-sm whitespace-no-wrap ml-5">
      <a class="cursor-pointer" href="/mb_33_A6_000/professional_services">Business Services<span class="align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_C4_000/restaurants_and_bars">Food & Beverage<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_B3_000/consumer_services">Consumer Products & Services<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_D0_000/healthcare">Health<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer" href="/mb_33_G4_000/information_technology">Tech & Communications<span class="h-2 align-top mx-2 text-primary">|</span></a>
      <a class="cursor-pointer mr-5" href="/mb_33_E6_000/industrial_machinery">Industrial</a>
    </div>
  </div>
</div>
  

</header>
    <main>
      <div class="bg-white w-screen lg:mx-auto pb-8 pt-12 flex flex-row justify-center">
        <div class="flex flex-col w-page mx-4 xl:mx-0">
          <h1 class="text-3xl font-serif text-gray-900">Restaurants and Bars</h1>
          <p>
            <span>Manta has 597,603 businesses under Restaurants and Bars in</span>
                                          <span>the</span>
                            <span>United States</span>
                      </p>
        </div>
      </div>
      <div class="w-screen lg:w-page lg:mx-auto">
                <h1 class="text-2xl mx-4 xl:mx-0 font-serif text-gray-900">All Company Listings</h1>
                  <div data-test="mb-result-card-mm23spx" class="md:rounded bg-white border-b  border-t  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm23spx" href="/c/mm23spx/desperado-sports-tavern" class="cursor-pointer font-serif text-gray-900 mr-2">Desperado Sports Tavern</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3101 S Russell Street</div>
                                            <div>Missoula, MT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:4065499651" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Desperado%20Sports%20Tavern,+3101%20S%20Russell%20Street+Missoula" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Burgers, Wings, Steaks, Seafood</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">ATM</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Patio Seating</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">32 HD TVS</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx4d4pk" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20140812M8vrBDrLOp)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx4d4pk" href="/c/mx4d4pk/finn-s-harborside" class="cursor-pointer font-serif text-gray-900 mr-2">Finn's Harborside</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">38 Water St</div>
                                            <div>East Greenwich, RI</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.finnsharborside.com%2F&s=d2355835f2aa3200b675f9e3718c9bad&cb=1441463" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4018846363" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Finn's%20Harborside,+38%20Water%20St+East%20Greenwich" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.finnsharborside.com%2F&s=d2355835f2aa3200b675f9e3718c9bad&cb=1441463" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Lobster Soup</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Lobster</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Steaks And Seafood Restaurants</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mtrbrt1" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20170801DSVwvf7tm7)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtrbrt1" href="/c/mtrbrt1/moe-s-southwest-grill" class="cursor-pointer font-serif text-gray-900 mr-2">Moe's Southwest Grill</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1 Dog Lane # A 101</div>
                                            <div>Storrs, CT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.moes.com%2Ffind-a-moes%2Fconnecticut%2Fmansfield%2F3555%2F%3Futm_source%3Dgoogle%26utm_medium%3Dorganic%26utm_campaign%3Dlocalmaps&s=56266d99b8c3033f7de3786994dbd263&cb=1441463" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8604771553" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Moe's%20Southwest%20Grill,+1%20Dog%20Lane%20%23%20A%20101+Storrs" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.moes.com%2Ffind-a-moes%2Fconnecticut%2Fmansfield%2F3555%2F%3Futm_source%3Dgoogle%26utm_medium%3Dorganic%26utm_campaign%3Dlocalmaps&s=56266d99b8c3033f7de3786994dbd263&cb=1441463" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Mexican Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mt1k6lw" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mt1k6lw" href="/c/mt1k6lw/daniel-s-bistro" class="cursor-pointer font-serif text-gray-900 mr-2">Daniel's Bistro</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">311 Broadway</div>
                                            <div>Point Pleasant Beach, NJ</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=www.danielsbistronj.com&s=e729379b4441b53024e2dafb94c86177&cb=1441463" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7328995333" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Daniel's%20Bistro,+311%20Broadway+Point%20Pleasant%20Beach" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=www.danielsbistronj.com&s=e729379b4441b53024e2dafb94c86177&cb=1441463" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">BYOB Restaurant</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Innovative cusine using the finest ingredients available. BYOB, reservations suggested and accepted year round.  Open Tuesday thru Sunday at 4:00pm.  Major credit cards accepted.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mtwt5rl" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20170424nK1H9JS3yi)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtwt5rl" href="/c/mtwt5rl/highline-pizzeria" class="cursor-pointer font-serif text-gray-900 mr-2">Highline Pizzeria</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">503 W 28th St</div>
                                            <div>New York, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fhighlinepizzeria.net&s=007d74538a63f547873c517063afc880&cb=1441463" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2125643330" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Highline%20Pizzeria,+503%20W%2028th%20St+New%20York" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fhighlinepizzeria.net&s=007d74538a63f547873c517063afc880&cb=1441463" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Pizza</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mh1g4ts" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20170630DnI1PCsck0)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mh1g4ts" href="/c/mh1g4ts/pink-pepper-asian-fusion" class="cursor-pointer font-serif text-gray-900 mr-2">Pink Pepper Asian Fusion</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1941 W Guadalupe Rd #105</div>
                                            <div>Mesa, AZ</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.pinkpepperasianfusion.com&s=79fe7009a1daefa1f131deddbbd18c9b&cb=1441463" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4808399009" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Pink%20Pepper%20Asian%20Fusion,+1941%20W%20Guadalupe%20Rd%20%23105+Mesa" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.pinkpepperasianfusion.com&s=79fe7009a1daefa1f131deddbbd18c9b&cb=1441463" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Pink Pepper Asian Fusion offers authentic Chinese, Thai, and Vietnamese food in Mesa, AZ.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mhcrv0r" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhcrv0r" href="/c/mhcrv0r/hoya" class="cursor-pointer font-serif text-gray-900 mr-2">HOYA</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">467 Essex Street</div>
                                            <div>Saugus, MA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.sauguschinesefood.com&s=40fd1ac22213b38d71a744039a7a6301&cb=1441463" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7816229403" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=HOYA,+467%20Essex%20Street+Saugus" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.sauguschinesefood.com&s=40fd1ac22213b38d71a744039a7a6301&cb=1441463" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Sushi Bars</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Description:	Specializes in chinese, sushi, & teriyaki.	
Services: 	Chinese Food, Sushi, Chinese Delivery	
Payment Types:	Cash, Visa, MasterCard, Discover, American	
Business Hours:	Monday-Thursday 11:00 AM - 09:30 PM Friday-Saturday 11:00 AM - 10:30 PM Sunday 11:00 AM - 09:30 PM</div>
    </div>
  </div>                  <div data-test="mb-result-card-mh1gpq1" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mh1gpq1" href="/c/mh1gpq1/charley-s-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">Charley's Restaurant</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1705 East Main St</div>
                                            <div>Albemarle, NC</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fcharleysrestaurant.blogspot.com%2F&s=d1eb96e8f9a59f9a213b1fc95c2993bd&cb=1441463" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:7049825214" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Charley's%20Restaurant,+1705%20East%20Main%20St+Albemarle" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fcharleysrestaurant.blogspot.com%2F&s=d1eb96e8f9a59f9a213b1fc95c2993bd&cb=1441463" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mmdg6x7" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmdg6x7" href="/c/mmdg6x7/bane-restaurant-corp" class="cursor-pointer font-serif text-gray-900 mr-2">Bane Restaurant Corp</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">325 Nesconset Highway # 1</div>
                                            <div>Hauppauge, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:6319797676" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Bane%20Restaurant%20Corp,+325%20Nesconset%20Highway%20%23%201+Hauppauge" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Steak Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mmyk09p" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmyk09p" href="/c/mmyk09p/mc-donald-s" class="cursor-pointer font-serif text-gray-900 mr-2">Mc Donald's</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">718 2nd Avenue E</div>
                                            <div>Oneonta, AL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2052748700" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Mc%20Donald's,+718%202nd%20Avenue%20E+Oneonta" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Services</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx4szj9" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx4szj9" href="/c/mx4szj9/fine-wining-fine-dining" class="cursor-pointer font-serif text-gray-900 mr-2">Fine Wining Fine Dining</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">123 main street</div>
                                            <div>Belmont, MA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:6179679993" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Fine%20Wining%20Fine%20Dining,+123%20main%20street+Belmont" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Wine Bars</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm2l38t" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm2l38t" href="/c/mm2l38t/kfc" class="cursor-pointer font-serif text-gray-900 mr-2">Kfc</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1710 Grand Avenue</div>
                                            <div>Beardstown, IL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:2173231700" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Kfc,+1710%20Grand%20Avenue+Beardstown" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Chain Fast Food Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx2g6d7" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20121024O5CUVf4srg)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx2g6d7" href="/c/mx2g6d7/cali-pho-asian-restaurant" class="cursor-pointer font-serif text-gray-900 mr-2">Cali Pho Asian Restaurant</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">322 S. 7th St.</div>
                                            <div>Delavan, WI</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2FCaliPhoDelavan.yolasite.com%2F&s=8f8cd4efca80e70c1cad428149ad4e6a&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2627256890" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Cali%20Pho%20Asian%20Restaurant,+322%20S.%207th%20St.+Delavan" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2FCaliPhoDelavan.yolasite.com%2F&s=8f8cd4efca80e70c1cad428149ad4e6a&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Gluten Free</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Sodium Nitrite Free</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Msg Free</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Asian Foods</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Chinese Foods</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mm3rfsv" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm3rfsv" href="/c/mm3rfsv/bar-b-q-pit" class="cursor-pointer font-serif text-gray-900 mr-2">Bar-B-Q Pit</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">11861 US Highway 69 S</div>
                                            <div>Warren, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:4095472203" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Bar-B-Q%20Pit,+11861%20US%20Highway%2069%20S+Warren" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">In Side Dining</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering Services</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mtxl410" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtxl410" href="/c/mtxl410/giovanni-s-pizzeria" class="cursor-pointer font-serif text-gray-900 mr-2">Giovanni's Pizzeria</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2701 Del Paso Road # 100</div>
                                            <div>Sacramento, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9165749070" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Giovanni's%20Pizzeria,+2701%20Del%20Paso%20Road%20%23%20100+Sacramento" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Pizza</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mhp5lkb" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhp5lkb" href="/c/mhp5lkb/your-pie-davenport" class="cursor-pointer font-serif text-gray-900 mr-2">Your Pie - Davenport</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4520 E. 53rd Street, Suite 400</div>
                                            <div>Davenport, IA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fyourpie.com%2Fstores%2Fdavenport%2F&s=7e5ae6b6210a80c484fd8c632397df9b&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:5633327811" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Your%20Pie%20-%20Davenport,+4520%20E.%2053rd%20Street%2C%20Suite%20400+Davenport" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fyourpie.com%2Fstores%2Fdavenport%2F&s=7e5ae6b6210a80c484fd8c632397df9b&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Pizza</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Just like in Italy, food and family come first at Your Pie. Their passion for improving their communities by changing the way people experience pizza can be seen and tasted: Doughs are always hand-tossed and made from scratch, and they celebrate the importance of customization using only the freshest ingredients.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mtrfwzg" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtrfwzg" href="/c/mtrfwzg/la-chinita-poblana" class="cursor-pointer font-serif text-gray-900 mr-2">LA Chinita Poblana</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">927 East Westfield Boulevard</div>
                                            <div>Indianapolis, IN</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.lachinitapoblana.com&s=fc9e9a8eb9f2abfce50672405756d174&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:3177228108" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=LA%20Chinita%20Poblana,+927%20East%20Westfield%20Boulevard+Indianapolis" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.lachinitapoblana.com&s=fc9e9a8eb9f2abfce50672405756d174&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Family Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mmdsxgc" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmdsxgc" href="/c/mmdsxgc/purple-moon-night-club" class="cursor-pointer font-serif text-gray-900 mr-2">Purple Moon Night Club</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2525 S Dort Highway</div>
                                            <div>Flint, MI</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:8104249579" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Purple%20Moon%20Night%20Club,+2525%20S%20Dort%20Highway+Flint" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Night Clubs</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Full Blown Night Club, come dance and drink the night away.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mbytf63" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mbytf63" href="/c/mbytf63/aunt-viv-s-homestyle-cooking-llc" class="cursor-pointer font-serif text-gray-900 mr-2">Aunt Viv's Homestyle Cooking, LLC</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1055 Huntington Tpke</div>
                                            <div>Bridgeport, CT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.auntvivscooking.com&s=fd58210c53700ad5ae35e899d38029ef&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2036121367" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Aunt%20Viv's%20Homestyle%20Cooking%2C%20LLC,+1055%20Huntington%20Tpke+Bridgeport" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.auntvivscooking.com&s=fd58210c53700ad5ae35e899d38029ef&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mhp8gtr" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhp8gtr" href="/c/mhp8gtr/dickey-s" class="cursor-pointer font-serif text-gray-900 mr-2">Dickey's</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">801 South Denton Tap Road</div>
                                            <div>Coppell, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Forder.dickeys.com%2Fmenu%2Fdickeys-coppel&s=47686ebda65d1822e452d9de130bf93b&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:9723937800" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Dickey's,+801%20South%20Denton%20Tap%20Road+Coppell" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Forder.dickeys.com%2Fmenu%2Fdickeys-coppel&s=47686ebda65d1822e452d9de130bf93b&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mhp8l6n" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhp8l6n" href="/c/mhp8l6n/dickey-s" class="cursor-pointer font-serif text-gray-900 mr-2">Dickey's</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">251 Southwest Wilshire Blvd.</div>
                                            <div>Burleson, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Forder.dickeys.com%2Fmenu%2Fdickeys-burleson&s=04f364d0458be337eea686865c135047&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:8172952006" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Dickey's,+251%20Southwest%20Wilshire%20Blvd.+Burleson" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Forder.dickeys.com%2Fmenu%2Fdickeys-burleson&s=04f364d0458be337eea686865c135047&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mmpxyj3" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmpxyj3" href="/c/mmpxyj3/mccalls-catering-events" class="cursor-pointer font-serif text-gray-900 mr-2">McCalls Catering & Events</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">350 Florida Street</div>
                                            <div>San Francisco, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.mccallssf.com%2F&s=fde327f47f7151f1d2155c33fc01f37d&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4155528550" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=McCalls%20Catering%20%26%20Events,+350%20Florida%20Street+San%20Francisco" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.mccallssf.com%2F&s=fde327f47f7151f1d2155c33fc01f37d&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Event Recorders</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Wedding Catering Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Business Catering</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Event Planning</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Design Management</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Premier San Francisco Bay Area caterers. McCalls full service fine catering company offers exceptional business and wedding catering, event planning, management and design, floral and lighting.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mtxk25x" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtxk25x" href="/c/mtxk25x/hungry-howie-s" class="cursor-pointer font-serif text-gray-900 mr-2">Hungry Howie's</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">144 W Brigham Rd, UNT #4</div>
                                            <div>Saint George, UT</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=HungryHowies&s=e36e28d612b04e4c6e5bec11b604f01c&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4356560011" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Hungry%20Howie's,+144%20W%20Brigham%20Rd%2C%20UNT%20%234+Saint%20George" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=HungryHowies&s=e36e28d612b04e4c6e5bec11b604f01c&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Chain Pizzerias</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">With 40 years of experience, Hungry Howie's is consistently ranked as one of the Top 10 Pizza Franchises in the nation. From its humble beginnings in Taylor, MI, Hungry Howie's has grown to almost 600 locations in 24 states.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mml74ms" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mml74ms" href="/c/mml74ms/dockside-sea-grille" class="cursor-pointer font-serif text-gray-900 mr-2">Dockside Sea Grille</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">766 Northlake Boulevard</div>
                                            <div>North Palm Beach, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.docksideseagrille.com&s=dc18ae3eb7134c50582b45e198fd2b61&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:5618422180" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Dockside%20Sea%20Grille,+766%20Northlake%20Boulevard+North%20Palm%20Beach" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.docksideseagrille.com&s=dc18ae3eb7134c50582b45e198fd2b61&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Palm Beach Seafood</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Palm Beach Waterfront Dining</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Lake Park Waterfront Dining</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Palm Beach Catering</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Lakes</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Enjoy the unique waterfront dining experience under our thatched tiki hut roof in a romantic yet family friendly restaurant atmosphere.  You'll feel like you walked into paradise! 

At Dockside Sea Grille, we've poured our heart and soul creating a small locally owned business that takes pride in creative, uniqely crafted seafood filled with outside the box ideas.  We aren't Applebee's, TGI Fridays, Olive Garden or any type of corporate restaurant and we certainly don't do things by the book.  We love to acknowledge our customers, getting to know more about them, their favorite drinks, and menu items. 

Dockside is a hole in the wall local Lake Park, West Palm Beach, and North Palm Beach restaurant and we love our customers, friends, and family that we get to share it with.  We hope to see</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm4m2bt" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm4m2bt" href="/c/mm4m2bt/port-hole" class="cursor-pointer font-serif text-gray-900 mr-2">Port Hole</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">932 Herbert Street</div>
                                            <div>Port Orange, FL</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:3867616075" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Port%20Hole,+932%20Herbert%20Street+Port%20Orange" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Cocktail Lounges</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mx4nw27" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mx4nw27" href="/c/mx4nw27/devin-s-review" class="cursor-pointer font-serif text-gray-900 mr-2">Devin's Review</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">7604 Standifer Gap Road, Apartment 102</div>
                                            <div>Chattanooga, TN</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:4234324875" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Devin's%20Review,+7604%20Standifer%20Gap%20Road%2C%20Apartment%20102+Chattanooga" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Call me now at (866) 551-6342 in Chattanooga, TN, for a comprehensive restaurant guide from an aspiring food critic.</div>
    </div>
  </div>                  <div data-test="mb-result-card-mm002tw" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mm002tw" href="/c/mm002tw/tasca-spanish-tapas-rstrnt" class="cursor-pointer font-serif text-gray-900 mr-2">Tasca Spanish Tapas Rstrnt</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">1610 Commonwealth Avenue</div>
                                            <div>Brighton, MA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:6177308002" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Tasca%20Spanish%20Tapas%20Rstrnt,+1610%20Commonwealth%20Avenue+Brighton" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mh1bph6" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mh1bph6" href="/c/mh1bph6/minas-brazilian-restaurant-cacha-aria" class="cursor-pointer font-serif text-gray-900 mr-2">Minas Brazilian Restaurant & Cachaçaria</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">41 Franlkin Street</div>
                                            <div>San Francisco, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.sanfranciscobrazilianfood.com&s=92865674cbea96670f0694ce38991536&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4154668371" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Minas%20Brazilian%20Restaurant%20%26%20Cacha%C3%A7aria,+41%20Franlkin%20Street+San%20Francisco" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.sanfranciscobrazilianfood.com&s=92865674cbea96670f0694ce38991536&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under American Restaurants</span>
          </div>
              </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Description:	Presented in a warm upscale environment, a wide variety of cachaças are available, providing a rare opportunity to explore the breadth and width of Brazil’s unique, signature spirit. Minas full service bar, array of appetizers, seasonal menu and weekend cultural entertainment makes it the perfect location to enjoy an international night out in the heart of the City.	
Services: 	Authentic Brazilian Food & Drinks, Lunch & Dinner, Delivery	
Payment Types:	Cash, Visa, MasterCard, Discover, American	
Business Hours:	Monday-Wednesday: 11:30AM-2:00PM & 5:30PM-9:30PM Thursday: 11:30AM-2:30PM & 5:00PM-10:00PM Friday: 11:30AM-2:30PM & 5:00PM-11:00PM Saturday: 5:00PM-11:00PM Sunday: 11:30AM-4:00PM</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmz2rqk" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20110417wOxPhJK5bb)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmz2rqk" href="/c/mmz2rqk/cosimo-s-italian-carry-out" class="cursor-pointer font-serif text-gray-900 mr-2">Cosimo's Italian Carry Out</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3 Huntington Street</div>
                                            <div>Cortland, NY</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.facebook.com%2F%3Fref%3Dhome%23!%2Fpages%2FCosimos-Italian-Carry-Out%2F131434126920869&s=f99b511bd3e003e569c96ae5aca4478c&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:6077531000" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Cosimo's%20Italian%20Carry%20Out,+3%20Huntington%20Street+Cortland" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.facebook.com%2F%3Fref%3Dhome%23!%2Fpages%2FCosimos-Italian-Carry-Out%2F131434126920869&s=f99b511bd3e003e569c96ae5aca4478c&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering , Any Size Or Type Of Function</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Pizza, Calzones</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Salads, Burgers, Club Sandwiches</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Home Made Bread And Soup</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Italian And American Cuisines</span>
            </div>
                        </div>
    </div>
  </div>
      <div class="hidden lg:block">
      <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">Find us on Facebook</div>
    </div>
  </div>                  <div data-test="mb-result-card-mmnjg1f" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20130107npNfAVwSWc)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmnjg1f" href="/c/mmnjg1f/little-mexico-mexican-grill" class="cursor-pointer font-serif text-gray-900 mr-2">Little Mexico Mexican Grill</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">722 Commonwealth Drive</div>
                                            <div>Norton, VA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.littlemexicomexicangrill.com&s=b8aa141051770062980093f6794fd815&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:2766796948" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Little%20Mexico%20Mexican%20Grill,+722%20Commonwealth%20Drive+Norton" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.littlemexicomexicangrill.com&s=b8aa141051770062980093f6794fd815&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Mexican Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mmfnb4f" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmfnb4f" href="/c/mmfnb4f/a-w-all-american-food" class="cursor-pointer font-serif text-gray-900 mr-2">A&W All-American Food</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">10 E Highway 12</div>
                                            <div>Webster, SD</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:6053454140" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=A%26W%20All-American%20Food,+10%20E%20Highway%2012+Webster" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Root Beer Floats</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mhp55vg" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mhp55vg" href="/c/mhp55vg/smokey-diva-s" class="cursor-pointer font-serif text-gray-900 mr-2">Smokey Diva’s</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">239 Onota Street</div>
                                            <div>Pittsfield, MA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=http%3A%2F%2Fwww.92ndsauce.com&s=8ed09f86fcf60a31c09c2b3c7899876c&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4133444206" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Smokey%20Diva%E2%80%99s,+239%20Onota%20Street+Pittsfield" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=http%3A%2F%2Fwww.92ndsauce.com&s=8ed09f86fcf60a31c09c2b3c7899876c&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Restaurants</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mmsl5cs" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mmsl5cs" href="/c/mmsl5cs/king-beast-pizza-inc" class="cursor-pointer font-serif text-gray-900 mr-2">King Beast Pizza, Inc</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">3720 Kitsap Way # C</div>
                                            <div>Bremerton, WA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:3603732020" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=King%20Beast%20Pizza%2C%20Inc,+3720%20Kitsap%20Way%20%23%20C+Bremerton" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Chain Pizzerias</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mtxrk42" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mtxrk42" href="/c/mtxrk42/american-beverage-service-co" class="cursor-pointer font-serif text-gray-900 mr-2">American Beverage Service CO</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">2351 Santa Anna Avenue</div>
                                            <div>Dallas, TX</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                                  <div class="flex md:hidden mt-3">
                          <a href="tel:9722938799" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=American%20Beverage%20Service%20CO,+2351%20Santa%20Anna%20Avenue+Dallas" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                  </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                  <div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under Beverage Stores</span>
          </div>
              </div>
    </div>
  </div>
  </div>                  <div data-test="mb-result-card-mrpr7ql" class="md:rounded bg-white border-b  border-primary-light-v1 px-3 py-4 md:p-8 md:mt-4 mx-4 xl:mx-0">
  <div class="flex items-start justify-between">
    <div>
      <div class="border border-gray-200 store-logo rounded">
                  <div class="bg-no-repeat bg-center bg-contain lazy" style="background-image: url(//images.manta-r3.com/api/claim-this-profile/image-mgr/view/20131121x98IS7WjQj)"></div>
              </div>
    </div>
    <div class="flex flex-grow justify-center">
      <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
        <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
          <a data-test="mb-result-card-title-mrpr7ql" href="/c/mrpr7ql/giacobbe-s-restaurant-and-catering" class="cursor-pointer font-serif text-gray-900 mr-2">Giacobbe's Restaurant and Catering</a>
                                    <div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>
                              </div>
                  <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
                              <div class="hidden md:block">4472 Pearl Avenue</div>
                                            <div>San Jose, CA</div>
                          </div>
          </div>
                  <div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>(*************</div>
          </div>
                          <div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="/urlverify?redirect=giacobbes.com&s=0bacaa6d43c763d8d1f9963791098a8c&cb=1441464" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>
                          <div class="flex md:hidden mt-3">
                          <a href="tel:4082692221" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="https://maps.google.com/maps?q=Giacobbe's%20Restaurant%20and%20Catering,+4472%20Pearl%20Avenue+San%20Jose" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                                      <a href="/urlverify?redirect=giacobbes.com&s=0bacaa6d43c763d8d1f9963791098a8c&cb=1441464" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>
                      </div>
              </div>
      <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
        <div class="flex rounded w-full mb-4">
          <div class="flex justify-between items-center w-full">
            <div class="flex">
                                          <div class="mt-1 rounded-l bg-success px-2 flex items-center">
  <i class="text-xs text-white fa fa-check"></i>
</div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                  CLAIMED
                </div>
                                      </div>
                      </div>
        </div>
                              <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Catering Services</span>
            </div>
                      <div class="flex items-baseline mb-1">
              <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
              <span class="text-gray-800">Food Services</span>
            </div>
                        </div>
    </div>
  </div>
  </div>                <div id="more-results">
        </div>
        <div data-test="mb-infinite-scroll-trigger" id="infinite-scroll-trigger"></div>
        <div data-test="mb-spinner-loader" id="loading-more-content" class="hidden my-8">
          <img class="mx-auto" width="100" height="100" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/spinner.svg" />
        </div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb">Companies</a>
                              <span class="mx-1 fa fa-angle-right"></span>
                          </span>
                      <span>
              <a class="hover:underline" href="/mb_33_C4_000/restaurants_and_bars">Restaurants and Bars</a>
                          </span>
                  </div>
        <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse Subcategories</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C432D_000/bars_taverns">Bars / Taverns</a> (62,324)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C4091_000/confectionery">Confectionery</a> (3,976)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_34_C432C_000/restaurants">Restaurants</a> (531,303)
          </div>
              </div>
    </div>
  </div>
</div>
        <div class="mb-2 text-xs text-gray-800 mx-4 xl:mx-0">
                      <span>
              <a class="hover:underline" href="/mb_33_C4_000/restaurants_and_bars">United States</a>
                          </span>
                  </div>
                  <div class="mb-8 flex flex-col items-center lg:items-start w-full px-4 xl:mx-0">
  <div class="flex flex-col w-full">
          <div class="text-xl pb-4">Browse undefined</div>
        <div class="flex flex-col lg:flex-row w-full">
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_01/restaurants_and_bars/alabama">Alabama</a> (8,092)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_02/restaurants_and_bars/alaska">Alaska</a> (1,526)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_03/restaurants_and_bars/arizona">Arizona</a> (10,729)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_04/restaurants_and_bars/arkansas">Arkansas</a> (4,032)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_05/restaurants_and_bars/california">California</a> (76,869)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_06/restaurants_and_bars/colorado">Colorado</a> (12,444)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_07/restaurants_and_bars/connecticut">Connecticut</a> (7,868)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_08/restaurants_and_bars/delaware">Delaware</a> (1,993)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_09/restaurants_and_bars/district_of_columbia">District of Columbia</a> (1,717)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_10/restaurants_and_bars/florida">Florida</a> (39,458)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_11/restaurants_and_bars/georgia">Georgia</a> (15,885)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_12/restaurants_and_bars/hawaii">Hawaii</a> (2,555)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_13/restaurants_and_bars/idaho">Idaho</a> (2,690)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_14/restaurants_and_bars/illinois">Illinois</a> (25,322)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_15/restaurants_and_bars/indiana">Indiana</a> (9,388)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_16/restaurants_and_bars/iowa">Iowa</a> (5,374)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_17/restaurants_and_bars/kansas">Kansas</a> (5,093)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_18/restaurants_and_bars/kentucky">Kentucky</a> (6,528)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_19/restaurants_and_bars/louisiana">Louisiana</a> (7,535)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_20/restaurants_and_bars/maine">Maine</a> (2,416)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_21/restaurants_and_bars/maryland">Maryland</a> (11,988)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_22/restaurants_and_bars/massachusetts">Massachusetts</a> (13,317)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_23/restaurants_and_bars/michigan">Michigan</a> (14,704)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_24/restaurants_and_bars/minnesota">Minnesota</a> (8,536)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_25/restaurants_and_bars/mississippi">Mississippi</a> (3,874)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_26/restaurants_and_bars/missouri">Missouri</a> (11,039)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_27/restaurants_and_bars/montana">Montana</a> (2,447)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_28/restaurants_and_bars/nebraska">Nebraska</a> (3,917)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_29/restaurants_and_bars/nevada">Nevada</a> (4,978)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_30/restaurants_and_bars/new_hampshire">New Hampshire</a> (2,375)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_31/restaurants_and_bars/new_jersey">New Jersey</a> (19,840)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_32/restaurants_and_bars/new_mexico">New Mexico</a> (3,115)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_33/restaurants_and_bars/new_york">New York</a> (39,986)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_34/restaurants_and_bars/north_carolina">North Carolina</a> (17,288)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_35/restaurants_and_bars/north_dakota">North Dakota</a> (1,621)
          </div>
              </div>
      <div class="flex flex-col lg:w-1/3">
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_36/restaurants_and_bars/ohio">Ohio</a> (20,123)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_37/restaurants_and_bars/oklahoma">Oklahoma</a> (5,551)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_38/restaurants_and_bars/oregon">Oregon</a> (6,741)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_39/restaurants_and_bars/pennsylvania">Pennsylvania</a> (27,523)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_53/restaurants_and_bars/puerto_rico">Puerto Rico</a> (332)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_40/restaurants_and_bars/rhode_island">Rhode Island</a> (2,641)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_41/restaurants_and_bars/south_carolina">South Carolina</a> (12,132)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_42/restaurants_and_bars/south_dakota">South Dakota</a> (1,632)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_43/restaurants_and_bars/tennessee">Tennessee</a> (11,419)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_44/restaurants_and_bars/texas">Texas</a> (56,253)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_45/restaurants_and_bars/utah">Utah</a> (3,877)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_46/restaurants_and_bars/vermont">Vermont</a> (1,290)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_47/restaurants_and_bars/virginia">Virginia</a> (14,596)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_48/restaurants_and_bars/washington">Washington</a> (10,911)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_49/restaurants_and_bars/west_virginia">West Virginia</a> (3,862)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_50/restaurants_and_bars/wisconsin">Wisconsin</a> (10,112)
          </div>
                  <div class="py-2">
            <a class="hover:underline" href="/mb_43_C4_51/restaurants_and_bars/wyoming">Wyoming</a> (1,125)
          </div>
              </div>
    </div>
  </div>
</div>
                      </div>
    </main>
    <style>
  @media (max-width: 360px) {
    .xs-device {
      font-size: 0.875rem;
    }
    .xs-8 {
      height: 2rem;
      width: 2rem;
    }
  }
</style>
<footer>
  <div class="w-full border-t bg-darks-v1 text-primary-light-v1 py-8 lg:py-16 flex flex-col justify-center items-center px-4 sm:px-6">
    <div class="grid grid-cols-8 gap-4 sm:gap-8 max-w-header w-full">
      <div class="hidden lg:flex justify-between mt-6 flex-col col-span-2">
        <div>
          <div>
            <a href="/" data-test="btn-logo-navbar">
              <img loading="lazy" width="162" height="37" class="mb-6 mr-3" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/manta_logo_coral.svg" alt="Manta Logo">
              <span class="sr-only">Manta Home</span>
            </a>
          </div>
          <div class="flex">
            <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
                <span class="sr-only">Manta on Twitter</span>
              </span>
            </a>
            <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
                <span class="sr-only">Manta on Facebook</span>
              </span>
            </a>
            <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
              <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
                <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
                <span class="sr-only">Manta on LinkedIn</span>
              </span>
            </a>
          </div>
        </div>
        <div class="text-primary-light-v1 xs-device">
          © 2025 Manta Media Inc.<br>All rights reserved.
                  </div>
      </div>
      <div class="grid grid-cols-2 sm:grid-cols-3 gap-8 col-span-8 sm:col-span-5 lg:col-span-4">
        <div class="flex flex-col">
          <span class="font-serif">Manta</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-contact-footer-desktop" href="/contact" class="hover:font-bold py-1">Contact Us</a>
            <a data-test="btn-about-footer-desktop" href="/about-us" class="hover:font-bold py-1">About Us</a>
            <a data-test="btn-reviews-footer-desktop" href="/manta-reviews" class="hover:font-bold py-1">Reviews</a>
            <a data-test="btn-careers-footer-desktop" href="/careers" class="hover:font-bold py-1">Careers</a>
            <a data-test="btn-termsConditions-footer-desktop" href="/terms-and-conditions" class="hover:font-bold py-1">Terms & Conditions</a>
            <a data-test="btn-privacyPolicy-footer-desktop" href="/privacy-policy" class="hover:font-bold py-1">Privacy Policy</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Services</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-digital-mkt-footer-desktop" href="/digital-marketing-services" class="hover:font-bold py-1">Digital Marketing Services</a>
            <a data-test="btn-seo-services-footer-desktop" href="/services/organic-seo-company" class="hover:font-bold py-1">SEO Services</a>
            <a data-test="btn-local-seo-footer-desktop" href="/services/affordable-local-seo" class="hover:font-bold py-1">Local SEO</a>
            <a data-test="btn-national-seo-footer-desktop" href="/services/national-seo-company" class="hover:font-bold py-1">National SEO</a>
            <a data-test="btn-free-seo-footer-desktop" href="/free-seo-website-test" class="hover:font-bold py-1">Free SEO Website Test</a>
            <a data-test="btn-listings-footer-desktop" href="/business-listings/listings-management" class="hover:font-bold py-1">Listings Management</a>
            <a data-test="btn-display-ads-footer-desktop" href="/services/display-advertising" class="hover:font-bold py-1">Display Ads</a>
            <a data-test="btn-ppc-footer-desktop" href="/services/ppc-consulting" class="hover:font-bold py-1">PPC Consulting</a>
            <a data-test="btn-websites-footer-desktop" href="/small-business-marketing/websites" class="hover:font-bold py-1">Website Creation</a>
          </div>
        </div>
        <div class="flex flex-col">
          <span class="font-serif">Resources</span>
          <div class="flex flex-col mt-6">
            <a data-test="btn-seo-faq-footer-desktop" href="/seo-faqs" class="hover:font-bold py-1">SEO FAQ</a>
            <a data-test="btn-ecommerce-footer-desktop" href="/seo-industry-guide/ecommerce-seo-guide" class="hover:font-bold py-1">Ecommerce SEO Guide</a>
            <a data-test="btn-construction-footer-desktop" href="/seo-industry-guide/seo-for-construction-companies" class="hover:font-bold py-1">Construction SEO Guide</a>
            <a data-test="btn-hvac-footer-desktop" href="/seo-industry-guide/seo-for-hvac" class="hover:font-bold py-1">HVAC SEO Guide</a>
            <a data-test="btn-homeyou-footer-desktop" href="/costs" class="hover:font-bold py-1">Home Services Cost</a>
          </div>
        </div>
      </div>
      <div class="border-t border-primary-light-v1 sm:border-none pt-6 sm:pt-0 flex flex-col col-span-8 sm:col-span-3 lg:col-span-2">
        <span class="font-serif">Manta Members</span>
                  <div class="flex items-center mt-6">
            <a data-test="btn-login-footer-desktop" class="hover:font-bold mr-4" href="/member/login">Log In</a>
            <a data-test="btn-login-footer-desktop" class="btn bg-primary-v1 text-white inline-block hover:font-bold" href="/member/register">Sign Up</a>
          </div>
                  <div class="mt-6">
                      <p class="mb-2">Search Manta's Directory to find the Small Business you're looking for</p>
            <a data-test="btn-index-footer-desktop" class="bg-primary-v1 py-2 px-4 rounded text-white inline-block hover:font-bold" href="/">Find a Business Near You</a>
                  </div>
      </div>
    </div>
    <div class="lg:hidden border-b border-t border-primary-light-v1 py-4 my-6 w-full">
      <div class="flex text-primary-light-v1">
        <a data-test="btn-help-footer-desktop-mobile" href="/contact" class="mr-6">Help</a>
        <a data-test="btn-termsConditions-footer-mobile" href="/terms-and-conditions" class="mr-6">Terms</a>
        <a data-test="btn-privacyPolicy-footer-mobile" href="/privacy-policy" class="mr-6">Privacy</a>
      </div>
    </div>
    <div class="flex lg:hidden justify-between items-center w-full">
      <div class="text-primary-light-v1 xs-device">
        © 2025 Manta Media Inc.<br>All rights reserved.
              </div>
      <div class="flex">
        <a data-test="btn-twitter-footer" href="https://twitter.com/Manta">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-twitter text-lg xs-device"></i>
            <span class="sr-only">Manta on Twitter</span>
          </span>
        </a>
        <a data-test="btn-facebook-footer" href="https://facebook.com/mantacom">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-facebook-f text-lg xs-device"></i>
            <span class="sr-only">Manta on Facebook</span>
          </span>
        </a>
        <a data-test="btn-linkedin-footer" href="https://www.linkedin.com/company/manta/">
          <span class="w-10 h-10 xs-8 rounded-full border-primary-light-v1 border mr-2 mb-2 cursor-pointer flex justify-center items-center">
            <i class="not-sr-only fab fa-linkedin text-lg xs-device"></i>
            <span class="sr-only">Manta on LinkedIn</span>
          </span>
        </a>
      </div>
    </div>
  </div>
  
      <script data-cfasync="false" async src="//cdn.intergient.com/1024347/72853/ramp.js"></script>
         </footer>    <script id="lazy-load-images">
  (function(hasObserver) {
    var createObservers = function(list, loadFn) {
      if (hasObserver) {
        var observer = new IntersectionObserver(function(entries, self) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              loadFn(entry.target);
              self.unobserve(entry.target);
            }
          });
        }, { rootMargin: '0px 0px 200px 0px' });

        list.forEach(function(el) {
          observer.observe(el);
        });
      } else {
        list.forEach(loadFn);
      }
    };

    var imgs = document.querySelectorAll('[lazy-load]');
    if (imgs.length) {
      createObservers(imgs, function(el) {
        el.setAttribute('src', el.getAttribute('lazy-load'));
      });
    }

    var bgs = document.querySelectorAll('.lazy');
    if (bgs.length) {
      createObservers(bgs, function(el) {
        el.classList.remove('lazy');
      });
    }
  })(typeof IntersectionObserver !== 'undefined');
</script>
    <script>
  const createCompany = company => {
    return (`
    <div class="flex items-start justify-between">
      <div>
        <div class="border border-gray-200 store-logo rounded">
          ${
            company.logo ? `<div class="bg-no-repeat bg-center bg-contain" style="background-image: url(${company.logo})"></div>`
            : `<div class="hidden md:block">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400" style="font-size: 4rem"></i>
            </div>
          </div>
          <div class="md:hidden">
            <div class="w-full h-full flex items-center justify-center">
              <i class="fad fa-store-alt text-gray-400 text-6xl"></i>
            </div>
          </div>`
          }
        </div>
      </div>
      <div class="flex flex-grow justify-center">
        <div class="flex flex-col w-full md:w-1/2 pl-4 sm:pl-8 text-gray-800">
          <div class="font-bold text-base sm:text-lg overflow-wrap text-gray-800 flex justify-between">
            <a data-test="mb-result-card-title-${company.emid}" href="${company.url}" class="cursor-pointer font-serif text-gray-900 mr-2 break-words">${company.name}</a>
            ${company.claimStatus === 'PBL' 
            ? `<div class="md:hidden"><svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="Icons/Verified-Badge">
      <g id="badge">
        <path d="M11.043346,16 C10.95043,16 10.8602876,15.9805831 10.7747679,15.9417493 L8.46065077,14.8899997 C8.31457386,14.8238897 8.15971384,14.7901413 8.00023113,14.7901413 C7.84028616,14.7901413 7.68542614,14.8238897 7.5398115,14.8899997 L5.22523208,15.9417493 C5.13971236,15.9805831 5.04956997,16 4.95665396,16 C4.68668903,16 4.44168661,15.8303332 4.34692153,15.577451 L3.45428065,13.197492 C3.34102482,12.8965298 3.10388097,12.6589038 2.80294404,12.5461007 L0.*********,11.653385 C0.*********,11.5891242 0.*********,11.4615273 0.0501338798,11.2932474 C-0.0196686952,11.1249675 -0.0164328143,10.9409691 0.0589169853,10.7750007 L1.1105783,8.46022711 C1.24371169,8.16758647 1.24371169,7.83241353 1.1105783,7.53977289 L0.0589169853,5.22499928 C-0.0164328143,5.05903089 -0.0196686952,4.87503251 0.0501338798,4.70675258 C0.*********,4.53847265 0.*********,4.41041348 0.*********,4.34661504 L2.80294404,3.45343697 C3.10388097,3.34063394 3.34102482,3.1034702 3.45428065,2.80250802 L4.34692153,0.42208674 C4.44168661,0.169204542 4.68668903,-5.68434189e-14 4.95665396,-5.68434189e-14 C5.04956997,-5.68434189e-14 5.13971236,0.0194169147 5.22523208,0.058250744 L7.53934923,1.11000029 C7.68542614,1.17611026 7.84028616,1.20985871 8.00023113,1.20985871 C8.15971384,1.20985871 8.31457386,1.17611026 8.4601885,1.11000029 L10.7747679,0.058250744 C10.8602876,0.0194169147 10.95043,-5.68434189e-14 11.043346,-5.68434189e-14 C11.313311,-5.68434189e-14 11.5583134,0.169204542 11.6530785,0.42208674 L12.5457193,2.80250802 C12.6589752,3.1034702 12.896119,3.34063394 13.197056,3.45343697 L15.5772775,4.34661504 C15.7478547,4.41041348 15.8800635,4.53847265 15.9498661,4.70675258 C16.0196687,4.87503251 16.0164328,5.05903089 15.941083,5.22499928 L14.8894217,7.53931058 C14.7562883,7.83241353 14.7562883,8.16758647 14.8894217,8.46022711 L15.941083,10.7750007 C16.0164328,10.9409691 16.0196687,11.1249675 15.9498661,11.2932474 C15.8800635,11.4615273 15.7478547,11.5891242 15.5772775,11.653385 L13.197056,12.5461007 C12.896119,12.6589038 12.6589752,12.8965298 12.5457193,13.197492 L11.6530785,15.577451 C11.5583134,15.8303332 11.313311,16 11.043346,16" id="Fill-1" fill="#24b85f"></path>
        <polyline id="Shape-Copy" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="4.9 8.27280651 6.96382537 10.315677 11.221429 6.10139102"></polyline>
      </g>
    </g>
  </g>
</svg>
</div>`
            : company.claimStatus === 'CLAIMED'
            ? `<div class="md:hidden"><svg width="16" height="16" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.4944 19.75C14.3724 19.75 14.2541 19.726 14.1419 19.6781L11.1046 18.3798C10.9129 18.2982 10.7096 18.2566 10.5003 18.2566C10.2904 18.2566 10.0871 18.2982 9.896 18.3798L6.85812 19.6781C6.74587 19.726 6.62756 19.75 6.50561 19.75C6.15128 19.75 5.82971 19.5406 5.70533 19.2284L4.53374 16.2907C4.3851 15.9192 4.07384 15.6258 3.67886 15.4866L0.554823 14.3846C0.330941 14.3053 0.157417 14.1478 0.0658007 13.9401C-0.0258152 13.7324 -0.0215681 13.5053 0.0773285 13.3004L1.45763 10.4431C1.63237 10.0819 1.63237 9.66814 1.45763 9.30691L0.0773285 6.44961C-0.0215681 6.24474 -0.0258152 6.01762 0.0658007 5.8099C0.157417 5.60218 0.330941 5.4441 0.554823 5.36535L3.67886 4.26284C4.07384 4.12359 4.3851 3.83085 4.53374 3.45935L5.70533 0.521013C5.82971 0.208862 6.15128 0 6.50561 0C6.62756 0 6.74587 0.0239678 6.85812 0.0719033L9.8954 1.37016C10.0871 1.45176 10.2904 1.49342 10.5003 1.49342C10.7096 1.49342 10.9129 1.45176 11.104 1.37016L14.1419 0.0719033C14.2541 0.0239678 14.3724 0 14.4944 0C14.8487 0 15.1703 0.208862 15.2947 0.521013L16.4663 3.45935C16.6149 3.83085 16.9262 4.12359 17.3211 4.26284L20.4452 5.36535C20.6691 5.4441 20.8426 5.60218 20.9342 5.8099C21.0258 6.01762 21.0216 6.24474 20.9227 6.44961L19.5424 9.30634C19.3676 9.66814 19.3676 10.0819 19.5424 10.4431L20.9227 13.3004C21.0216 13.5053 21.0258 13.7324 20.9342 13.9401C20.8426 14.1478 20.6691 14.3053 20.4452 14.3846L17.3211 15.4866C16.9262 15.6258 16.6149 15.9192 16.4663 16.2907L15.2947 19.2284C15.1703 19.5406 14.8487 19.75 14.4944 19.75" fill="#14BA5C"/>
  <path d="M6.43164 9.21171L9.14041 11.7334L14.7285 6.53137" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</div>`
            : ''
            }
          </div>
          ${company.recommendations.averageRating.ratingCount > 0
          ? `<div class="flex items-center">
              <div class="text-primary-v1 font-bold mr-4">${company.recommendations.averageRating.ratingValue}</div>
              {stars({ stars: ${company.recommendations.averageRating.ratingValue} })/}
              <div class="ml-4">(${company.recommendations.averageRating.ratingCount})</div>
            </div>` 
          : ''}
          <div class="flex items-center mt-1 md:mt-3">
            <img width="16" height="20" class="pt-1 w-5" src="//cc3.manta-r3.com/dist/fc1ac3e4/img/pin-gray.svg" alt="Map pin">
            <div class="ml-4">
              ${company.location.address !== undefined ? `<div class="hidden md:block">${company.location.address}</div>` : ''}
              ${company.location.country && company.location.country !== 'United States' 
              ? `<div>${company.location.city}, ${company.location.country}</div>`
              : `<div>${company.location.city}, ${company.location.stateAbbrv}</div>`}
            </div>
          </div>
          ${company.contactInfo.phone ? `<div class="hidden md:flex items-center mt-2">
            <i class="fa fa-phone mr-4 text-xl fa-rotate-90 pt-1 text-gray-400"></i>
            <div>${company.contactInfo.phone}</div>
          </div>` : ''}
          ${company.contactInfo.website ? `<div class="hidden md:flex items-center mt-3">
            <i class="fa fa-globe mr-4 text-xl pt-1 text-gray-400"></i>
            <a href="${company.contactInfo.website}" rel="nofollow noopener" class="cursor-pointer hover:text-darks-v1 hover:underline" target="_blank">Visit Website</a>
          </div>` : ''}
          ${company.contactInfo.phone || company.contactInfo.website || company.location.address 
            ? `
            <div class="flex md:hidden mt-3">
              ${company.contactInfo.phone ? `<a href="tel:${company.contactInfo.phone}" rel="nofollow noopener"class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-phone fa-rotate-90 text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
              ${company.location.address ? `
                <a href="${`https://maps.google.com/maps?q=${encodeURIComponent(company.name)},` + '+' + `${encodeURIComponent(company.location.address)}` + '+' + `${encodeURIComponent(company.location.city)}`}" class="flex items-center mr-2">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-directions text-gray-400"></i>
                  </div>
                </div>
              </a>
                ` : ''}
                ${company.contactInfo.website ? `
                  <a href="${company.contactInfo.website}" rel="nofollow noopener" class="flex items-center">
                <div class="flex flex-col items-center justify-center">
                  <div class="bg-gray-light w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fa fa-globe text-gray-400"></i>
                  </div>
                </div>
              </a>` : ''}
            </div>` 
            : ''}
        </div>
        <div class="hidden md:flex flex-col w-1/2 pl-4 text-gray-500">
          <div class="flex rounded w-full mb-4">
            <div class="flex justify-between w-full">
              <div class="flex justify-between items-center">
                ${company.claimStatus === 'PBL' 
                ? `
                <div class="mt-1 rounded-l bg-primary-v1 px-1 py-1 flex flex-row items-center">
                  <i class="text-white fa fa-badge-check"></i>
                </div>
                <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 whitespace-no-wrap w-28">
                  MANTA VERIFIED
                </div>` 
                : company.claimStatus === 'CLAIMED'
                ? `<div class="mt-1 rounded-l bg-success px-2">
                    <i class="text-xs text-white fa fa-check"></i>
                  </div>
                  <div class="mt-1 rounded-r bg-primary-light-v1 text-center py-1 text-xs text-darks-v1 leading-relaxed w-20">
                    CLAIMED
                  </div>`
                : ''
                }
                ${company?.serviceAreasString && `<p class="text-sm text-gray-600 hidden md:block italic ml-4">Serving ${company.serviceAreasString.split(',')[0]} and the Surrounding Area</p>`}
              </div>
            </div>
          </div>
          ${company.products.productTerms.list.length > 0 
            ? company.products.productTerms.list.forEach(term => {
              return `
                <div class="flex items-baseline mb-1">
                  <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
                  <span class="text-gray-800">${term.name}</span>
                </div>
              `
            })
            : `<div class="flex items-baseline mb-1">
            <i class="fa fa-check-circle text-darks-v1 mr-2"></i>
            <span class="text-gray-800">Categorized under ${company.industry.sicm.description}</span>
          </div>`}
        </div>
      </div>
    </div>
    <div>
    ${company.detailedDescription 
      ? `<div class="hidden lg:block">
    <div class="border-t border-gray-200 mt-4 pt-4 px-20 clamp lines-2 text-gray-800" style="max-height: 68px">${company.detailedDescription}</div>
    </div>` 
      : ''}
    </div>
    `);
  }
</script>    <script>

  const trigger = document.querySelector('#infinite-scroll-trigger');
  const loader = document.querySelector('#loading-more-content');
  const results = document.querySelector('#more-results');

  const config = {
    root: null,
    threshold: 0.1,
    rootMargin: '0px'
  };

  let search = 2;

  const sicAndGeoLastIndex = window.location.pathname.lastIndexOf('/');
  const sicAndGeoFirstIndex = window.location.pathname.indexOf('_');
  const sicAndGeo = window.location.pathname.slice(sicAndGeoFirstIndex + 1, sicAndGeoLastIndex);

  const observer = new IntersectionObserver(async (entries, self) => {
    try {
      const [entry] = entries;
      if (!entry.isIntersecting) return;
      loader.classList.toggle('hidden');
      const response = await axios.get(`/more-results/${sicAndGeo}?pg=${search}`, {
        headers: { 'x-request-id': '3dc20d8f63f91a6ff36a9722273055c4' }
      });
      if (response.data.companies.list.length === 0) {
        self.unobserve(entry.target);
      }
      loader.classList.toggle('hidden');
      let companies = [];
      response.data.companies.list.forEach((company) => {
        const resultContainer = document.createElement('div');
        resultContainer.setAttribute('data-test', `mb-result-card-${company.emid}`);
        resultContainer.classList.add('md:rounded', 'bg-white', 'border-b', 'border-primary-light-v1', 'px-3', 'py-4', 'md:p-8', 'md:mt-4', 'mx-4', 'xl:mx-0');
        resultContainer.innerHTML = createCompany(company);
        companies.push(resultContainer);
      })
      results.append(...companies);
      search++;
    } catch(error) {
      loader.classList.toggle('hidden');
      self.unobserve(entry.target);
    }
  });

  observer.observe(trigger, config);

</script>  </body>
</html>
