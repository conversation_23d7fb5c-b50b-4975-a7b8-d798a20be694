#!/usr/bin/env python3
"""
Session Extraction Tool
Easy-to-use script for extracting fresh cookies and headers from TruthFinder and Manta.
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def extract_truthfinder_session():
    """Extract TruthFinder session with real browser interaction."""
    print("🔍 TruthFinder Session Extraction")
    print("=" * 50)
    print("This will open a browser and perform a real search on TruthFinder")
    print("to capture fresh cookies and API headers.")
    print()
    
    try:
        from src.utils.session_extractor import SessionExtractor
        
        # Create extractor (set headless=False to see the browser)
        extractor = SessionExtractor(headless=False, browser_type="chromium")
        
        # Extract session data
        print("🚀 Starting browser and navigating to TruthFinder...")
        session_data = await extractor.extract_truthfinder_session("<PERSON>")
        
        if session_data:
            # Save session data
            filepath = extractor.save_session_data(session_data, "truthfinder_session.json")
            
            # Generate API config
            api_config = extractor.generate_api_config(session_data)
            if api_config:
                config_filepath = "sessions/truthfinder_api_config.json"
                with open(config_filepath, 'w') as f:
                    json.dump(api_config, f, indent=2)
                
                print(f"✅ TruthFinder session extracted successfully!")
                print(f"📁 Session data: {filepath}")
                print(f"⚙️  API config: {config_filepath}")
                print(f"🍪 Cookies captured: {len(session_data.get('cookies', {}))}")
                print(f"📡 API requests captured: {len(session_data.get('api_requests', []))}")
                
                # Show key cookies for manual update
                cookies = session_data.get('cookies', {})
                important_cookies = ['sessionId', 'device-id', '__cf_bm', 'sessionCreated']
                
                print(f"\n🔑 Key cookies for API:")
                for cookie_name in important_cookies:
                    if cookie_name in cookies:
                        print(f"   {cookie_name}: {cookies[cookie_name]}")
                
                # Show API key if found
                api_requests = session_data.get('api_requests', [])
                for req in api_requests:
                    headers = req.get('headers', {})
                    if 'api-key' in headers:
                        print(f"🔐 API Key: {headers['api-key']}")
                        break
                
                return session_data
            else:
                print("⚠️  No API requests captured. Try performing a search manually.")
                return None
        else:
            print("❌ Failed to extract TruthFinder session")
            return None
            
    except ImportError:
        print("❌ Playwright not installed. Install with: pip install playwright")
        print("   Then run: playwright install")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def extract_manta_session():
    """Extract Manta session with real browser interaction."""
    print("🏢 Manta Session Extraction")
    print("=" * 50)
    print("This will open a browser and browse Manta business listings")
    print("to capture fresh cookies and API headers.")
    print()
    
    try:
        from src.utils.session_extractor import SessionExtractor
        
        # Create extractor (set headless=False to see the browser)
        extractor = SessionExtractor(headless=False, browser_type="chromium")
        
        # Extract session data
        print("🚀 Starting browser and navigating to Manta...")
        session_data = await extractor.extract_manta_session("restaurant", "houston")
        
        if session_data:
            # Save session data
            filepath = extractor.save_session_data(session_data, "manta_session.json")
            
            # Generate API config
            api_config = extractor.generate_api_config(session_data)
            if api_config:
                config_filepath = "sessions/manta_api_config.json"
                with open(config_filepath, 'w') as f:
                    json.dump(api_config, f, indent=2)
                
                print(f"✅ Manta session extracted successfully!")
                print(f"📁 Session data: {filepath}")
                print(f"⚙️  API config: {config_filepath}")
                print(f"🍪 Cookies captured: {len(session_data.get('cookies', {}))}")
                print(f"📡 API requests captured: {len(session_data.get('api_requests', []))}")
                
                # Show key cookies
                cookies = session_data.get('cookies', {})
                important_cookies = ['cf_clearance', 'pwBotScore', 'cust_id', '_ga']
                
                print(f"\n🔑 Key cookies for API:")
                for cookie_name in important_cookies:
                    if cookie_name in cookies:
                        print(f"   {cookie_name}: {cookies[cookie_name]}")
                
                return session_data
            else:
                print("⚠️  No API requests captured. Try browsing more business listings.")
                return None
        else:
            print("❌ Failed to extract Manta session")
            return None
            
    except ImportError:
        print("❌ Playwright not installed. Install with: pip install playwright")
        print("   Then run: playwright install")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def update_api_configs(truthfinder_session=None, manta_session=None):
    """Update API configurations with fresh session data."""
    print("\n🔄 Updating API Configurations")
    print("=" * 40)
    
    if truthfinder_session:
        print("📝 Updating TruthFinder API config...")
        # Update TruthFinder API configuration
        cookies = truthfinder_session.get('cookies', {})
        
        # Create updated config snippet
        truthfinder_config = {
            'api_key': cookies.get('api-key', 'B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98'),
            'cookies': {
                'sessionId': cookies.get('sessionId', ''),
                'device-id': cookies.get('device-id', ''),
                '__cf_bm': cookies.get('__cf_bm', ''),
                'sessionCreated': cookies.get('sessionCreated', ''),
                'utm_source': cookies.get('utm_source', 'VBDA'),
                'utm_medium': cookies.get('utm_medium', 'affiliate'),
                'utm_campaign': cookies.get('utm_campaign', 'truepeoplesearch')
            },
            'headers': {
                'User-Agent': truthfinder_session.get('user_agent', ''),
                'Referer': 'https://www.truthfinder.com/search/',
                'Origin': 'https://www.truthfinder.com'
            }
        }
        
        with open('sessions/truthfinder_update_config.json', 'w') as f:
            json.dump(truthfinder_config, f, indent=2)
        
        print("✅ TruthFinder config ready for update")
    
    if manta_session:
        print("📝 Updating Manta API config...")
        # Update Manta API configuration
        cookies = manta_session.get('cookies', {})
        
        manta_config = {
            'cookies': {
                'cf_clearance': cookies.get('cf_clearance', ''),
                'pwBotScore': cookies.get('pwBotScore', '99'),
                'cust_id': cookies.get('cust_id', ''),
                '_ga': cookies.get('_ga', ''),
                '_gcl_au': cookies.get('_gcl_au', ''),
                'refer_id': cookies.get('refer_id', '0000'),
                'sess_refer': cookies.get('sess_refer', '1')
            },
            'headers': {
                'User-Agent': manta_session.get('user_agent', ''),
                'Accept': '*/*',
                'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
        }
        
        with open('sessions/manta_update_config.json', 'w') as f:
            json.dump(manta_config, f, indent=2)
        
        print("✅ Manta config ready for update")

def show_usage():
    """Show usage instructions."""
    print("🤖 Session Extraction Tool")
    print("=" * 30)
    print("Usage:")
    print("  python extract_sessions.py truthfinder  # Extract TruthFinder session")
    print("  python extract_sessions.py manta        # Extract Manta session")
    print("  python extract_sessions.py both         # Extract both sessions")
    print()
    print("Requirements:")
    print("  pip install playwright")
    print("  playwright install")
    print()
    print("This tool will:")
    print("  1. Open a real browser (Chrome)")
    print("  2. Navigate to the target website")
    print("  3. Perform realistic user actions")
    print("  4. Capture all cookies and API requests")
    print("  5. Generate configuration files for API updates")

async def main():
    """Main function."""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    truthfinder_session = None
    manta_session = None
    
    if command in ['truthfinder', 'both']:
        truthfinder_session = await extract_truthfinder_session()
    
    if command in ['manta', 'both']:
        manta_session = await extract_manta_session()
    
    # Update configurations
    if truthfinder_session or manta_session:
        await update_api_configs(truthfinder_session, manta_session)
        
        print(f"\n🎉 Session extraction completed!")
        print(f"📂 Check the 'sessions/' directory for all generated files")
        print(f"💡 Use the generated configs to update your API integrations")

if __name__ == '__main__':
    asyncio.run(main())
