#!/usr/bin/env python3
"""
Test Manta Browser Scraper with Multiple Business Types
This tests the automated cookie refresh pipeline across various business categories.
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from scrapers.manta_browser_scraper import MantaBrowserScraper

class MockEngine:
    """Mock engine for testing."""
    def __init__(self):
        self.config = {
            'sources': {
                'manta_browser': {
                    'enabled': True,
                    'max_pages': 2,  # Test multiple pages
                    'headless': True
                }
            }
        }

def test_business_variety():
    """Test Manta scraper with multiple business types and locations."""
    print("🏢 Testing Manta Browser Scraper - Business Variety Test")
    print("=" * 70)
    print("This comprehensive test will:")
    print("1. 🔄 Test automated cookie refresh for each business type")
    print("2. 📊 Test category mapping for different industries")
    print("3. 🌍 Test multiple locations")
    print("4. 📈 Measure success rates and response times")
    print("5. 🛡️  Test Cloudflare bypass effectiveness")
    print()
    
    # Test cases: (business_type, location, expected_category)
    test_cases = [
        ("restaurant", "houston tx", "54_C4_000"),
        ("construction", "dallas tx", "54_B1_000"),
        ("plumbing", "austin tx", "54_B1_KCQ"),
        ("dental", "san antonio tx", "54_D0_KCQ"),
        ("automotive", "fort worth tx", "54_A1_000"),
        ("legal", "el paso tx", "54_A6_KCQ"),
        ("real estate", "arlington tx", "54_A6_KCQ"),
        ("retail", "corpus christi tx", "54_C0_000"),
        ("lawn care", "plano tx", "54_B3_KCQ"),
        ("accounting", "garland tx", "54_A6_000")
    ]
    
    # Initialize scraper
    engine = MockEngine()
    scraper = MantaBrowserScraper(engine)
    
    print(f"✅ Scraper initialized with {scraper.max_cookie_refreshes} max cookie refreshes")
    print()
    
    results = []
    total_start_time = time.time()
    
    for i, (business_type, location, expected_category) in enumerate(test_cases, 1):
        print(f"🔍 Test {i}/{len(test_cases)}: {business_type.upper()} in {location.upper()}")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            # Test category mapping
            category_id = scraper._get_category_id(business_type)
            category_match = "✅" if category_id == expected_category else "❌"
            print(f"   📂 Category mapping: {category_match} {business_type} -> {category_id}")
            
            # Reset cookie refresh count for each test
            scraper.cookie_refresh_count = 0
            scraper.fresh_cookies = None
            
            # Test the search method (this will trigger cookie refresh if needed)
            print(f"   🔄 Testing search with automated cookie refresh...")
            
            # Run the actual search
            search_results = scraper.search(business_type, location)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Analyze results
            result_count = len(search_results) if search_results else 0
            cookie_refreshes = scraper.cookie_refresh_count
            
            print(f"   📊 Results: {result_count} businesses found")
            print(f"   🔄 Cookie refreshes used: {cookie_refreshes}/{scraper.max_cookie_refreshes}")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
            
            # Determine success status
            if result_count > 0:
                status = "✅ SUCCESS"
                success = True
            elif cookie_refreshes > 0:
                status = "🔄 ATTEMPTED (Cloudflare blocked)"
                success = False
            else:
                status = "❌ FAILED"
                success = False
            
            print(f"   🎯 Status: {status}")
            
            # Store results
            results.append({
                'business_type': business_type,
                'location': location,
                'category_id': category_id,
                'expected_category': expected_category,
                'category_correct': category_id == expected_category,
                'result_count': result_count,
                'cookie_refreshes': cookie_refreshes,
                'duration': duration,
                'success': success,
                'status': status
            })
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"   ❌ ERROR: {str(e)[:100]}...")
            print(f"   ⏱️  Duration: {duration:.2f} seconds")
            
            results.append({
                'business_type': business_type,
                'location': location,
                'category_id': 'ERROR',
                'expected_category': expected_category,
                'category_correct': False,
                'result_count': 0,
                'cookie_refreshes': 0,
                'duration': duration,
                'success': False,
                'status': f"❌ ERROR: {str(e)[:50]}..."
            })
        
        print()
        
        # Small delay between tests to be respectful
        if i < len(test_cases):
            print("   ⏳ Waiting 3 seconds before next test...")
            time.sleep(3)
            print()
    
    total_duration = time.time() - total_start_time
    
    # Generate comprehensive report
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    
    # Summary statistics
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    category_correct = sum(1 for r in results if r['category_correct'])
    total_results = sum(r['result_count'] for r in results)
    total_refreshes = sum(r['cookie_refreshes'] for r in results)
    avg_duration = sum(r['duration'] for r in results) / total_tests
    
    print(f"📈 SUMMARY STATISTICS:")
    print(f"   Total tests: {total_tests}")
    print(f"   Successful searches: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   Correct category mapping: {category_correct}/{total_tests} ({category_correct/total_tests*100:.1f}%)")
    print(f"   Total businesses found: {total_results}")
    print(f"   Total cookie refreshes: {total_refreshes}")
    print(f"   Average test duration: {avg_duration:.2f} seconds")
    print(f"   Total test duration: {total_duration:.2f} seconds")
    print()
    
    # Detailed results table
    print(f"📋 DETAILED RESULTS:")
    print(f"{'Business Type':<15} {'Location':<15} {'Category':<12} {'Results':<8} {'Refreshes':<10} {'Status':<25}")
    print("-" * 85)
    
    for r in results:
        category_status = "✅" if r['category_correct'] else "❌"
        print(f"{r['business_type']:<15} {r['location']:<15} {category_status}{r['category_id']:<11} {r['result_count']:<8} {r['cookie_refreshes']:<10} {r['status']:<25}")
    
    print()
    
    # Cookie refresh analysis
    print(f"🔄 COOKIE REFRESH ANALYSIS:")
    refresh_usage = {}
    for r in results:
        refreshes = r['cookie_refreshes']
        refresh_usage[refreshes] = refresh_usage.get(refreshes, 0) + 1
    
    for refreshes, count in sorted(refresh_usage.items()):
        percentage = count / total_tests * 100
        print(f"   {refreshes} refreshes: {count} tests ({percentage:.1f}%)")
    
    print()
    
    # Business type performance
    print(f"🏢 BUSINESS TYPE PERFORMANCE:")
    business_performance = {}
    for r in results:
        bt = r['business_type']
        if bt not in business_performance:
            business_performance[bt] = {'total': 0, 'success': 0, 'results': 0}
        business_performance[bt]['total'] += 1
        if r['success']:
            business_performance[bt]['success'] += 1
        business_performance[bt]['results'] += r['result_count']
    
    for business_type, perf in sorted(business_performance.items()):
        success_rate = perf['success'] / perf['total'] * 100 if perf['total'] > 0 else 0
        print(f"   {business_type:<15}: {perf['success']}/{perf['total']} success ({success_rate:.1f}%), {perf['results']} results")
    
    print()
    
    # Final assessment
    print(f"🎯 FINAL ASSESSMENT:")
    
    if successful_tests > 0:
        print(f"✅ PIPELINE STATUS: WORKING")
        print(f"💡 The automated cookie refresh pipeline successfully handled {successful_tests} business types")
        print(f"🔄 Cookie refresh system activated {total_refreshes} times across all tests")
        print(f"📊 Found {total_results} total business records")
        
        if successful_tests >= total_tests * 0.7:  # 70% success rate
            print(f"🚀 PRODUCTION READY: High success rate ({successful_tests/total_tests*100:.1f}%)")
        else:
            print(f"⚠️  NEEDS OPTIMIZATION: Moderate success rate ({successful_tests/total_tests*100:.1f}%)")
            
    else:
        print(f"❌ PIPELINE STATUS: BLOCKED")
        print(f"💡 Manta's anti-bot protection is currently blocking all requests")
        print(f"🔄 Cookie refresh system attempted {total_refreshes} refreshes but couldn't bypass protection")
        print(f"🛡️  This indicates very aggressive Cloudflare protection")
    
    print()
    print(f"📝 RECOMMENDATIONS:")
    
    if category_correct == total_tests:
        print(f"✅ Category mapping is perfect - no changes needed")
    else:
        print(f"⚠️  Category mapping needs attention for some business types")
    
    if total_refreshes > 0:
        print(f"✅ Cookie refresh system is working - activated {total_refreshes} times")
    else:
        print(f"💡 Cookie refresh system wasn't needed - either working perfectly or not triggering")
    
    if avg_duration > 30:
        print(f"⚠️  Average test duration is high ({avg_duration:.1f}s) - consider optimization")
    else:
        print(f"✅ Test performance is good (avg {avg_duration:.1f}s per test)")
    
    return results

def main():
    """Main test function."""
    print("🤖 Manta Business Variety Test")
    print("Testing automated cookie refresh across multiple business types and locations")
    print()
    
    results = test_business_variety()
    
    # Save results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"manta_variety_test_results_{timestamp}.txt"
    
    with open(filename, 'w') as f:
        f.write("Manta Business Variety Test Results\n")
        f.write(f"Generated: {datetime.now()}\n")
        f.write("=" * 50 + "\n\n")
        
        for r in results:
            f.write(f"Business Type: {r['business_type']}\n")
            f.write(f"Location: {r['location']}\n")
            f.write(f"Category ID: {r['category_id']}\n")
            f.write(f"Results Found: {r['result_count']}\n")
            f.write(f"Cookie Refreshes: {r['cookie_refreshes']}\n")
            f.write(f"Duration: {r['duration']:.2f}s\n")
            f.write(f"Status: {r['status']}\n")
            f.write("-" * 30 + "\n")
    
    print(f"📁 Detailed results saved to: {filename}")
    print()
    print("🎉 Business variety test complete!")

if __name__ == '__main__':
    main()
