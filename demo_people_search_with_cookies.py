#!/usr/bin/env python3
"""
Demo script showing how to use the people search with the existing cookie system.

This script demonstrates the complete workflow:
1. Check for existing cookies
2. Refresh cookies if needed
3. Perform people search with fresh session data
4. Show results
"""

import sys
import os
import asyncio
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def demo_people_search_workflow():
    """Demonstrate the complete people search workflow with cookie management."""
    print("🚀 People Search with Cookie Management Demo")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    try:
        from main import BusinessOwnerScraper
        from src.people_search import PersonSearchQuery
        
        # Step 1: Initialize the scraper
        print("📋 Step 1: Initializing Business Owner Scraper...")
        scraper = BusinessOwnerScraper("config.yaml")
        print("✅ Scraper initialized with people search capabilities")
        
        # Step 2: Check session data availability
        print("\n📋 Step 2: Checking session data availability...")
        truthfinder_scraper = scraper.people_search_engine.people_scrapers.get('truthfinder_browser')
        
        if truthfinder_scraper:
            session_data = truthfinder_scraper._load_fresh_session_data()
            
            if session_data:
                cookies_count = len(session_data.get('cookies', {}))
                print(f"✅ Fresh session data found with {cookies_count} cookies")
                
                # Show some key cookies
                cookies = session_data.get('cookies', {})
                key_cookies = ['sessionId', 'device-id', '__cf_bm', 'cf_clearance']
                found_key_cookies = [name for name in key_cookies if name in cookies]
                print(f"   🔑 Key cookies available: {', '.join(found_key_cookies)}")
                
                if 'user_agent' in session_data:
                    ua = session_data['user_agent']
                    print(f"   🌐 User agent: {ua[:50]}...")
            else:
                print("⚠️  No fresh session data found")
                print("💡 Recommendation: Run 'python get_fresh_cookies.py truthfinder --automated'")
                
                # Try to refresh cookies automatically
                print("\n📋 Attempting automatic cookie refresh...")
                if truthfinder_scraper._refresh_truthfinder_cookies():
                    print("✅ Cookies refreshed successfully")
                    session_data = truthfinder_scraper._load_fresh_session_data()
                    if session_data:
                        print(f"✅ Fresh session data now available with {len(session_data.get('cookies', {}))} cookies")
                else:
                    print("⚠️  Automatic cookie refresh failed")
                    print("💡 Will use fallback cookies for demo")
        
        # Step 3: Perform people search
        print("\n📋 Step 3: Performing people search...")
        print("🔍 Searching for: John Smith in Houston, TX")
        
        # Use the main scraper's people search method
        results = scraper.search_person(
            first_name="John",
            last_name="Smith",
            city="Houston",
            state="TX",
            sources=['truthfinder_browser']  # Use only TruthFinder for this demo
        )
        
        print(f"✅ Search completed, found {len(results)} results")
        
        # Step 4: Display results
        if results:
            print("\n📋 Step 4: Displaying results...")
            
            for i, result in enumerate(results[:3], 1):  # Show first 3 results
                print(f"\n📊 Result {i}:")
                print(f"   👤 Name: {result.owner_name}")
                print(f"   📍 Location: {result.location or 'N/A'}")
                print(f"   📞 Phone: {result.phone or 'N/A'}")
                print(f"   📧 Email: {result.email or 'N/A'}")
                print(f"   🎂 Age: {result.owner_age or 'N/A'}")
                print(f"   🏠 Address: {result.address or 'N/A'}")
                print(f"   🔍 Source: {result.source}")
                print(f"   ⭐ Confidence: {result.match_confidence or 'N/A'}")
                print(f"   📊 Data Quality: {result.data_quality or 'N/A'}")
                
                # Show aliases if available
                if result.aliases:
                    print(f"   🏷️  Aliases: {', '.join(result.aliases[:3])}")
                
                # Show family members if available
                if result.family_members:
                    print(f"   👨‍👩‍👧‍👦 Family: {len(result.family_members)} members")
                    for member in result.family_members[:2]:  # Show first 2
                        name = member.get('name', 'N/A')
                        relationship = member.get('relationship', 'N/A')
                        print(f"      - {name} ({relationship})")
                
                # Show additional phone numbers
                if result.phone_numbers and len(result.phone_numbers) > 1:
                    print(f"   📱 Additional phones:")
                    for phone in result.phone_numbers[1:3]:  # Show 2 additional
                        number = phone.get('number', 'N/A')
                        phone_type = phone.get('type', 'N/A')
                        print(f"      - {number} ({phone_type})")
                
                # Show additional emails
                if result.email_addresses and len(result.email_addresses) > 1:
                    print(f"   📧 Additional emails:")
                    for email in result.email_addresses[1:3]:  # Show 2 additional
                        address = email.get('email', 'N/A')
                        email_type = email.get('type', 'N/A')
                        print(f"      - {address} ({email_type})")
        else:
            print("\n📋 Step 4: No results found")
            print("💡 This could be due to:")
            print("   - Expired cookies (try refreshing)")
            print("   - API rate limiting")
            print("   - Person not found in database")
            print("   - Network connectivity issues")
        
        # Step 5: Export results (if any)
        if results:
            print("\n📋 Step 5: Exporting results...")
            try:
                output_file = scraper.process_and_export(results, "excel")
                if output_file:
                    print(f"✅ Results exported to: {output_file}")
                else:
                    print("⚠️  Export failed")
            except Exception as e:
                print(f"⚠️  Export error: {e}")
        
        # Clean up
        scraper.cleanup()
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_cookie_management_tips():
    """Show tips for managing cookies effectively."""
    print("\n💡 Cookie Management Tips")
    print("=" * 40)
    
    print("🍪 For best results with TruthFinder API:")
    print("")
    print("1. 🔄 Refresh cookies regularly:")
    print("   python get_fresh_cookies.py truthfinder --automated")
    print("")
    print("2. 🕐 Cookies typically expire after:")
    print("   - Cloudflare cookies (__cf_bm): ~30 minutes")
    print("   - Session cookies: ~24 hours")
    print("   - Device cookies: ~30 days")
    print("")
    print("3. 🚨 Signs you need fresh cookies:")
    print("   - 403 Forbidden responses")
    print("   - Cloudflare challenge pages")
    print("   - Empty API responses")
    print("")
    print("4. 🔧 Automatic refresh triggers:")
    print("   - Before each API call batch")
    print("   - After 403 responses")
    print("   - When session files are old")
    print("")
    print("5. 📁 Session files location:")
    print("   - sessions/truthfinder_cookies_*.json")
    print("   - sessions/truthfinder_session.json")
    print("   - sessions/truthfinder_api_config.json")

def main():
    """Run the people search demo."""
    print("🎬 Starting People Search Demo with Cookie Management")
    print("")
    
    # Check prerequisites
    sessions_dir = os.path.join(os.getcwd(), "sessions")
    if not os.path.exists(sessions_dir):
        print("⚠️  Sessions directory not found. Creating it...")
        os.makedirs(sessions_dir)
    
    get_cookies_script = os.path.join(os.getcwd(), "get_fresh_cookies.py")
    if not os.path.exists(get_cookies_script):
        print("⚠️  get_fresh_cookies.py not found")
        print("💡 Make sure you're in the correct directory")
        return False
    
    # Run the demo
    success = asyncio.run(demo_people_search_workflow())
    
    # Show tips regardless of success
    show_cookie_management_tips()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Demo completed successfully!")
        print("")
        print("✅ The people search system is working with:")
        print("- Fresh cookie management")
        print("- Automatic session refresh")
        print("- Proper API authentication")
        print("- Comprehensive data extraction")
        print("")
        print("🚀 Ready for production use!")
    else:
        print("⚠️  Demo encountered issues")
        print("")
        print("🔧 Troubleshooting steps:")
        print("1. Refresh cookies: python get_fresh_cookies.py truthfinder")
        print("2. Check internet connection")
        print("3. Verify TruthFinder API is accessible")
        print("4. Check session files in sessions/ directory")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
