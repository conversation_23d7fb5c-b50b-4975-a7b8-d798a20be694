#!/usr/bin/env python3
"""
Test script for the new people search functionality.

This script validates that the people search system is working correctly
and integrates properly with the existing business search system.
"""

import sys
import os
import logging
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_people_search_imports():
    """Test that all people search modules can be imported."""
    print("🧪 Testing People Search Imports...")
    
    try:
        from src.people_search import PersonSearchEngine, PersonSearchQuery
        print("   ✅ PersonSearchEngine and PersonSearchQuery imported successfully")
        
        from src.core import ScrapingResult
        print("   ✅ Enhanced ScrapingResult imported successfully")
        
        from src.utils.data_processor import DataProcessor
        print("   ✅ Enhanced DataProcessor imported successfully")
        
        from main import BusinessOwnerScraper
        print("   ✅ Enhanced BusinessOwnerScraper imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_scraping_result_enhancements():
    """Test the enhanced ScrapingResult data structure."""
    print("\n🧪 Testing Enhanced ScrapingResult...")
    
    try:
        from src.core import ScrapingResult
        
        # Create a test result with people search fields
        result = ScrapingResult(
            owner_name="<PERSON>",
            business_name="Smith Construction",
            location="Houston, TX",
            source="test",
            
            # People-specific fields
            aliases=["Johnny Smith", "J. Smith"],
            middle_name="Michael",
            owner_age="45",
            family_members=[
                {"name": "Jane Smith", "relationship": "spouse", "age": "42"},
                {"name": "John Smith Jr", "relationship": "son", "age": "18"}
            ],
            phone_numbers=[
                {"number": "(*************", "type": "mobile", "carrier": "Verizon"},
                {"number": "(*************", "type": "home", "carrier": "AT&T"}
            ],
            email_addresses=[
                {"email": "<EMAIL>", "type": "business", "verified": True},
                {"email": "<EMAIL>", "type": "personal", "verified": False}
            ],
            address_history=[
                {"address": "123 Old St, Houston, TX", "start_date": "2010", "end_date": "2020", "type": "previous"},
                {"address": "456 New Ave, Houston, TX", "start_date": "2020", "end_date": "", "type": "current"}
            ],
            search_type="people",
            search_query="John Smith Houston TX",
            match_confidence=0.9,
            data_completeness=0.8
        )
        
        print("   ✅ ScrapingResult created with people search fields")
        print(f"   ✅ Owner name: {result.owner_name}")
        print(f"   ✅ Aliases: {result.aliases}")
        print(f"   ✅ Family members: {len(result.family_members)}")
        print(f"   ✅ Phone numbers: {len(result.phone_numbers)}")
        print(f"   ✅ Email addresses: {len(result.email_addresses)}")
        print(f"   ✅ Address history: {len(result.address_history)}")
        print(f"   ✅ Search type: {result.search_type}")
        print(f"   ✅ Match confidence: {result.match_confidence}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ ScrapingResult test failed: {e}")
        return False

def test_person_search_query():
    """Test the PersonSearchQuery data structure."""
    print("\n🧪 Testing PersonSearchQuery...")
    
    try:
        from src.people_search import PersonSearchQuery
        
        # Create a test query
        query = PersonSearchQuery(
            first_name="John",
            last_name="Smith",
            middle_name="Michael",
            address="123 Main St",
            city="Houston",
            state="TX",
            zip_code="77001",
            age_range=(40, 50),
            phone="(*************",
            email="<EMAIL>"
        )
        
        print("   ✅ PersonSearchQuery created successfully")
        print(f"   ✅ Full name: {query.first_name} {query.middle_name} {query.last_name}")
        print(f"   ✅ Location: {query.city}, {query.state} {query.zip_code}")
        print(f"   ✅ Age range: {query.age_range}")
        print(f"   ✅ Contact: {query.phone}, {query.email}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ PersonSearchQuery test failed: {e}")
        return False

def test_person_search_engine_initialization():
    """Test PersonSearchEngine initialization."""
    print("\n🧪 Testing PersonSearchEngine Initialization...")
    
    try:
        from src.people_search import PersonSearchEngine
        
        # Initialize the engine
        engine = PersonSearchEngine("config.yaml")
        
        print("   ✅ PersonSearchEngine initialized successfully")
        print(f"   ✅ People scrapers available: {list(engine.people_scrapers.keys())}")
        print(f"   ✅ Max results per source: {engine.max_results_per_source}")
        print(f"   ✅ Cross-reference enabled: {engine.enable_cross_reference}")
        
        # Clean up
        engine.cleanup()
        
        return True
        
    except Exception as e:
        print(f"   ❌ PersonSearchEngine initialization failed: {e}")
        return False

def test_business_owner_scraper_integration():
    """Test integration with BusinessOwnerScraper."""
    print("\n🧪 Testing BusinessOwnerScraper Integration...")
    
    try:
        from main import BusinessOwnerScraper
        
        # Initialize the scraper
        scraper = BusinessOwnerScraper("config.yaml")
        
        print("   ✅ BusinessOwnerScraper initialized with people search support")
        
        # Check if people search methods are available
        if hasattr(scraper, 'search_person'):
            print("   ✅ search_person method available")
        else:
            print("   ❌ search_person method not found")
            return False
        
        if hasattr(scraper, 'search_person_with_cross_reference'):
            print("   ✅ search_person_with_cross_reference method available")
        else:
            print("   ❌ search_person_with_cross_reference method not found")
            return False
        
        if hasattr(scraper, 'people_search_engine'):
            print("   ✅ people_search_engine attribute available")
        else:
            print("   ❌ people_search_engine attribute not found")
            return False
        
        # Clean up
        scraper.cleanup()
        
        return True
        
    except Exception as e:
        print(f"   ❌ BusinessOwnerScraper integration test failed: {e}")
        return False

def test_data_processor_enhancements():
    """Test DataProcessor enhancements for people search."""
    print("\n🧪 Testing DataProcessor Enhancements...")
    
    try:
        from src.utils.data_processor import DataProcessor
        from src.core import ScrapingResult
        
        # Initialize processor
        config = {'output': {'deduplication': {'enabled': True}}}
        processor = DataProcessor(config)
        
        print("   ✅ DataProcessor initialized")
        
        # Check if people search methods are available
        if hasattr(processor, 'process_people_search_results'):
            print("   ✅ process_people_search_results method available")
        else:
            print("   ❌ process_people_search_results method not found")
            return False
        
        # Test with sample people results
        sample_results = [
            ScrapingResult(
                owner_name="John Smith",
                phone="(*************",
                email="<EMAIL>",
                address="123 Main St, Houston, TX",
                search_type="people"
            ),
            ScrapingResult(
                owner_name="John Smith",  # Duplicate
                phone="(*************",
                email="<EMAIL>", 
                address="123 Main St, Houston, TX",
                search_type="people"
            )
        ]
        
        # Process results
        processed_results = processor.process_people_search_results(sample_results)
        
        print(f"   ✅ Processed {len(sample_results)} -> {len(processed_results)} results")
        print("   ✅ People search processing completed successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ DataProcessor enhancement test failed: {e}")
        return False

def test_configuration_updates():
    """Test that configuration includes people search settings."""
    print("\n🧪 Testing Configuration Updates...")
    
    try:
        import yaml
        
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Check for people search configuration
        if 'people_search' in config:
            print("   ✅ people_search configuration section found")
            
            people_config = config['people_search']
            
            if 'enabled' in people_config:
                print(f"   ✅ people_search enabled: {people_config['enabled']}")
            
            if 'source_priorities' in people_config:
                print(f"   ✅ source_priorities configured: {list(people_config['source_priorities'].keys())}")
            
            if 'processing' in people_config:
                print("   ✅ people search processing settings configured")
            
            if 'deduplication' in people_config:
                print("   ✅ people search deduplication settings configured")
        else:
            print("   ❌ people_search configuration section not found")
            return False
        
        # Check for people search support in sources
        sources_with_people_search = []
        if 'sources' in config:
            for source_name, source_config in config['sources'].items():
                if source_config.get('supports_people_search', False):
                    sources_with_people_search.append(source_name)
        
        print(f"   ✅ Sources with people search support: {sources_with_people_search}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

def main():
    """Run all people search tests."""
    print("🚀 People Search Functionality Test Suite")
    print("=" * 50)
    
    tests = [
        test_people_search_imports,
        test_scraping_result_enhancements,
        test_person_search_query,
        test_person_search_engine_initialization,
        test_business_owner_scraper_integration,
        test_data_processor_enhancements,
        test_configuration_updates
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"   ❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All people search tests passed! The functionality is ready to use.")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
