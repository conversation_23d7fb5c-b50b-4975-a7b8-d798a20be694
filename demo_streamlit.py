#!/usr/bin/env python3
"""
Business Finder System - Streamlit Demo Script
Demonstrates the web interface functionality with sample data.
"""

import pandas as pd
import json
from datetime import datetime, timedelta
import random
from pathlib import Path

def create_sample_data():
    """Create sample business data for demonstration"""
    
    # Sample business data
    businesses = [
        {
            'business_name': 'Houston BBQ Palace',
            'business_type': 'restaurant',
            'owner_name': '<PERSON>',
            'owner_age': 45,
            'address': '123 Main St, Houston, TX 77001',
            'city': 'Houston',
            'state': 'TX',
            'zip_code': '77001',
            'phone': '(*************',
            'website': 'https://houstonbbqpalace.com',
            'years_in_business': 12,
            'employee_count': 25,
            'source': 'manta_browser',
            'confidence_score': 0.85,
            'data_quality': 'enriched',
            'verification_status': 'api_verified'
        },
        {
            'business_name': 'Tex-Mex Cantina',
            'business_type': 'restaurant',
            'owner_name': '<PERSON>',
            'owner_age': 38,
            'address': '456 Oak Ave, Houston, TX 77002',
            'city': 'Houston',
            'state': 'TX',
            'zip_code': '77002',
            'phone': '(*************',
            'website': 'https://texmexcantina.com',
            'years_in_business': 8,
            'employee_count': 15,
            'source': 'manta_browser+truthfinder_browser',
            'confidence_score': 0.92,
            'data_quality': 'enriched',
            'verification_status': 'cross_verified'
        },
        {
            'business_name': 'Downtown Deli',
            'business_type': 'restaurant',
            'owner_name': 'David Chen',
            'owner_age': 52,
            'address': '789 Commerce St, Houston, TX 77003',
            'city': 'Houston',
            'state': 'TX',
            'zip_code': '77003',
            'phone': '(*************',
            'website': '',
            'years_in_business': 15,
            'employee_count': 8,
            'source': 'truthfinder_browser',
            'confidence_score': 0.78,
            'data_quality': 'standard',
            'verification_status': 'verified'
        },
        {
            'business_name': 'Pizza Corner',
            'business_type': 'restaurant',
            'owner_name': 'Tony Italiano',
            'owner_age': 41,
            'address': '321 Elm St, Houston, TX 77004',
            'city': 'Houston',
            'state': 'TX',
            'zip_code': '77004',
            'phone': '(*************',
            'website': 'https://pizzacorner.com',
            'years_in_business': 6,
            'employee_count': 12,
            'source': 'manta_browser',
            'confidence_score': 0.88,
            'data_quality': 'enriched',
            'verification_status': 'api_verified'
        },
        {
            'business_name': 'Seafood Shack',
            'business_type': 'restaurant',
            'owner_name': 'Sarah Johnson',
            'owner_age': 35,
            'address': '654 Bay St, Houston, TX 77005',
            'city': 'Houston',
            'state': 'TX',
            'zip_code': '77005',
            'phone': '(*************',
            'website': 'https://seafoodshack.com',
            'years_in_business': 4,
            'employee_count': 18,
            'source': 'manta_browser+truthfinder_browser',
            'confidence_score': 0.91,
            'data_quality': 'enriched',
            'verification_status': 'cross_verified'
        }
    ]
    
    # Generate additional sample data
    for i in range(20):
        business = {
            'business_name': f'Sample Restaurant {i+6}',
            'business_type': 'restaurant',
            'owner_name': f'Owner {i+6}',
            'owner_age': random.randint(25, 65),
            'address': f'{random.randint(100, 9999)} Sample St, Houston, TX 7700{random.randint(1, 9)}',
            'city': 'Houston',
            'state': 'TX',
            'zip_code': f'7700{random.randint(1, 9)}',
            'phone': f'(713) 555-{random.randint(1000, 9999)}',
            'website': f'https://sample{i+6}.com' if random.choice([True, False]) else '',
            'years_in_business': random.randint(1, 20),
            'employee_count': random.randint(3, 50),
            'source': random.choice(['manta_browser', 'truthfinder_browser', 'manta_browser+truthfinder_browser']),
            'confidence_score': round(random.uniform(0.6, 0.95), 2),
            'data_quality': random.choice(['standard', 'enriched', 'enriched']),
            'verification_status': random.choice(['verified', 'api_verified', 'cross_verified'])
        }
        businesses.append(business)
    
    return pd.DataFrame(businesses)

def create_sample_files():
    """Create sample output files for demonstration"""
    
    # Create results directory
    results_dir = Path('results')
    results_dir.mkdir(exist_ok=True)
    
    # Create sample data
    df = create_sample_data()
    
    # Save as Excel
    excel_file = results_dir / f'demo_business_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    df.to_excel(excel_file, index=False)
    
    # Save as CSV
    csv_file = results_dir / f'demo_business_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    df.to_csv(csv_file, index=False)
    
    print(f"✅ Created sample files:")
    print(f"   - {excel_file}")
    print(f"   - {csv_file}")
    
    return excel_file, csv_file

def create_sample_search_history():
    """Create sample search history data"""
    
    history = []
    
    # Generate sample searches over the past month
    for i in range(15):
        days_ago = random.randint(0, 30)
        timestamp = datetime.now() - timedelta(days=days_ago)
        
        business_types = ['restaurant', 'construction', 'law firm', 'medical practice', 'retail store']
        locations = ['houston tx', 'dallas tx', 'austin tx', 'san antonio tx', 'miami fl']
        
        history.append({
            'timestamp': timestamp,
            'business_type': random.choice(business_types),
            'location': random.choice(locations),
            'sources': random.choice([
                ['manta_browser'],
                ['truthfinder_browser'],
                ['manta_browser', 'truthfinder_browser']
            ]),
            'results_count': random.randint(50, 200)
        })
    
    return history

def setup_demo_environment():
    """Setup demonstration environment"""
    
    print("🎬 Setting up Business Finder System Demo Environment")
    print("=" * 55)
    
    # Create sample data files
    print("\n📁 Creating sample data files...")
    excel_file, csv_file = create_sample_files()
    
    # Create sample configuration
    print("\n⚙️ Creating demo configuration...")
    
    demo_config = {
        'demo_mode': True,
        'sample_data': {
            'excel_file': str(excel_file),
            'csv_file': str(csv_file),
            'search_history': create_sample_search_history()
        },
        'system_stats': {
            'total_searches': 15,
            'total_results': 1847,
            'total_cost': 0.045,
            'avg_processing_time': 142.5
        }
    }
    
    # Save demo configuration
    demo_config_file = Path('demo_config.json')
    with open(demo_config_file, 'w') as f:
        json.dump(demo_config, f, indent=2, default=str)
    
    print(f"   - {demo_config_file}")
    
    print("\n✅ Demo environment setup complete!")
    print("\n🚀 To start the demo:")
    print("   python run_streamlit.py")
    print("\n💡 Demo features:")
    print("   - Sample business data (25 restaurants)")
    print("   - Mock search history (15 searches)")
    print("   - Simulated system statistics")
    print("   - All web interface functionality")

def load_demo_data():
    """Load demo data for Streamlit application"""
    
    demo_config_file = Path('demo_config.json')
    if not demo_config_file.exists():
        return None
    
    try:
        with open(demo_config_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading demo config: {e}")
        return None

def is_demo_mode():
    """Check if running in demo mode"""
    demo_config = load_demo_data()
    return demo_config and demo_config.get('demo_mode', False)

if __name__ == "__main__":
    setup_demo_environment()
