#!/usr/bin/env python3
"""
Setup Script for Session Extraction
Installs Playwright and sets up the session extraction environment.
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def main():
    """Main setup function."""
    print("🚀 Session Extraction Setup")
    print("=" * 40)
    print("This will install Playwright and set up session extraction tools.")
    print()
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required for Playwright")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Install Playwright
    if not run_command("pip install playwright", "Installing Playwright"):
        print("💡 Try: python -m pip install playwright")
        return False
    
    # Install Playwright browsers
    if not run_command("playwright install", "Installing Playwright browsers"):
        print("💡 Try: python -m playwright install")
        return False
    
    # Install additional requirements
    if os.path.exists("requirements_session.txt"):
        if not run_command("pip install -r requirements_session.txt", "Installing additional requirements"):
            print("💡 Some packages may have failed to install, but Playwright should work")
    
    # Create sessions directory
    os.makedirs("sessions", exist_ok=True)
    print("✅ Created sessions directory")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Extract TruthFinder session:")
    print("   python extract_sessions.py truthfinder")
    print()
    print("2. Extract Manta session:")
    print("   python extract_sessions.py manta")
    print()
    print("3. Extract both sessions:")
    print("   python extract_sessions.py both")
    print()
    print("💡 The browser will open and perform real user actions to capture fresh cookies and headers.")

if __name__ == '__main__':
    main()
