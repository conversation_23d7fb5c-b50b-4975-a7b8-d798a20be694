#!/usr/bin/env python3
"""
Business Finder System - Streamlit Application Launcher
Handles setup, configuration, and launching of the web interface.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    required_packages = [
        'streamlit', 'pandas', 'plotly', 'openpyxl', 
        'pyyaml', 'requests', 'beautifulsoup4'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements_streamlit.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_system_files():
    """Check if required system files exist"""
    required_files = [
        'main.py',
        'config.yaml',
        'src/utils/data_processor.py',
        'src/models/scraping_result.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required system files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ All required system files are present")
    return True

def setup_environment():
    """Setup environment variables and configuration"""
    
    # Set Streamlit configuration
    os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
    os.environ['STREAMLIT_SERVER_ENABLE_CORS'] = 'false'
    os.environ['STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION'] = 'false'
    
    # Create results directory if it doesn't exist
    results_dir = Path('results')
    results_dir.mkdir(exist_ok=True)
    
    print("✅ Environment configured")

def launch_streamlit(port=8501, host='localhost'):
    """Launch the Streamlit application"""
    
    print(f"🚀 Launching Business Finder System Web Interface...")
    print(f"   URL: http://{host}:{port}")
    print(f"   Press Ctrl+C to stop the server")
    
    # Launch Streamlit
    cmd = [
        'streamlit', 'run', 'streamlit_app.py',
        '--server.port', str(port),
        '--server.address', host,
        '--server.headless', 'true',
        '--browser.gatherUsageStats', 'false'
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Shutting down Business Finder System...")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to launch Streamlit: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(description='Launch Business Finder System Web Interface')
    parser.add_argument('--port', type=int, default=8501, help='Port to run the server on')
    parser.add_argument('--host', default='localhost', help='Host to bind the server to')
    parser.add_argument('--skip-checks', action='store_true', help='Skip system checks')
    
    args = parser.parse_args()
    
    print("🔍 Business Finder System - Web Interface Launcher")
    print("=" * 50)
    
    if not args.skip_checks:
        print("\n📋 Running system checks...")
        
        # Check requirements
        if not check_requirements():
            sys.exit(1)
        
        # Check system files
        if not check_system_files():
            sys.exit(1)
        
        print("✅ All system checks passed!")
    
    # Setup environment
    print("\n⚙️ Setting up environment...")
    setup_environment()
    
    # Launch application
    print("\n🚀 Starting web interface...")
    success = launch_streamlit(args.port, args.host)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
