#!/usr/bin/env python3
"""
TruthFinder Browser API - Makes API calls within real browser context to bypass Cloudflare.
This is the ONLY method that reliably works with TruthFinder's anti-bot protection.
"""

import asyncio
import json
import sys
from datetime import datetime
from playwright.async_api import async_playwright

async def search_truthfinder_in_browser(first_name: str, last_name: str, city: str = "", state: str = ""):
    """
    Search TruthFinder API using real browser context.
    
    Args:
        first_name: First name to search
        last_name: Last name to search
        city: City (optional)
        state: State (optional)
        
    Returns:
        API response data or None if failed
    """
    print(f"🔍 Searching TruthFinder for: {first_name} {last_name}")
    if city or state:
        print(f"📍 Location: {city}, {state}")
    
    async with async_playwright() as p:
        # Launch browser (set headless=False to see what's happening)
        browser = await p.chromium.launch(
            headless=False,  # Set to True for production
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage'
            ]
        )
        
        # Create realistic browser context
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='en-US',
            timezone_id='America/New_York'
        )
        
        # Add stealth script
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        page = await context.new_page()
        
        try:
            # Step 1: Navigate to TruthFinder to establish session
            print("🌐 Navigating to TruthFinder...")
            await page.goto("https://www.truthfinder.com", wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # Step 2: Handle any modals/popups
            print("✅ Handling popups...")
            try:
                # Look for and click "I AGREE" button
                agree_button = await page.query_selector('button:has-text("I AGREE"), button:has-text("I Agree"), button.green')
                if agree_button:
                    await agree_button.click()
                    print("✅ Accepted terms")
                    await page.wait_for_timeout(2000)
            except:
                pass
            
            # Step 3: Make the API call within browser context
            print("📡 Making API call within browser context...")
            
            # Build API URL
            api_url = "https://api2.truthfinder.com/v1/people/"
            params = {
                'firstName': first_name,
                'lastName': last_name,
                'fields': 'names,locations,related_persons,phones,emails,addresses,demographics'
            }
            
            if city:
                params['city'] = city
            if state:
                params['state'] = state
            
            # Convert params to URL string
            param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            full_url = f"{api_url}?{param_string}"
            
            # Make the API call using browser's fetch API
            api_response = await page.evaluate(f"""
                async () => {{
                    try {{
                        const response = await fetch('{full_url}', {{
                            method: 'GET',
                            headers: {{
                                'accept': '*/*',
                                'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8,hi;q=0.7',
                                'api-key': 'B7QbTIt3PtAID67cRtfQwrgzL0H3qU5buaxp17PoZ98',
                                'app-id': 'tf-web',
                                'cache-control': 'no-cache',
                                'origin': 'https://www.truthfinder.com',
                                'referer': 'https://www.truthfinder.com/search/'
                            }}
                        }});
                        
                        if (response.ok) {{
                            const data = await response.json();
                            return {{ success: true, data: data, status: response.status }};
                        }} else {{
                            const text = await response.text();
                            return {{ success: false, error: text, status: response.status }};
                        }}
                    }} catch (error) {{
                        return {{ success: false, error: error.message, status: 0 }};
                    }}
                }}
            """)
            
            await browser.close()
            
            if api_response['success']:
                print(f"✅ API call successful! Status: {api_response['status']}")
                return api_response['data']
            else:
                print(f"❌ API call failed! Status: {api_response['status']}")
                print(f"Error: {api_response['error'][:200]}...")
                return None
                
        except Exception as e:
            print(f"❌ Browser API call failed: {e}")
            await browser.close()
            return None

def format_truthfinder_results(data):
    """Format TruthFinder API results for display."""
    if not data:
        return "No results found"
    
    results = []
    
    for person in data:
        result = {
            'person_info': {},
            'names': [],
            'locations': [],
            'related_persons': []
        }
        
        # Extract names
        if 'names' in person:
            for name in person['names']:
                name_info = {
                    'first': name.get('first', ''),
                    'middle': name.get('middle', ''),
                    'last': name.get('last', ''),
                    'full_name': f"{name.get('first', '')} {name.get('middle', '')} {name.get('last', '')}".strip()
                }
                result['names'].append(name_info)
        
        # Extract locations
        if 'locations' in person:
            for location in person['locations']:
                if 'address' in location:
                    addr = location['address']
                    location_info = {
                        'city': addr.get('city', ''),
                        'state': addr.get('state', ''),
                        'state_code': addr.get('state_code', ''),
                        'country': addr.get('country', ''),
                        'street': addr.get('street', ''),
                        'zip_code': addr.get('zip_code', ''),
                        'full_address': f"{addr.get('street', '')} {addr.get('city', '')}, {addr.get('state_code', '')} {addr.get('zip_code', '')}".strip()
                    }
                    result['locations'].append(location_info)
        
        # Extract related persons
        if 'related_persons' in person:
            for related in person['related_persons']:
                related_info = {
                    'relationship': related.get('sub_type', ''),
                    'names': [],
                    'ages': []
                }
                
                if 'names' in related:
                    for name in related['names']:
                        related_info['names'].append(f"{name.get('first', '')} {name.get('last', '')}".strip())
                
                if 'dobs' in related:
                    for dob in related['dobs']:
                        related_info['ages'].append(dob.get('age', ''))
                
                result['related_persons'].append(related_info)
        
        results.append(result)
    
    return results

async def main():
    """Main function for testing."""
    if len(sys.argv) < 3:
        print("🤖 TruthFinder Browser API")
        print("=" * 30)
        print("Usage:")
        print("  python truthfinder_browser_api.py <first_name> <last_name> [city] [state]")
        print()
        print("Examples:")
        print("  python truthfinder_browser_api.py John Smith")
        print("  python truthfinder_browser_api.py John Smith Houston TX")
        print("  python truthfinder_browser_api.py Saad Momin")
        return
    
    first_name = sys.argv[1]
    last_name = sys.argv[2]
    city = sys.argv[3] if len(sys.argv) > 3 else ""
    state = sys.argv[4] if len(sys.argv) > 4 else ""
    
    # Search TruthFinder
    data = await search_truthfinder_in_browser(first_name, last_name, city, state)
    
    if data:
        # Format and display results
        results = format_truthfinder_results(data)
        
        print(f"\n🎯 TRUTHFINDER RESULTS")
        print("=" * 40)
        print(f"📊 Found {len(results)} person(s)")
        
        for i, result in enumerate(results, 1):
            print(f"\n👤 Person {i}:")
            
            # Names
            if result['names']:
                print(f"   Names:")
                for name in result['names']:
                    print(f"      {name['full_name']}")
            
            # Locations
            if result['locations']:
                print(f"   Locations:")
                for location in result['locations']:
                    if location['full_address'].strip():
                        print(f"      {location['full_address']}")
                    else:
                        print(f"      {location['city']}, {location['state_code']}")
            
            # Related persons
            if result['related_persons']:
                print(f"   Related Persons:")
                for related in result['related_persons']:
                    relationship = related['relationship']
                    names = ', '.join(related['names'])
                    ages = ', '.join(str(age) for age in related['ages'] if age)
                    print(f"      {relationship}: {names}" + (f" (Age: {ages})" if ages else ""))
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"truthfinder_results_{first_name}_{last_name}_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"\n📁 Raw results saved to: {filename}")
        print(f"🎉 TruthFinder browser API search completed!")
        
    else:
        print("❌ No results found or API call failed")
        print("💡 This could be due to:")
        print("   - Person not found in TruthFinder database")
        print("   - Cloudflare still blocking the request")
        print("   - API endpoint changes")
        print("   - Network connectivity issues")

if __name__ == '__main__':
    asyncio.run(main())
